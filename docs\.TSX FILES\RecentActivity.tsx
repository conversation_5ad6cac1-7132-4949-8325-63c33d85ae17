
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileText, CheckCircle, AlertTriangle, Upload, User } from 'lucide-react'

interface ActivityItem {
  id: string
  type: 'document' | 'compliance' | 'review' | 'upload' | 'user'
  title: string
  description: string
  timestamp: string
  status: 'success' | 'warning' | 'info'
}

export function RecentActivity() {
  const [activities, setActivities] = useState<ActivityItem[]>([])

  useEffect(() => {
    // Simulate loading recent activities
    const timer = setTimeout(() => {
      setActivities([
        {
          id: '1',
          type: 'compliance',
          title: 'Compliance Review Completed',
          description: 'Project ABC-123 passed all compliance checks',
          timestamp: '2 minutes ago',
          status: 'success'
        },
        {
          id: '2',
          type: 'upload',
          title: 'Document Uploaded',
          description: 'Environmental assessment report uploaded',
          timestamp: '15 minutes ago',
          status: 'info'
        },
        {
          id: '3',
          type: 'review',
          title: 'Review Required',
          description: 'Project DEF-456 needs manual review',
          timestamp: '1 hour ago',
          status: 'warning'
        },
        {
          id: '4',
          type: 'user',
          title: 'New User Registered',
          description: '<PERSON> joined the platform',
          timestamp: '2 hours ago',
          status: 'info'
        },
        {
          id: '5',
          type: 'document',
          title: 'Document Processed',
          description: 'Cost estimate analysis completed',
          timestamp: '3 hours ago',
          status: 'success'
        }
      ])
    }, 800)

    return () => clearTimeout(timer)
  }, [])

  const getIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'document':
        return FileText
      case 'compliance':
        return CheckCircle
      case 'review':
        return AlertTriangle
      case 'upload':
        return Upload
      case 'user':
        return User
      default:
        return FileText
    }
  }

  const getStatusColor = (status: ActivityItem['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-400'
      case 'warning':
        return 'text-yellow-400'
      case 'info':
        return 'text-blue-400'
      default:
        return 'text-gray-400'
    }
  }

  return (
    <div className="space-y-4">
      {activities.length === 0 ? (
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-white/20 rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-3 bg-white/20 rounded w-3/4" />
                <div className="h-2 bg-white/10 rounded w-1/2" />
              </div>
            </div>
          ))}
        </div>
      ) : (
        activities.map((activity, index) => {
          const Icon = getIcon(activity.type)
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-start space-x-3 p-3 rounded-lg hover:bg-white/5 transition-colors"
            >
              <div className={`p-2 rounded-full bg-white/10 ${getStatusColor(activity.status)}`}>
                <Icon className="w-4 h-4" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {activity.title}
                </p>
                <p className="text-xs text-white/60 mt-1">
                  {activity.description}
                </p>
                <p className="text-xs text-white/40 mt-1">
                  {activity.timestamp}
                </p>
              </div>
            </motion.div>
          )
        })
      )}
    </div>
  )
}

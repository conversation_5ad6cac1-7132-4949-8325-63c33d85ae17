<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.8, maximum-scale=1.5">
    <title>Emergency Work - ComplianceMax V74</title>
    <style>
        /* ==========================================
           UNIVERSAL SCALING AND VIEWPORT FIX
           ========================================== */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px !important;
            zoom: 1.0 !important;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            color: white;
            zoom: 1.0 !important;
            transform: scale(1.0) !important;
            transform-origin: top left;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* ==========================================
           UNIVERSAL NAVIGATION HEADER
           ========================================== */
        .universal-nav {
            background: rgba(0, 0, 0, 0.95);
            height: 64px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .nav-brand {
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-brand:hover {
            color: #fbbf24;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .nav-link.active {
            background: rgba(239, 68, 68, 0.2);
            color: white;
            border-color: rgba(239, 68, 68, 0.5);
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 8px;
        }

        /* ==========================================
           MAIN CONTENT AREA
           ========================================== */
        .main-container {
            margin-top: 64px;
            padding: 40px 24px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
        }

        .page-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .page-title {
            font-size: 56px;
            font-weight: 900;
            margin-bottom: 16px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
            line-height: 1.1;
        }

        .page-subtitle {
            font-size: 22px;
            opacity: 0.95;
            margin-bottom: 32px;
            font-weight: 400;
        }

        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            margin-bottom: 32px;
            backdrop-filter: blur(5px);
        }

        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: white;
        }

        /* ==========================================
           EMERGENCY WORK CONTENT
           ========================================== */
        .emergency-content {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 32px;
            margin-bottom: 40px;
        }

        .category-card {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .category-card:hover::before {
            animation: shimmer 1.5s ease-in-out;
        }

        .category-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-8px) scale(1.02);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
        }

        .category-icon {
            font-size: 64px;
            margin-bottom: 24px;
            display: block;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
        }

        .category-title {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 16px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .category-description {
            font-size: 16px;
            opacity: 0.95;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .category-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        /* ==========================================
           INFORMATION SECTION
           ========================================== */
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            margin-top: 32px;
            backdrop-filter: blur(10px);
        }

        .info-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .info-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-item h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #fbbf24;
        }

        .info-item p {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.5;
        }

        /* ==========================================
           NOTIFICATION SYSTEM
           ========================================== */
        .notification-area {
            position: fixed;
            top: 84px;
            right: 24px;
            z-index: 1100;
            max-width: 420px;
            width: 100%;
        }

        .notification {
            background: rgba(255, 255, 255, 0.95);
            color: #1f2937;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success {
            border-left: 5px solid #10b981;
        }

        .notification.error {
            border-left: 5px solid #ef4444;
        }

        .notification.warning {
            border-left: 5px solid #f39c12;
        }

        .notification.info {
            border-left: 5px solid #3b82f6;
        }

        .notification-header {
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .notification-body {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* ==========================================
           RESPONSIVE DESIGN
           ========================================== */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(0, 0, 0, 0.95);
                flex-direction: column;
                padding: 24px;
                gap: 16px;
                backdrop-filter: blur(15px);
            }

            .nav-links.mobile-open {
                display: flex;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .main-container {
                padding: 24px 16px;
            }

            .page-title {
                font-size: 36px;
            }

            .page-subtitle {
                font-size: 18px;
            }

            .emergency-content {
                padding: 24px;
            }

            .categories-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .category-card {
                padding: 24px;
            }

            .category-icon {
                font-size: 48px;
            }

            .category-title {
                font-size: 24px;
            }

            .notification-area {
                left: 16px;
                right: 16px;
                max-width: none;
            }
        }

        @media (max-width: 480px) {
            .universal-nav {
                padding: 0 16px;
            }

            .nav-brand {
                font-size: 16px;
            }

            .main-container {
                padding: 16px 12px;
            }

            .page-title {
                font-size: 28px;
            }

            .emergency-content {
                padding: 20px;
            }
        }

        /* ==========================================
           ANIMATIONS
           ========================================== */
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .main-container > * {
            animation: fadeInUp 0.6s ease-out;
        }

        /* ==========================================
           PRINT STYLES
           ========================================== */
        @media print {
            .universal-nav,
            .notification-area {
                display: none !important;
            }

            .main-container {
                margin-top: 0 !important;
                padding: 0 !important;
                max-width: none !important;
            }

            body {
                background: white !important;
                color: black !important;
            }

            .emergency-content,
            .info-section,
            .category-card {
                background: white !important;
                color: black !important;
                border: 1px solid #ccc !important;
            }
        }
    </style>
</head>
<body>
    <!-- Universal Navigation -->
    <nav class="universal-nav">
        <a href="/" class="nav-brand">
            <span>🚨</span>
            <span>ComplianceMax V74</span>
        </a>
        
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">☰</button>
        
        <div class="nav-links" id="navLinks">
            <a href="/" class="nav-link">🏠 Home</a>
            <a href="/dashboard" class="nav-link">📊 Dashboard</a>
            <a href="/emergency" class="nav-link active">🚨 Emergency Work</a>
            <a href="/cbcs" class="nav-link">🏗️ CBCS Work</a>
            <a href="/professional-intake" class="nav-link">📋 Professional</a>
            <a href="/logout" class="nav-link">🚪 Logout</a>
        </div>
    </nav>

    <!-- Notification Area -->
    <div class="notification-area" id="notificationArea"></div>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="/">Home</a> › <a href="/dashboard">Dashboard</a> › Emergency Work
        </div>

        <!-- Page Header -->
        <header class="page-header">
            <h1 class="page-title">Emergency Work</h1>
            <p class="page-subtitle">Categories A & B - Debris Removal and Emergency Protective Measures</p>
        </header>

        <!-- Emergency Content -->
        <div class="emergency-content">
            <div class="categories-grid">
                <!-- Category A -->
                <div class="category-card" onclick="selectCategory('A')">
                    <span class="category-icon">🗑️</span>
                    <h3 class="category-title">Category A</h3>
                    <p class="category-description">
                        Debris removal from public roads, bridges, waterways, and other public facilities to ensure public safety and access.
                    </p>
                    <span class="category-badge">DEBRIS REMOVAL</span>
                </div>

                <!-- Category B -->
                <div class="category-card" onclick="selectCategory('B')">
                    <span class="category-icon">🛡️</span>
                    <h3 class="category-title">Category B</h3>
                    <p class="category-description">
                        Emergency protective measures taken to save lives, protect public health and safety, or protect improved property.
                    </p>
                    <span class="category-badge">PROTECTIVE MEASURES</span>
                </div>
            </div>

            <!-- Information Section -->
            <div class="info-section">
                <h3 class="info-title">
                    <span>ℹ️</span>
                    Emergency Work Requirements
                </h3>
                <div class="info-content">
                    <div class="info-item">
                        <h4>Immediate Threat</h4>
                        <p>Work must address an immediate threat to lives, public health and safety, or improved property.</p>
                    </div>
                    <div class="info-item">
                        <h4>Cost Effectiveness</h4>
                        <p>Emergency work must be the most cost-effective method to address the immediate threat.</p>
                    </div>
                    <div class="info-item">
                        <h4>Documentation</h4>
                        <p>Proper documentation of the threat and work performed is required for reimbursement.</p>
                    </div>
                    <div class="info-item">
                        <h4>Time Sensitivity</h4>
                        <p>Emergency work should be completed within the first 30 days after the disaster declaration.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin-top: 40px;">
                <p style="font-size: 18px; margin-bottom: 24px; opacity: 0.9;">
                    Select a category above to begin emergency work documentation
                </p>
                <p style="font-size: 15px; opacity: 0.8;">
                    For permanent infrastructure work (Categories C-G), please use the 
                    <a href="/cbcs" style="color: #fbbf24; text-decoration: underline; font-weight: 600;">CBCS Work module</a>
                </p>
            </div>
        </div>
    </main>

    <script>
        // ==========================================
        // NOTIFICATION SYSTEM
        // ==========================================
        function showNotification(message, type = 'info', duration = 5000) {
            const area = document.getElementById('notificationArea');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            
            notification.innerHTML = `
                <div class="notification-header">
                    <span>${icons[type] || icons.info}</span>
                    <span>${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                </div>
                <div class="notification-body">${message}</div>
            `;
            
            area.appendChild(notification);
            
            // Animate in
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Auto remove
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        area.removeChild(notification);
                    }
                }, 400);
            }, duration);
        }

        // ==========================================
        // NAVIGATION FUNCTIONS
        // ==========================================
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('mobile-open');
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const navLinks = document.getElementById('navLinks');
            const toggle = document.querySelector('.mobile-menu-toggle');
            
            if (!navLinks.contains(event.target) && !toggle.contains(event.target)) {
                navLinks.classList.remove('mobile-open');
            }
        });

        // ==========================================
        // CATEGORY SELECTION
        // ==========================================
        function selectCategory(category) {
            const categoryName = category === 'A' ? 'Debris Removal' : 'Emergency Protective Measures';
            
            showNotification(
                `Category ${category} (${categoryName}) selected. Redirecting to intake form...`,
                'success',
                3000
            );
            
            // Redirect to CBCS for now (as per your current routing)
            setTimeout(() => {
                window.location.href = '/cbcs';
            }, 2000);
        }

        // ==========================================
        // VIEWPORT AND SCALING MANAGEMENT
        // ==========================================
        function enforceViewportSettings() {
            // Force viewport meta tag
            let viewport = document.querySelector('meta[name=viewport]');
            if (!viewport) {
                viewport = document.createElement('meta');
                viewport.name = 'viewport';
                document.head.appendChild(viewport);
            }
            viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.8, maximum-scale=1.5';
            
            // Force zoom reset
            document.body.style.zoom = '1.0';
            document.documentElement.style.zoom = '1.0';
            
            // Remove any existing transform scaling
            document.body.style.transform = 'scale(1.0)';
            document.documentElement.style.transform = 'scale(1.0)';
        }

        // ==========================================
        // PAGE INITIALIZATION
        // ==========================================
        window.addEventListener('load', function() {
            // Enforce viewport settings
            enforceViewportSettings();
            
            // Show welcome notification
            setTimeout(() => {
                showNotification(
                    'Emergency Work module loaded successfully! Select a category to begin.',
                    'success',
                    4000
                );
            }, 1000);
        });

        // Re-enforce viewport on resize
        window.addEventListener('resize', function() {
            enforceViewportSettings();
        });

        // Prevent zoom on double-tap (mobile)
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html> 
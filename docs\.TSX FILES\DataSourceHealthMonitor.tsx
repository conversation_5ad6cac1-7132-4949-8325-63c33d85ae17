
'use client';

import { useState, useCallback, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Activity,
  TrendingUp,
  Wifi,
  AlertCircle
} from 'lucide-react';

import { useWebSocket } from './hooks/useWebSocket';
import { ConnectionStatus } from './components';

// Types
interface DataSourceMetrics {
  id: string;
  name: string;
  type: string;
  status: 'online' | 'offline' | 'error' | 'warning';
  lastFetch: Date;
  lastSuccess: Date;
  errorCount: number;
  recordCount: number;
  responseTime: number;
  uptime: number;
  errorRate: number;
  successRate: number;
  lastError?: string;
  configuration?: Record<string, any>;
}

interface HealthMetrics {
  totalSources: number;
  onlineSources: number;
  offlineSources: number;
  errorSources: number;
  avgResponseTime: number;
  totalRecords: number;
  totalErrors: number;
  overallHealth: number;
}

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  source_id?: string;
}

interface DataSourceHealthMonitorProps {
  clientId?: string;
  channel?: string;
  className?: string;
}

const DataSourceHealthMonitor: React.FC<DataSourceHealthMonitorProps> = ({
  clientId = 'health-monitor',
  channel = 'health',
  className = ''
}) => {
  // State management
  const [dataSources, setDataSources] = useState<Map<string, DataSourceMetrics>>(new Map());
  const [healthMetrics, setHealthMetrics] = useState<HealthMetrics>({
    totalSources: 0,
    onlineSources: 0,
    offlineSources: 0,
    errorSources: 0,
    avgResponseTime: 0,
    totalRecords: 0,
    totalErrors: 0,
    overallHealth: 0
  });
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // WebSocket configuration
  const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
  const wsUrl = `${WS_BASE_URL}/ws/health-monitor`;

  // Sample data for demonstration
  const initializeSampleData = useCallback(() => {
    const sampleSources = new Map<string, DataSourceMetrics>([
      ['epa-api', {
        id: 'epa-api',
        name: 'EPA Air Quality API',
        type: 'REST API',
        status: 'online',
        lastFetch: new Date(Date.now() - 2000),
        lastSuccess: new Date(Date.now() - 2000),
        errorCount: 0,
        recordCount: 15420,
        responseTime: 234,
        uptime: 99.8,
        errorRate: 0.2,
        successRate: 99.8
      }],
      ['fema-feeds', {
        id: 'fema-feeds',
        name: 'FEMA Disaster Feeds',
        type: 'RSS/XML',
        status: 'online',
        lastFetch: new Date(Date.now() - 5000),
        lastSuccess: new Date(Date.now() - 5000),
        errorCount: 2,
        recordCount: 8930,
        responseTime: 567,
        uptime: 98.5,
        errorRate: 1.5,
        successRate: 98.5
      }],
      ['security-alerts', {
        id: 'security-alerts',
        name: 'Security Alert System',
        type: 'WebSocket',
        status: 'warning',
        lastFetch: new Date(Date.now() - 30000),
        lastSuccess: new Date(Date.now() - 45000),
        errorCount: 8,
        recordCount: 3250,
        responseTime: 1200,
        uptime: 95.2,
        errorRate: 4.8,
        successRate: 95.2,
        lastError: 'Connection timeout after 30s'
      }],
      ['news-api', {
        id: 'news-api',
        name: 'News API Service',
        type: 'REST API',
        status: 'error',
        lastFetch: new Date(Date.now() - 120000),
        lastSuccess: new Date(Date.now() - 180000),
        errorCount: 15,
        recordCount: 12800,
        responseTime: 0,
        uptime: 87.3,
        errorRate: 12.7,
        successRate: 87.3,
        lastError: 'API rate limit exceeded'
      }],
      ['compliance-db', {
        id: 'compliance-db',
        name: 'Compliance Database',
        type: 'PostgreSQL',
        status: 'online',
        lastFetch: new Date(Date.now() - 1000),
        lastSuccess: new Date(Date.now() - 1000),
        errorCount: 0,
        recordCount: 45600,
        responseTime: 89,
        uptime: 99.9,
        errorRate: 0.1,
        successRate: 99.9
      }],
      ['weather-service', {
        id: 'weather-service',
        name: 'Weather Service API',
        type: 'REST API',
        status: 'offline',
        lastFetch: new Date(Date.now() - 300000),
        lastSuccess: new Date(Date.now() - 600000),
        errorCount: 25,
        recordCount: 78900,
        responseTime: 0,
        uptime: 78.5,
        errorRate: 21.5,
        successRate: 78.5,
        lastError: 'Service unavailable - maintenance mode'
      }]
    ]);

    setDataSources(sampleSources);
    calculateHealthMetrics(sampleSources);
  }, []);

  // Calculate overall health metrics
  const calculateHealthMetrics = useCallback((sources: Map<string, DataSourceMetrics>) => {
    const sourceArray = Array.from(sources.values());
    const totalSources = sourceArray.length;
    const onlineSources = sourceArray.filter(s => s.status === 'online').length;
    const offlineSources = sourceArray.filter(s => s.status === 'offline').length;
    const errorSources = sourceArray.filter(s => s.status === 'error').length;
    const avgResponseTime = sourceArray.reduce((sum, s) => sum + s.responseTime, 0) / totalSources;
    const totalRecords = sourceArray.reduce((sum, s) => sum + s.recordCount, 0);
    const totalErrors = sourceArray.reduce((sum, s) => sum + s.errorCount, 0);
    const overallHealth = sourceArray.reduce((sum, s) => sum + s.uptime, 0) / totalSources;

    setHealthMetrics({
      totalSources,
      onlineSources,
      offlineSources,
      errorSources,
      avgResponseTime,
      totalRecords,
      totalErrors,
      overallHealth
    });
  }, []);

  // WebSocket message handler
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      if (message.type === 'health_update' && message.source_id) {
        setDataSources(prev => {
          const updated = new Map(prev);
          const existing = updated.get(message.source_id!);
          if (existing) {
            updated.set(message.source_id!, {
              ...existing,
              ...message.data,
              lastFetch: new Date(message.timestamp)
            });
          }
          return updated;
        });
        setLastUpdate(new Date());
      } else if (message.type === 'health_metrics') {
        setHealthMetrics(message.data);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }, []);

  // WebSocket connection handlers
  const handleOpen = useCallback(() => {
    console.log('Health monitor WebSocket connected');
  }, []);

  const handleClose = useCallback(() => {
    console.log('Health monitor WebSocket disconnected');
  }, []);

  const handleError = useCallback((error: Event) => {
    console.error('Health monitor WebSocket error:', error);
  }, []);

  // Use WebSocket hook
  const {
    connect,
    disconnect,
    readyState,
    isConnected,
    isConnecting,
    reconnectAttempts,
    sendMessage
  } = useWebSocket({
    url: wsUrl,
    onOpen: handleOpen,
    onMessage: handleMessage,
    onClose: handleClose,
    onError: handleError,
    reconnectAttempts: 5,
    reconnectDelay: [1000, 2000, 5000, 10000, 30000]
  });

  // Refresh data
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    
    // Simulate data refresh
    setTimeout(() => {
      setDataSources(prev => {
        const updated = new Map(prev);
        updated.forEach((source, key) => {
          updated.set(key, {
            ...source,
            lastFetch: new Date(),
            responseTime: Math.floor(Math.random() * 1000) + 100
          });
        });
        return updated;
      });
      setLastUpdate(new Date());
      setIsRefreshing(false);
    }, 1000);

    // Send refresh request via WebSocket if connected
    if (isConnected) {
      sendMessage({
        type: 'refresh_health',
        timestamp: new Date().toISOString()
      });
    }
  }, [isConnected, sendMessage]);

  // Initialize sample data on component mount
  useEffect(() => {
    initializeSampleData();
  }, [initializeSampleData]);

  // Recalculate metrics when data sources change
  useEffect(() => {
    calculateHealthMetrics(dataSources);
  }, [dataSources, calculateHealthMetrics]);

  // Get connection status
  const getConnectionStatus = () => {
    if (isConnected) return 'connected';
    if (isConnecting) return 'connecting';
    if (reconnectAttempts > 0) return 'reconnecting';
    return 'offline';
  };

  // Format timestamp
  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // Get status icon and color
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'online':
        return { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-100' };
      case 'warning':
        return { icon: AlertTriangle, color: 'text-yellow-500', bg: 'bg-yellow-100' };
      case 'error':
        return { icon: AlertCircle, color: 'text-red-500', bg: 'bg-red-100' };
      case 'offline':
      default:
        return { icon: XCircle, color: 'text-gray-500', bg: 'bg-gray-100' };
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Activity className="h-6 w-6 text-blue-500" />
              <div>
                <CardTitle>Health Monitor Status</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Last updated: {formatTimestamp(lastUpdate)}
                </p>
              </div>
            </div>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="sm"
              disabled={isRefreshing}
              className="min-w-[100px]"
            >
              {isRefreshing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Refreshing
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </>
              )}
            </Button>
          </div>
          <ConnectionStatus
            status={getConnectionStatus()}
            reconnectAttempts={reconnectAttempts}
            maxReconnectAttempts={5}
            onConnect={connect}
            onDisconnect={disconnect}
            showControls={true}
          />
        </CardHeader>
      </Card>

      {/* Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overall Health</p>
                <p className="text-3xl font-bold text-green-600">{healthMetrics.overallHealth.toFixed(1)}%</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
              {healthMetrics.onlineSources}/{healthMetrics.totalSources} sources online
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                <p className="text-3xl font-bold text-blue-600">{Math.round(healthMetrics.avgResponseTime)}ms</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <Activity className="h-4 w-4 mr-1 text-blue-500" />
              Real-time monitoring
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Records</p>
                <p className="text-3xl font-bold text-purple-600">{formatNumber(healthMetrics.totalRecords)}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Database className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <TrendingUp className="h-4 w-4 mr-1 text-purple-500" />
              Across all sources
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Errors</p>
                <p className="text-3xl font-bold text-red-600">{formatNumber(healthMetrics.totalErrors)}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <XCircle className="h-4 w-4 mr-1 text-red-500" />
              {healthMetrics.errorSources} sources with errors
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Sources Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from(dataSources.values()).map((source) => {
          const statusConfig = getStatusConfig(source.status);
          const StatusIcon = statusConfig.icon;

          return (
            <Card key={source.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${statusConfig.bg}`}>
                      <StatusIcon className={`h-5 w-5 ${statusConfig.color}`} />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{source.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{source.type}</p>
                    </div>
                  </div>
                  <Badge 
                    className={`${statusConfig.color} ${statusConfig.bg} border-0`}
                  >
                    {source.status.toUpperCase()}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Metrics Grid */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Records</p>
                    <p className="text-2xl font-bold">{formatNumber(source.recordCount)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Errors</p>
                    <p className="text-2xl font-bold text-red-600">{source.errorCount}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Response Time</p>
                    <p className="text-2xl font-bold">{source.responseTime}ms</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-muted-foreground">Uptime</p>
                    <p className="text-2xl font-bold text-green-600">{source.uptime}%</p>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="flex items-center justify-between text-sm text-muted-foreground border-t pt-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>Last fetch: {formatTimestamp(source.lastFetch)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Success: {formatTimestamp(source.lastSuccess)}</span>
                  </div>
                </div>

                {/* Error Message */}
                {source.lastError && (
                  <div className="flex items-start gap-2 p-3 bg-red-50 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-red-800">Last Error</p>
                      <p className="text-sm text-red-600">{source.lastError}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default DataSourceHealthMonitor;

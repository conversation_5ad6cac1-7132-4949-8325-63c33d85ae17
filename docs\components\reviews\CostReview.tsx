import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Chip,
  Box,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { useReview } from '../../hooks/useReview';

interface CostReviewProps {
  projectId: number;
}

interface CostItem {
  category: string;
  description: string;
  amount: number;
  justification: string;
}

interface Alternative {
  description: string;
  estimatedCost: number;
  pros: string[];
  cons: string[];
}

export const CostReview: React.FC<CostReviewProps> = ({ projectId }) => {
  const { createReview, reviews, loading } = useReview(projectId);
  const [costBreakdown, setCostBreakdown] = useState<CostItem[]>([]);
  const [alternatives, setAlternatives] = useState<Alternative[]>([]);
  const [newCostItem, setNewCostItem] = useState<CostItem>({
    category: '',
    description: '',
    amount: 0,
    justification: ''
  });
  const [newAlternative, setNewAlternative] = useState<Alternative>({
    description: '',
    estimatedCost: 0,
    pros: [],
    cons: []
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const totalCost = costBreakdown.reduce((sum, item) => sum + item.amount, 0);
      await createReview('cost', {
        total_cost: totalCost,
        cost_breakdown: costBreakdown,
        alternatives: alternatives
      });
      // Reset form
      setCostBreakdown([]);
      setAlternatives([]);
      setNewCostItem({
        category: '',
        description: '',
        amount: 0,
        justification: ''
      });
      setNewAlternative({
        description: '',
        estimatedCost: 0,
        pros: [],
        cons: []
      });
    } catch (error) {
      console.error('Error creating cost review:', error);
    }
  };

  const addCostItem = () => {
    if (newCostItem.category && newCostItem.amount > 0) {
      setCostBreakdown([...costBreakdown, newCostItem]);
      setNewCostItem({
        category: '',
        description: '',
        amount: 0,
        justification: ''
      });
    }
  };

  const removeCostItem = (index: number) => {
    setCostBreakdown(costBreakdown.filter((_, i) => i !== index));
  };

  const addAlternative = () => {
    if (newAlternative.description && newAlternative.estimatedCost > 0) {
      setAlternatives([...alternatives, newAlternative]);
      setNewAlternative({
        description: '',
        estimatedCost: 0,
        pros: [],
        cons: []
      });
    }
  };

  const removeAlternative = (index: number) => {
    setAlternatives(alternatives.filter((_, i) => i !== index));
  };

  const costReviews = reviews.filter(review => review.type === 'cost');

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Create Cost Review
            </Typography>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Cost Breakdown Section */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Cost Breakdown
                  </Typography>
                  <Paper sx={{ p: 2, mb: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={3}>
                        <TextField
                          fullWidth
                          label="Category"
                          value={newCostItem.category}
                          onChange={(e) => setNewCostItem({
                            ...newCostItem,
                            category: e.target.value
                          })}
                        />
                      </Grid>
                      <Grid item xs={12} md={3}>
                        <TextField
                          fullWidth
                          label="Description"
                          value={newCostItem.description}
                          onChange={(e) => setNewCostItem({
                            ...newCostItem,
                            description: e.target.value
                          })}
                        />
                      </Grid>
                      <Grid item xs={12} md={2}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Amount"
                          value={newCostItem.amount}
                          onChange={(e) => setNewCostItem({
                            ...newCostItem,
                            amount: parseFloat(e.target.value)
                          })}
                        />
                      </Grid>
                      <Grid item xs={12} md={3}>
                        <TextField
                          fullWidth
                          label="Justification"
                          value={newCostItem.justification}
                          onChange={(e) => setNewCostItem({
                            ...newCostItem,
                            justification: e.target.value
                          })}
                        />
                      </Grid>
                      <Grid item xs={12} md={1}>
                        <IconButton
                          color="primary"
                          onClick={addCostItem}
                          disabled={!newCostItem.category || newCostItem.amount <= 0}
                        >
                          <AddIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </Paper>

                  {costBreakdown.length > 0 && (
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Category</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell align="right">Amount</TableCell>
                            <TableCell>Justification</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {costBreakdown.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell>{item.category}</TableCell>
                              <TableCell>{item.description}</TableCell>
                              <TableCell align="right">${item.amount.toFixed(2)}</TableCell>
                              <TableCell>{item.justification}</TableCell>
                              <TableCell>
                                <IconButton
                                  size="small"
                                  onClick={() => removeCostItem(index)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                          <TableRow>
                            <TableCell colSpan={2}>Total</TableCell>
                            <TableCell align="right">
                              ${costBreakdown.reduce((sum, item) => sum + item.amount, 0).toFixed(2)}
                            </TableCell>
                            <TableCell colSpan={2} />
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </Grid>

                {/* Alternatives Section */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Alternatives
                  </Typography>
                  <Paper sx={{ p: 2, mb: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Description"
                          value={newAlternative.description}
                          onChange={(e) => setNewAlternative({
                            ...newAlternative,
                            description: e.target.value
                          })}
                        />
                      </Grid>
                      <Grid item xs={12} md={5}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Estimated Cost"
                          value={newAlternative.estimatedCost}
                          onChange={(e) => setNewAlternative({
                            ...newAlternative,
                            estimatedCost: parseFloat(e.target.value)
                          })}
                        />
                      </Grid>
                      <Grid item xs={12} md={1}>
                        <IconButton
                          color="primary"
                          onClick={addAlternative}
                          disabled={!newAlternative.description || newAlternative.estimatedCost <= 0}
                        >
                          <AddIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </Paper>

                  {alternatives.map((alt, index) => (
                    <Paper key={index} sx={{ p: 2, mb: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="subtitle2">
                              Alternative {index + 1}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => removeAlternative(index)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography>
                            Description: {alt.description}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography>
                            Estimated Cost: ${alt.estimatedCost.toFixed(2)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Paper>
                  ))}
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={loading || costBreakdown.length === 0}
                  >
                    Create Review
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Grid>

      {costReviews.length > 0 && (
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Existing Cost Reviews
              </Typography>
              <List>
                {costReviews.map((review) => (
                  <ListItem key={review.id} divider>
                    <ListItemText
                      primary={`Total Cost: $${review.total_cost}`}
                      secondary={
                        <>
                          <Typography component="span" variant="body2">
                            Items: {review.cost_breakdown?.length || 0}
                          </Typography>
                          <br />
                          <Typography component="span" variant="body2">
                            Alternatives: {review.alternatives?.length || 0}
                          </Typography>
                          <br />
                          <Typography component="span" variant="body2">
                            Created: {new Date(review.created_at).toLocaleDateString()}
                          </Typography>
                        </>
                      }
                    />
                    <Chip
                      label={review.status}
                      color={
                        review.status === 'completed' ? 'success' :
                        review.status === 'rejected' ? 'error' :
                        review.status === 'in_progress' ? 'warning' : 'default'
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      )}
    </Grid>
  );
};

import React, { useState } from 'react';
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Divider,
} from '@mui/material';
import { ActionType } from '../../types/workflow';

interface ActionConfigEditorProps {
  actionType: ActionType;
  config: any;
  onUpdate: (config: any) => void;
}

const ActionConfigEditor: React.FC<ActionConfigEditorProps> = ({
  actionType,
  config,
  onUpdate,
}) => {
  const [localConfig, setLocalConfig] = useState(config);

  const handleConfigChange = (field: string, value: any) => {
    const newConfig = { ...localConfig, [field]: value };
    setLocalConfig(newConfig);
    onUpdate(newConfig);
  };

  const renderHttpConfig = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="URL"
          value={localConfig.url || ''}
          onChange={(e) => handleConfigChange('url', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Method</InputLabel>
          <Select
            value={localConfig.method || 'GET'}
            onChange={(e) => handleConfigChange('method', e.target.value)}
          >
            <MenuItem value="GET">GET</MenuItem>
            <MenuItem value="POST">POST</MenuItem>
            <MenuItem value="PUT">PUT</MenuItem>
            <MenuItem value="DELETE">DELETE</MenuItem>
            <MenuItem value="PATCH">PATCH</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Headers"
          multiline
          rows={3}
          value={localConfig.headers || ''}
          onChange={(e) => handleConfigChange('headers', e.target.value)}
          helperText="Enter headers as JSON object"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Body"
          multiline
          rows={4}
          value={localConfig.body || ''}
          onChange={(e) => handleConfigChange('body', e.target.value)}
          helperText="Enter request body as JSON"
        />
      </Grid>
    </Grid>
  );

  const renderEmailConfig = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="To"
          value={localConfig.to || ''}
          onChange={(e) => handleConfigChange('to', e.target.value)}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Subject"
          value={localConfig.subject || ''}
          onChange={(e) => handleConfigChange('subject', e.target.value)}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Body"
          multiline
          rows={4}
          value={localConfig.body || ''}
          onChange={(e) => handleConfigChange('body', e.target.value)}
        />
      </Grid>
    </Grid>
  );

  const renderDatabaseConfig = () => (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Database Type</InputLabel>
          <Select
            value={localConfig.databaseType || 'mongodb'}
            onChange={(e) => handleConfigChange('databaseType', e.target.value)}
          >
            <MenuItem value="mongodb">MongoDB</MenuItem>
            <MenuItem value="sql">SQL</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Operation</InputLabel>
          <Select
            value={localConfig.operation || 'find'}
            onChange={(e) => handleConfigChange('operation', e.target.value)}
          >
            <MenuItem value="find">Find</MenuItem>
            <MenuItem value="insert">Insert</MenuItem>
            <MenuItem value="update">Update</MenuItem>
            <MenuItem value="delete">Delete</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Query"
          multiline
          rows={4}
          value={localConfig.query || ''}
          onChange={(e) => handleConfigChange('query', e.target.value)}
          helperText="Enter query as JSON for MongoDB or SQL query for SQL"
        />
      </Grid>
    </Grid>
  );

  const renderFileConfig = () => (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Operation</InputLabel>
          <Select
            value={localConfig.operation || 'read'}
            onChange={(e) => handleConfigChange('operation', e.target.value)}
          >
            <MenuItem value="read">Read</MenuItem>
            <MenuItem value="write">Write</MenuItem>
            <MenuItem value="delete">Delete</MenuItem>
            <MenuItem value="move">Move</MenuItem>
            <MenuItem value="copy">Copy</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Source Path"
          value={localConfig.sourcePath || ''}
          onChange={(e) => handleConfigChange('sourcePath', e.target.value)}
        />
      </Grid>
      {['move', 'copy'].includes(localConfig.operation) && (
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Destination Path"
            value={localConfig.destinationPath || ''}
            onChange={(e) => handleConfigChange('destinationPath', e.target.value)}
          />
        </Grid>
      )}
    </Grid>
  );

  const renderNotificationConfig = () => (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Type</InputLabel>
          <Select
            value={localConfig.type || 'system'}
            onChange={(e) => handleConfigChange('type', e.target.value)}
          >
            <MenuItem value="system">System</MenuItem>
            <MenuItem value="slack">Slack</MenuItem>
            <MenuItem value="teams">Teams</MenuItem>
            <MenuItem value="webhook">Webhook</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Message"
          multiline
          rows={4}
          value={localConfig.message || ''}
          onChange={(e) => handleConfigChange('message', e.target.value)}
        />
      </Grid>
      {['slack', 'teams', 'webhook'].includes(localConfig.type) && (
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Webhook URL"
            value={localConfig.webhookUrl || ''}
            onChange={(e) => handleConfigChange('webhookUrl', e.target.value)}
          />
        </Grid>
      )}
    </Grid>
  );

  const renderConfig = () => {
    switch (actionType) {
      case ActionType.HTTP:
        return renderHttpConfig();
      case ActionType.EMAIL:
        return renderEmailConfig();
      case ActionType.DATABASE:
        return renderDatabaseConfig();
      case ActionType.FILE:
        return renderFileConfig();
      case ActionType.NOTIFICATION:
        return renderNotificationConfig();
      default:
        return (
          <Typography color="textSecondary">
            No configuration required for this action type.
          </Typography>
        );
    }
  };

  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom>
        Action Configuration
      </Typography>
      <Divider sx={{ mb: 2 }} />
      {renderConfig()}
    </Box>
  );
};

export default ActionConfigEditor; 
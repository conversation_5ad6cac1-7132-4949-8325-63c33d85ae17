import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  CircularProgress, 
  Container, 
  Divider, 
  FormControlLabel,
  Grid, 
  Paper, 
  Switch, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import { 
  CloudUpload as CloudUploadIcon,
  Assessment as AssessmentIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import axios from 'axios';

// Components
import FileUploader from '../common/FileUploader';
import PolicySelector from '../policy/PolicySelector';
import MatchingResults from './MatchingResults';
import MatchingReport from './MatchingReport';

// Types
interface PolicyMatcherProps {
  projectId?: number;
}

interface MatchingJob {
  job_id: string;
  status: 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  report_url?: string;
  error_message?: string;
}

const PolicyMatcher: React.FC<PolicyMatcherProps> = ({ projectId }) => {
  // State
  const [requirementsFile, setRequirementsFile] = useState<File | null>(null);
  const [selectedPolicyIds, setSelectedPolicyIds] = useState<number[]>([]);
  const [useCache, setUseCache] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [currentJob, setCurrentJob] = useState<MatchingJob | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);
  const [matchingResults, setMatchingResults] = useState<any>(null);
  const [reportHtml, setReportHtml] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Check for existing job on component mount
  useEffect(() => {
    const checkExistingJob = async () => {
      try {
        const response = await axios.get('/api/v1/policy-matcher/recent-job');
        if (response.data && response.data.job_id) {
          setCurrentJob(response.data);
          pollJobStatus(response.data.job_id);
        }
      } catch (error) {
        console.error('Error checking for existing job:', error);
      }
    };

    checkExistingJob();
  }, []);

  // Poll job status
  const pollJobStatus = (jobId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await axios.get(`/api/v1/policy-matcher/status/${jobId}`);
        setCurrentJob(response.data);

        if (response.data.status === 'completed' || response.data.status === 'failed') {
          clearInterval(interval);
          
          if (response.data.status === 'completed') {
            fetchResults(jobId);
            fetchReport(jobId);
          }
        }
      } catch (error) {
        console.error('Error polling job status:', error);
        clearInterval(interval);
      }
    }, 3000);

    return () => clearInterval(interval);
  };

  // Fetch matching results
  const fetchResults = async (jobId: string) => {
    try {
      const response = await axios.get(`/api/v1/policy-matcher/results/${jobId}`);
      setMatchingResults(response.data.results);
    } catch (error) {
      console.error('Error fetching results:', error);
    }
  };

  // Fetch HTML report
  const fetchReport = async (jobId: string) => {
    try {
      const response = await axios.get(`/api/v1/policy-matcher/report/${jobId}`);
      setReportHtml(response.data);
    } catch (error) {
      console.error('Error fetching report:', error);
    }
  };

  // Handle file upload
  const handleFileChange = (file: File | null) => {
    setRequirementsFile(file);
    setErrorMessage('');
  };

  // Handle policy selection
  const handlePolicySelection = (policyIds: number[]) => {
    setSelectedPolicyIds(policyIds);
    setErrorMessage('');
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!requirementsFile) {
      setErrorMessage('Please upload a requirements document');
      return;
    }

    if (selectedPolicyIds.length === 0) {
      setErrorMessage('Please select at least one policy document');
      return;
    }

    setIsSubmitting(true);
    setErrorMessage('');

    try {
      const formData = new FormData();
      formData.append('requirements_file', requirementsFile);
      selectedPolicyIds.forEach(id => formData.append('policy_ids', id.toString()));
      formData.append('use_cache', useCache.toString());
      
      if (projectId) {
        formData.append('project_id', projectId.toString());
      }

      const response = await axios.post('/api/v1/policy-matcher/match', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setCurrentJob({
        job_id: response.data.job_id,
        status: 'processing',
        created_at: new Date().toISOString()
      });

      pollJobStatus(response.data.job_id);
    } catch (error) {
      console.error('Error submitting policy matching job:', error);
      setErrorMessage('Error submitting policy matching job. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle job deletion
  const handleDeleteJob = async () => {
    if (!currentJob) return;

    try {
      await axios.delete(`/api/v1/policy-matcher/jobs/${currentJob.job_id}`);
      setCurrentJob(null);
      setMatchingResults(null);
      setReportHtml('');
    } catch (error) {
      console.error('Error deleting job:', error);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Policy Matcher
        </Typography>
        <Typography variant="body1" paragraph>
          Upload a requirements document to match against policy documents and identify relevant policies for compliance.
        </Typography>

        {currentJob && currentJob.status === 'processing' ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CircularProgress />
            <Typography variant="h6" sx={{ mt: 2 }}>
              Processing Policy Matching
            </Typography>
            <Typography variant="body2" color="textSecondary">
              This may take several minutes depending on the number of documents.
            </Typography>
          </Box>
        ) : currentJob && (currentJob.status === 'completed' || currentJob.status === 'failed') ? (
          <Box>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label="Results" icon={<AssessmentIcon />} />
                <Tab label="Report" />
              </Tabs>
            </Box>

            {tabValue === 0 && (
              <MatchingResults results={matchingResults} />
            )}

            {tabValue === 1 && (
              <MatchingReport htmlContent={reportHtml} />
            )}

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleDeleteJob}
              >
                Delete Results
              </Button>
            </Box>
          </Box>
        ) : (
          <Box>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Upload Requirements Document
                </Typography>
                <Box sx={{
                  border: '2px dashed #ccc',
                  borderRadius: 2,
                  p: 3,
                  textAlign: 'center',
                  backgroundColor: '#f9f9f9'
                }}>
                  <Typography variant="body1" gutterBottom>
                    Use Master Document Upload System
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Enhanced analysis with pod routing and compliance validation
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => {
                      const currentUrl = encodeURIComponent(window.location.href);
                      window.location.href = `document_upload_system.html?return=${currentUrl}&context=policy-matcher`;
                    }}
                  >
                    📄 Open Upload System
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Select Policy Documents
                </Typography>
                <PolicySelector onSelectionChange={handlePolicySelection} />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={useCache}
                    onChange={(e) => setUseCache(e.target.checked)}
                  />
                }
                label="Use document cache (faster processing)"
              />
            </Box>

            {errorMessage && (
              <Typography color="error" sx={{ mt: 2 }}>
                {errorMessage}
              </Typography>
            )}

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CloudUploadIcon />}
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Processing...' : 'Match Policies'}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default PolicyMatcher;

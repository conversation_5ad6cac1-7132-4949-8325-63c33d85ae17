import { parseJsonSchemaToFormSteps } from './jsonFormParser';
import { fuzz } from 'fuzzer';

const validInput = { version: '2.0', entries: [
  {
    process_phase: "Test Phase",
    "step/requirement": "Test Requirement",
    documentation_required: "test"
  }
]};

describe('Fuzz Testing', () => {
  const FUZZ_ITERATIONS = 1000;

  it('should handle malformed inputs without crashing', () => {
    Array(FUZZ_ITERATIONS).fill(0).forEach(() => {
      const fuzzed = fuzz(validInput, {
        strategies: ['truncate', 'corrupt', 'overflow'],
      });
      expect(() => parseJsonSchemaToFormSteps([fuzzed])).not.toThrow();
    });
  });
}); 
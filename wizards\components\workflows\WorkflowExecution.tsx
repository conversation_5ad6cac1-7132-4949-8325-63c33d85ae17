import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { api } from '../../services/api';
import { Workflow, WorkflowExecution as WorkflowExecutionType, StepExecution } from '../../types/workflow';

interface WorkflowExecutionProps {
  workflow: Workflow;
  onExecutionComplete?: (execution: WorkflowExecutionType) => void;
}

const WorkflowExecution: React.FC<WorkflowExecutionProps> = ({
  workflow,
  onExecutionComplete,
}) => {
  const [execution, setExecution] = useState<WorkflowExecutionType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const fetchExecutionStatus = async () => {
    if (!execution) return;
    
    try {
      const response = await api.get(`/workflows/${workflow.id}/executions/${execution.id}`);
      const updatedExecution = response.data;
      setExecution(updatedExecution);
      
      if (updatedExecution.status === 'completed' || updatedExecution.status === 'failed') {
        setAutoRefresh(false);
        onExecutionComplete?.(updatedExecution);
      }
    } catch (err) {
      setError('Failed to fetch execution status');
    }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (autoRefresh && execution) {
      interval = setInterval(fetchExecutionStatus, 2000);
    }
    return () => clearInterval(interval);
  }, [autoRefresh, execution]);

  const startExecution = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await api.post(`/workflows/${workflow.id}/execute`);
      setExecution(response.data);
      setAutoRefresh(true);
    } catch (err) {
      setError('Failed to start workflow execution');
    } finally {
      setIsLoading(false);
    }
  };

  const stopExecution = async () => {
    if (!execution) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await api.post(`/workflows/${workflow.id}/executions/${execution.id}/stop`);
      setAutoRefresh(true);
    } catch (err) {
      setError('Failed to stop workflow execution');
    } finally {
      setIsLoading(false);
    }
  };

  const getStepIcon = (step: StepExecution) => {
    switch (step.status) {
      case 'completed':
        return <SuccessIcon color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      case 'running':
        return <CircularProgress size={20} />;
      case 'pending':
        return <WarningIcon color="warning" />;
      default:
        return null;
    }
  };

  const getStepStatusText = (step: StepExecution) => {
    if (step.error) {
      return `Error: ${step.error}`;
    }
    return step.status.charAt(0).toUpperCase() + step.status.slice(1);
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Workflow Execution</Typography>
        <Box>
          {execution?.status === 'running' ? (
            <Button
              variant="contained"
              color="error"
              startIcon={<StopIcon />}
              onClick={stopExecution}
              disabled={isLoading}
            >
              Stop Execution
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayIcon />}
              onClick={startExecution}
              disabled={isLoading || !!execution}
            >
              Start Execution
            </Button>
          )}
          {execution && (
            <Tooltip title={autoRefresh ? 'Auto-refresh enabled' : 'Auto-refresh disabled'}>
              <IconButton
                onClick={() => setAutoRefresh(!autoRefresh)}
                color={autoRefresh ? 'primary' : 'default'}
                sx={{ ml: 1 }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {execution && (
        <>
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1">Execution Status</Typography>
            <Typography variant="body2" color="textSecondary">
              Started: {new Date(execution.started_at).toLocaleString()}
              {execution.completed_at && ` | Completed: ${new Date(execution.completed_at).toLocaleString()}`}
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle1" gutterBottom>
            Step Progress
          </Typography>
          <List>
            {execution.steps.map((step, index) => (
              <ListItem key={step.id}>
                <ListItemIcon>{getStepIcon(step)}</ListItemIcon>
                <ListItemText
                  primary={`${index + 1}. ${step.name}`}
                  secondary={getStepStatusText(step)}
                />
              </ListItem>
            ))}
          </List>
        </>
      )}

      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <CircularProgress />
        </Box>
      )}
    </Paper>
  );
};

export default WorkflowExecution; 
{% extends "base_responsive.html" %}

{% block title %}CBCS Work (C-G) - ComplianceMax V74{% endblock %}

{% block page_title %}CBCS Permanent Work (Categories C-G){% endblock %}

{% block page_subtitle %}Roads, Buildings, Utilities & Infrastructure{% endblock %}

{% block content %}
<div class="cbcs-section">
    <div class="cbcs-header">
        🏗️ CBCS Permanent Work Intake
    </div>
    
    <form class="grid" id="cbcsForm" onsubmit="handleCbcsSubmit(event)">
        <div class="grid grid-2">
            <div class="form-group">
                <label for="applicant-name">Applicant Name *</label>
                <input type="text" id="applicant-name" name="applicant_name" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="disaster-number">Disaster Number *</label>
                <input type="text" id="disaster-number" name="disaster_number" class="form-control" 
                       placeholder="e.g., DR-4xxx-XX" required>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="form-group">
                <label for="category">CBCS Category *</label>
                <select id="category" name="category" class="form-control" required onchange="updateCategoryInfo()">
                    <option value="">Select Category</option>
                    <option value="C">Category C - Roads and Bridges</option>
                    <option value="D">Category D - Water Control Facilities</option>
                    <option value="E">Category E - Public Buildings</option>
                    <option value="F">Category F - Public Utilities</option>
                    <option value="G">Category G - Parks and Recreation</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="facility-type">Facility Type *</label>
                <select id="facility-type" name="facility_type" class="form-control" required>
                    <option value="">Select Type</option>
                    <option value="public">Public</option>
                    <option value="pnp">Private Non-Profit</option>
                </select>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="form-group">
                <label for="project-cost">Project Cost *</label>
                <input type="number" id="project-cost" name="project_cost" class="form-control" 
                       min="0" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="insurance-coverage">Insurance Coverage</label>
                <select id="insurance-coverage" name="insurance_coverage" class="form-control">
                    <option value="">Unknown</option>
                    <option value="yes">Yes - Have Insurance</option>
                    <option value="no">No Insurance</option>
                    <option value="partial">Partial Coverage</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label for="facility-description">Facility/Project Description *</label>
            <textarea id="facility-description" name="facility_description" class="form-control" 
                      rows="3" required placeholder="Describe the damaged facility or project..."></textarea>
        </div>

        <div class="grid grid-2">
            <div class="form-group">
                <label for="damage-date">Damage Date *</label>
                <input type="date" id="damage-date" name="damage_date" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="repair-method">Repair Method</label>
                <select id="repair-method" name="repair_method" class="form-control">
                    <option value="in-kind">In-Kind Restoration</option>
                    <option value="improved">Improved Project</option>
                    <option value="alternate">Alternate Project</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-block">Generate CBCS Documentation</button>
        </div>
    </form>
</div>

<!-- Category Information Panel -->
<div id="category-info" class="cbcs-section" style="display: none;">
    <div class="cbcs-header">
        📋 Category Requirements
    </div>
    <div id="category-details">
        <!-- Dynamic content will be loaded here -->
    </div>
</div>

<!-- CBCS Code Panel -->
<div id="cbcs-code-panel" class="cbcs-section" style="display: none;">
    <div class="cbcs-header">
        🔢 CBCS Code Assignment
    </div>
    <div id="cbcs-code-content">
        <!-- CBCS codes will be displayed here -->
    </div>
</div>

<!-- Results Panel -->
<div id="results-panel" class="cbcs-section" style="display: none;">
    <div class="cbcs-header">
        📄 Generated Documentation
    </div>
    <div id="results-content">
        <!-- Results will be displayed here -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateCategoryInfo() {
    const category = document.getElementById('category').value;
    const infoPanel = document.getElementById('category-info');
    const detailsDiv = document.getElementById('category-details');
    
    if (category) {
        infoPanel.style.display = 'block';
        
        const categoryInfo = {
            'C': {
                title: 'Category C - Roads and Bridges',
                requirements: [
                    'Engineering assessment required',
                    'Traffic impact analysis',
                    'Environmental compliance review',
                    'Design standards documentation',
                    'Cost-benefit analysis for improvements'
                ]
            },
            'D': {
                title: 'Category D - Water Control Facilities',
                requirements: [
                    'Hydraulic engineering review',
                    'Environmental impact assessment',
                    'Permit requirements verification',
                    'Flood control effectiveness analysis',
                    'Long-term maintenance planning'
                ]
            },
            'E': {
                title: 'Category E - Public Buildings',
                requirements: [
                    'Building code compliance verification',
                    'ADA accessibility requirements',
                    'Life safety code compliance',
                    'Seismic/wind load considerations',
                    'Energy efficiency standards'
                ]
            },
            'F': {
                title: 'Category F - Public Utilities',
                requirements: [
                    'Utility system integration',
                    'Service restoration priority',
                    'Redundancy and reliability',
                    'Environmental considerations',
                    'Regulatory compliance verification'
                ]
            },
            'G': {
                title: 'Category G - Parks and Recreation',
                requirements: [
                    'Public use accessibility',
                    'Environmental restoration',
                    'Safety and liability considerations',
                    'Community benefit analysis',
                    'Maintenance and operation planning'
                ]
            }
        };
        
        const info = categoryInfo[category];
        if (info) {
            detailsDiv.innerHTML = `
                <h4 class="mb-2">${info.title}</h4>
                <ul style="margin-left: 1rem;">
                    ${info.requirements.map(req => `<li class="mb-1">${req}</li>`).join('')}
                </ul>
            `;
        }
        
        // Auto-populate CBCS codes
        generateCbcsCodes(category);
    } else {
        infoPanel.style.display = 'none';
        document.getElementById('cbcs-code-panel').style.display = 'none';
    }
}

function generateCbcsCodes(category) {
    const codePanel = document.getElementById('cbcs-code-panel');
    const codeContent = document.getElementById('cbcs-code-content');
    
    // Simplified CBCS code logic
    const baseCodes = {
        'C': '300-399 Series (Roads/Bridges)',
        'D': '400-499 Series (Water Control)',
        'E': '500-599 Series (Buildings)',
        'F': '600-699 Series (Utilities)',
        'G': '700-799 Series (Parks/Recreation)'
    };
    
    codeContent.innerHTML = `
        <p><strong>Applicable CBCS Range:</strong> ${baseCodes[category]}</p>
        <p class="text-sm">Specific codes will be determined based on facility type and damage assessment.</p>
        <button class="btn" onclick="requestCbcsAssignment()">Request Detailed CBCS Assignment</button>
    `;
    
    codePanel.style.display = 'block';
}

async function handleCbcsSubmit(event) {
    event.preventDefault();
    
    if (!validateForm('cbcsForm')) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }
    
    const submitBtn = event.target.querySelector('button[type="submit"]');
    setLoading(submitBtn, true);
    
    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        
        const response = await fetch('/api/intake/cbcs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('CBCS documentation generated successfully!', 'success');
            displayResults(result.documentation);
        } else {
            showNotification(result.message || 'Error generating documentation', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Network error occurred. Please try again.', 'error');
    } finally {
        setLoading(submitBtn, false);
    }
}

function requestCbcsAssignment() {
    showNotification('CBCS code assignment feature coming soon!', 'info');
}

function displayResults(documentation) {
    const resultsPanel = document.getElementById('results-panel');
    const resultsContent = document.getElementById('results-content');
    
    resultsContent.innerHTML = `
        <div class="mb-3">
            <h4>Generated CBCS Documentation:</h4>
            <pre style="background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 6px; white-space: pre-wrap; font-size: 0.9rem;">
${documentation}
            </pre>
        </div>
        <div class="grid grid-2">
            <button class="btn" onclick="downloadDocumentation()">📥 Download PDF</button>
            <button class="btn" onclick="continueToWizard()">➡️ Continue to Wizard</button>
        </div>
    `;
    
    resultsPanel.style.display = 'block';
    resultsPanel.scrollIntoView({ behavior: 'smooth' });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} show`;
    notification.innerHTML = `
        ${message}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; cursor: pointer;">×</button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

function downloadDocumentation() {
    showNotification('PDF download feature coming soon!', 'info');
}

function continueToWizard() {
    window.location.href = '/wizard/cbcs';
}
</script>
{% endblock %} 
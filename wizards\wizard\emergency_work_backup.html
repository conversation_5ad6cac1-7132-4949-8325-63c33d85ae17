<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Work - ComplianceMax V74</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            min-height: 100vh;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .navbar {
            background: rgba(15, 23, 42, 0.95);
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            padding: 1rem 0;
        }
        
        .feature-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 0.75rem;
            padding: 2rem;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            background: rgba(30, 41, 59, 1);
            border-color: rgba(59, 130, 246, 0.5);
        }
        
        .btn-primary {
            background: #ef4444;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            width: 100%;
            text-align: center;
        }
        
        .btn-primary:hover {
            background: #dc2626;
        }
        
        .btn-secondary {
            background: rgba(30, 41, 59, 0.9);
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            border: 1px solid rgba(75, 85, 99, 0.5);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: rgba(75, 85, 99, 0.5);
        }

        .emergency-badge {
            background: #ef4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .main-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .warning-card {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1.5rem;
        }

        .intake-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .intake-modal.active {
            display: flex;
        }

        .intake-content {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            color: white;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 0.5rem;
            background: rgba(15, 23, 42, 0.8);
            color: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ef4444;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="max-w-7xl mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <span class="text-white font-bold text-lg">CM</span>
                </div>
                <div>
                    <span class="text-white font-semibold text-lg">ComplianceMax V74</span>
                    <div class="text-blue-200 text-sm">Emergency Work Pathway</div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <span class="emergency-badge">Categories A & B</span>
                <a href="/" class="btn-secondary text-sm py-2 px-4">← Back to Dashboard</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Page Header -->
        <div class="main-card text-center">
            <div class="w-20 h-20 bg-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span class="text-3xl text-white font-bold">A&B</span>
            </div>
            <h2 class="text-4xl font-bold text-white mb-4">
                Emergency Work Pathway
            </h2>
            <p class="text-gray-300 text-lg mb-6 max-w-3xl mx-auto">
                Categories A & B for immediate emergency protective measures and debris removal. 
                Fast-track processing for urgent disaster response needs.
            </p>
            <div class="warning-card">
                <p class="text-yellow-300 font-semibold flex items-center justify-center">
                    <span class="mr-2">⚡</span>
                    Emergency Work Notice: This pathway is for immediate emergency response work only. CBCS (Consensus-
                    Based Codes & Standards) do NOT apply to Categories A & B. Use separate intake for permanent work.
                </p>
            </div>
        </div>

        <!-- Category Cards -->
        <div class="grid md:grid-cols-2 gap-6">
            <!-- Category A -->
            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl text-white font-bold">A</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">Category A - Debris Removal</h3>
                <p class="text-gray-300 mb-6 leading-relaxed">
                    Emergency debris assessment and classification, immediate debris removal for public safety, and contaminated debris protocols.
                </p>
                <div class="space-y-3 mb-8 text-left">
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Emergency debris assessment and classification</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Immediate debris removal for public safety</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Debris monitoring and documentation</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Contaminated debris protocols</span>
                    </div>
                </div>
                <button onclick="startIntake('A')" class="btn-primary">Start Category A Intake</button>
            </div>

            <!-- Category B -->
            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl text-white font-bold">B</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">Category B - Emergency Protective Measures</h3>
                <p class="text-gray-300 mb-6 leading-relaxed">
                    Emergency protective measure assessment, immediate safety measure implementation, and emergency facility stabilization.
                </p>
                <div class="space-y-3 mb-8 text-left">
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Emergency protective measure assessment</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Immediate safety measure implementation</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Emergency facility stabilization</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Temporary protective installations</span>
                    </div>
                </div>
                <button onclick="startIntake('B')" class="btn-primary">Start Category B Intake</button>
            </div>
        </div>
    </div>

    <!-- Intake Modal -->
    <div id="intakeModal" class="intake-modal">
        <div class="intake-content">
            <div class="flex justify-between items-center mb-6">
                <h3 id="modalTitle" class="text-2xl font-bold text-white">Emergency Work Intake</h3>
                <button onclick="closeIntake()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>
            
            <form id="intakeForm" class="space-y-4">
                <div class="form-group">
                    <label>Project Name</label>
                    <input type="text" id="projectName" required placeholder="Enter project name">
                </div>
                
                <div class="form-group">
                    <label>Work Category</label>
                    <select id="workCategory" required>
                        <option value="">Select category</option>
                        <option value="A">Category A - Debris Removal</option>
                        <option value="B">Category B - Emergency Protective Measures</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Urgency Level</label>
                    <select id="urgencyLevel" required>
                        <option value="">Select urgency</option>
                        <option value="Critical">Critical - Immediate threat to life/safety</option>
                        <option value="High">High - Significant risk if delayed</option>
                        <option value="Medium">Medium - Should be addressed promptly</option>
                        <option value="Low">Low - Can be scheduled</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Work Description</label>
                    <textarea id="workDescription" rows="4" required placeholder="Describe the emergency work needed"></textarea>
                </div>
                
                <div class="form-group">
                    <label>Incident Date</label>
                    <input type="date" id="incidentDate" required>
                </div>
                
                <div class="flex space-x-4">
                    <button type="button" onclick="closeIntake()" class="btn-secondary flex-1">Cancel</button>
                    <button type="submit" class="btn-primary flex-1">Submit Intake</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function startIntake(category) {
            document.getElementById('intakeModal').classList.add('active');
            document.getElementById('modalTitle').textContent = `Category ${category} - Emergency Work Intake`;
            document.getElementById('workCategory').value = category;
        }

        function closeIntake() {
            document.getElementById('intakeModal').classList.remove('active');
            document.getElementById('intakeForm').reset();
        }

        document.getElementById('intakeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                projectName: document.getElementById('projectName').value,
                workCategory: document.getElementById('workCategory').value,
                urgencyLevel: document.getElementById('urgencyLevel').value,
                workDescription: document.getElementById('workDescription').value,
                incidentDate: document.getElementById('incidentDate').value,
                start_wizard: true  // Request wizard session creation
            };

            try {
                const response = await fetch('/api/intake/emergency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.status === 'success' && result.wizard_session) {
                    closeIntake();
                    // MODIFICATION: Call the new modal function
                    showWizardLaunchModal(result.intake_id, result.wizard_session);
                } else {
                    // This is fallback logic in case the wizard fails to create
                    alert('Error submitting intake: ' + (result.message || 'Wizard session could not be created.'));
                    closeIntake();
                }
            } catch (error) {
                alert('Error submitting intake: ' + error.message);
            }
        });

        function showWizardLaunchModal(intakeId, wizardSession) {
            // Remove existing modal if it exists
            const existingModal = document.getElementById('wizardLaunchModal');
            if (existingModal) {
                existingModal.remove();
            }

            const modalContainer = document.createElement('div');
            modalContainer.id = 'wizardLaunchModal';
            modalContainer.className = 'intake-modal active'; // Reuse the same modal styling

            modalContainer.innerHTML = `
                <div class="intake-content" style="border-color: #34d399;">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-2">Intake Submitted Successfully!</h3>
                        <p class="text-gray-300 mb-4">Intake ID: <strong class="text-white">${intakeId}</strong></p>
                        <p class="text-gray-400 mb-6">Your emergency work intake is logged. You can now launch the compliance wizard to get started.</p>
                        <div class="flex space-x-4">
                            <button onclick="launchEmergencyWizard('${wizardSession.session_id}', JSON.parse(decodeURIComponent('${encodeURIComponent(JSON.stringify(wizardSession))}')))" class="btn-primary flex-1" style="background-color: #10b981; hover:background-color: #059669;">
                                🚀 Launch Wizard
                            </button>
                            <button onclick="document.getElementById('wizardLaunchModal').remove()" class="btn-secondary flex-1">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modalContainer);
        }

        // Launch emergency compliance wizard function
        function launchEmergencyWizard(sessionId, wizardSession) {
            // Close the launch modal if it's open
            const launchModal = document.getElementById('wizardLaunchModal');
            if (launchModal) {
                launchModal.remove();
            }

            // Create emergency wizard interface
            const wizardContainer = document.createElement('div');
            wizardContainer.id = 'emergencyWizardContainer';
            wizardContainer.className = 'wizard-overlay';
            
            wizardContainer.innerHTML = `
                <div class="wizard-content">
                    <div class="wizard-header">
                        <h2>🚨 Emergency Compliance Wizard - ${wizardSession.wizard_flow.type.toUpperCase()}</h2>
                        <button onclick="closeEmergencyWizard()" class="close-btn">&times;</button>
                    </div>
                    
                    <div class="wizard-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${(1/wizardSession.total_steps)*100}%"></div>
                        </div>
                        <span class="progress-text">Step 1 of ${wizardSession.total_steps}</span>
                    </div>
                    
                    <div class="wizard-body">
                        <h3>🚀 Emergency Work Compliance Ready!</h3>
                        <p><strong>Project:</strong> ${wizardSession.project_data.project_name || 'Emergency Project'}</p>
                        <p><strong>Category:</strong> ${wizardSession.project_data.category}</p>
                        <p><strong>Urgency:</strong> ${wizardSession.project_data.urgency || 'High'}</p>
                        
                        <div class="emergency-notice">
                            <h4>🚨 Emergency Work Notice:</h4>
                            <p><strong>Categories A & B are CBCS-EXEMPT!</strong></p>
                            <ul>
                                <li>✅ No consensus-based codes required</li>
                                <li>✅ Immediate work authorization</li>
                                <li>✅ Streamlined documentation</li>
                                <li>✅ Emergency safety protocols</li>
                            </ul>
                        </div>
                        
                        <div class="wizard-steps">
                            <h4>📋 Your Emergency Compliance Steps:</h4>
                            <ol>
                                ${wizardSession.wizard_flow.steps.map((step, index) => 
                                    `<li class="${index === 0 ? 'current-step' : ''}">${step.title}</li>`
                                ).join('')}
                            </ol>
                        </div>
                        
                        <div class="initial-guidance">
                            <h4>💡 Real-Time Emergency Guidance:</h4>
                            <p>Connected to 53,048 FEMA policy records for instant emergency work guidance and safety protocols.</p>
                        </div>
                        
                        <div class="wizard-actions">
                            <button onclick="startEmergencyWizardStep('${sessionId}', '${wizardSession.wizard_flow.steps[0].id}', ${JSON.stringify(wizardSession)})" class="btn-primary">
                                🚀 Start Step 1: ${wizardSession.wizard_flow.steps[0].title}
                            </button>
                            <button onclick="closeEmergencyWizard()" class="btn-secondary">
                                📋 Save for Later
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(wizardContainer);
            
            // Add emergency wizard styles
            if (!document.getElementById('emergencyWizardStyles')) {
                const styles = document.createElement('style');
                styles.id = 'emergencyWizardStyles';
                styles.textContent = `
                    .wizard-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.9);
                        z-index: 2000;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 2rem;
                    }
                    
                    .wizard-content {
                        background: rgba(30, 41, 59, 0.95);
                        border: 1px solid rgba(239, 68, 68, 0.3);
                        border-radius: 1rem;
                        max-width: 900px;
                        width: 100%;
                        max-height: 90vh;
                        overflow-y: auto;
                        color: white;
                    }
                    
                    .wizard-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 1.5rem;
                        border-bottom: 1px solid rgba(75, 85, 99, 0.3);
                    }
                    
                    .wizard-header h2 {
                        margin: 0;
                        color: #ef4444;
                    }
                    
                    .close-btn {
                        background: none;
                        border: none;
                        color: #9ca3af;
                        font-size: 2rem;
                        cursor: pointer;
                        padding: 0;
                        width: 2rem;
                        height: 2rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .close-btn:hover {
                        color: white;
                    }
                    
                    .wizard-progress {
                        padding: 1rem 1.5rem;
                        border-bottom: 1px solid rgba(75, 85, 99, 0.3);
                    }
                    
                    .progress-bar {
                        background: rgba(75, 85, 99, 0.3);
                        height: 0.5rem;
                        border-radius: 0.25rem;
                        overflow: hidden;
                        margin-bottom: 0.5rem;
                    }
                    
                    .progress-fill {
                        background: #ef4444;
                        height: 100%;
                        transition: width 0.3s ease;
                    }
                    
                    .progress-text {
                        font-size: 0.875rem;
                        color: #9ca3af;
                    }
                    
                    .wizard-body {
                        padding: 1.5rem;
                    }
                    
                    .wizard-body h3 {
                        color: #ef4444;
                        margin-bottom: 1rem;
                    }
                    
                    .wizard-body h4 {
                        color: #f87171;
                        margin: 1.5rem 0 0.5rem 0;
                    }
                    
                    .emergency-notice {
                        background: rgba(239, 68, 68, 0.1);
                        border: 1px solid rgba(239, 68, 68, 0.2);
                        border-radius: 0.5rem;
                        padding: 1rem;
                        margin: 1rem 0;
                    }
                    
                    .emergency-notice ul {
                        margin: 0.5rem 0;
                        padding-left: 1rem;
                    }
                    
                    .emergency-notice li {
                        margin: 0.25rem 0;
                        color: #fecaca;
                    }
                    
                    .wizard-steps ol {
                        margin: 0.5rem 0;
                        padding-left: 1.5rem;
                    }
                    
                    .wizard-steps li {
                        margin: 0.5rem 0;
                        color: #9ca3af;
                    }
                    
                    .wizard-steps li.current-step {
                        color: #ef4444;
                        font-weight: 600;
                    }
                    
                    .initial-guidance {
                        background: rgba(239, 68, 68, 0.1);
                        border: 1px solid rgba(239, 68, 68, 0.2);
                        border-radius: 0.5rem;
                        padding: 1rem;
                        margin: 1rem 0;
                    }
                    
                    .wizard-actions {
                        display: flex;
                        gap: 1rem;
                        margin-top: 2rem;
                    }
                    
                    .wizard-actions button {
                        flex: 1;
                        padding: 0.75rem 1rem;
                        border-radius: 0.5rem;
                        border: none;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                `;
                document.head.appendChild(styles);
            }
        }

        async function startEmergencyWizardStep(sessionId, stepId, wizardData) {
            // This function now dynamically updates the wizard UI
            const wizardBody = document.querySelector('#emergencyWizardContainer .wizard-body');
            if (!wizardBody) {
                console.error("Wizard body not found!");
                alert("Error: Could not update wizard content.");
                return;
            }

            const currentStepData = wizardData.wizard_flow.steps.find(s => s.id === stepId);
            if (!currentStepData) {
                console.error("Current step data not found!");
                alert("Error: Could not find data for the current step.");
                return;
            }
            
            // --- UI Update ---
            wizardBody.innerHTML = `
                <h3>Step: ${currentStepData.title}</h3>
                <p class="text-gray-400 mb-4">${currentStepData.description}</p>
                
                <div class="form-group">
                    <label for="stepInput-${stepId}">Project Information Summary</label>
                    <textarea id="stepInput-${stepId}" rows="6" class="w-full bg-slate-800 p-2 rounded" readonly>Project: ${wizardData.project_data.project_name}\nCategory: ${wizardData.project_data.category}\nUrgency: ${wizardData.project_data.urgency}\nDescription: ${wizardData.project_data.work_description}</textarea>
                </div>

                <h4>💡 Compliance Pod Guidance:</h4>
                <div class="initial-guidance">
                    <p><strong>Documentation Pod:</strong> Ensure all initial project details are logged. A timestamped record has been created.</p>
                    <p><strong>Safety Pod:</strong> Based on 'Critical' urgency, immediate safety protocols are advised. Log all initial actions.</p>
                </div>
                
                <div class="wizard-actions">
                    <button onclick="closeEmergencyWizard()" class="btn-secondary">
                        Save & Close
                    </button>
                    <button class="btn-primary">
                        Mark Step as Complete & Proceed →
                    </button>
                </div>
            `;
            
            // Update the progress bar
            const progressFill = document.querySelector('#emergencyWizardContainer .progress-fill');
            const progressText = document.querySelector('#emergencyWizardContainer .progress-text');
            const currentStepIndex = wizardData.wizard_flow.steps.findIndex(s => s.id === stepId);
            const progressPercentage = ((currentStepIndex + 1) / wizardData.total_steps) * 100;
            
            if(progressFill) progressFill.style.width = `${progressPercentage}%`;
            if(progressText) progressText.textContent = `Step ${currentStepIndex + 1} of ${wizardData.total_steps}`;
        }

        // Close emergency wizard function
        function closeEmergencyWizard() {
            const wizardContainer = document.getElementById('emergencyWizardContainer');
            if (wizardContainer) {
                wizardContainer.remove();
            }
        }

        // Close modal when clicking outside
        document.getElementById('intakeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeIntake();
            }
        });
    </script>
</body>
</html> 
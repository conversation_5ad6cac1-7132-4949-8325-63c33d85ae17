
'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle } from 'lucide-react';

interface Alert {
  id?: string;
  case_id?: string;
  title?: string;
  alert_type?: string;
  message?: string;
  description?: string;
  priority?: string;
  severity?: string;
  timestamp: string;
  [key: string]: any;
}

interface AlertsListProps {
  alerts: Alert[];
  maxItems?: number;
  className?: string;
}

const AlertsList: React.FC<AlertsListProps> = ({
  alerts,
  maxItems = 20,
  className = ''
}) => {
  const formatTimestamp = (timestamp: string | undefined) => {
    if (!timestamp) return 'Unknown';
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
      case 'high':
        return 'bg-red-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const displayedAlerts = alerts.slice(0, maxItems);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            <CardTitle>Live Alerts</CardTitle>
            <Badge variant="secondary">{alerts.length}</Badge>
          </div>
        </div>
        <CardDescription>Real-time alerts and notifications</CardDescription>
      </CardHeader>
      <CardContent>
        {displayedAlerts.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No alerts received yet.</p>
            <p className="text-sm">Listening for live alerts...</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {displayedAlerts.map((alert, index) => (
              <div 
                key={alert.id || alert.case_id || index} 
                className="flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <Badge className={getPriorityBadgeColor(alert.priority || alert.severity || 'medium')} />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">
                      {alert.title || alert.alert_type || 'Alert'}
                    </p>
                    <span className="text-xs text-muted-foreground">
                      {formatTimestamp(alert.timestamp || new Date().toISOString())}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {alert.message || alert.description}
                  </p>
                  {(alert.case_id || alert.id) && (
                    <p className="text-xs font-mono text-muted-foreground">
                      ID: {alert.case_id || alert.id}
                    </p>
                  )}
                </div>
              </div>
            ))}
            {alerts.length > maxItems && (
              <div className="text-center text-sm text-muted-foreground pt-2 border-t">
                Showing {maxItems} of {alerts.length} alerts
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AlertsList;

import componentName from './<%= componentImportFileName %>';
<%_ if ( interactionTests ) { _%>
import { expect } from 'storybook/test';
<%_ } _%>

export default {
  component: <%= componentName %>,
  title: '<%= componentName %>',<% if ( argTypes && argTypes.length > 0 ) { %> 
  argTypes: {<% for (let argType of argTypes) { %>
    <%= argType.name %>: { <%- argType.type %> : "<%- argType.actionText %>" },<% } %>
}
   <%_ } _%> 
};

export const Primary = {
  args: {<% for (let prop of props) { %>
    <%= prop.name %>:  <%- prop.defaultValue %>,<% } %>
  },
};

<%_ if ( interactionTests ) { _%>
export const Heading: Story = {
  play: async ({ canvas }) => {
    await expect(canvas.getByText(/Welcome to <%=componentName%>!/gi)).toBeTruthy();
  },
};
<%_ } _%>

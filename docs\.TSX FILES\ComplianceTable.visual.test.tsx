import React from 'react';
import renderer from 'react-test-renderer';
import { render } from '@testing-library/react';
import { ComplianceTable } from '@/components/compliance/ComplianceTable';
import { ComplianceItem, ComplianceStatus } from '@/types/compliance';

// Mock dependencies for visual tests
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/compliance',
    query: {},
  }),
}));

jest.mock('@/hooks/useDebounce', () => ({
  useDebounce: (value: any) => value,
}));

// Mock ResizeObserver and IntersectionObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Test data for visual tests
const mockComplianceData: ComplianceItem[] = [
  {
    id: '1',
    title: 'GDPR Compliance Assessment',
    description: 'General Data Protection Regulation compliance review for data processing activities',
    status: ComplianceStatus.COMPLIANT,
    priority: 'high',
    dueDate: '2024-12-31',
    assignee: 'John Doe',
    department: 'Legal',
    framework: 'GDPR',
    lastUpdated: '2024-06-20',
    progress: 85,
    riskLevel: 'medium',
    tags: ['privacy', 'data-protection', 'eu-regulation'],
  },
  {
    id: '2',
    title: 'SOX Financial Controls',
    description: 'Sarbanes-Oxley Act compliance verification for financial reporting controls',
    status: ComplianceStatus.NON_COMPLIANT,
    priority: 'critical',
    dueDate: '2024-07-15',
    assignee: 'Jane Smith',
    department: 'Finance',
    framework: 'SOX',
    lastUpdated: '2024-06-19',
    progress: 45,
    riskLevel: 'high',
    tags: ['financial', 'controls', 'audit'],
  },
  {
    id: '3',
    title: 'ISO 27001 Security Review',
    description: 'Information security management system audit and certification process',
    status: ComplianceStatus.IN_PROGRESS,
    priority: 'medium',
    dueDate: '2024-08-30',
    assignee: 'Bob Johnson',
    department: 'IT Security',
    framework: 'ISO 27001',
    lastUpdated: '2024-06-18',
    progress: 65,
    riskLevel: 'low',
    tags: ['security', 'iso', 'certification'],
  },
  {
    id: '4',
    title: 'HIPAA Privacy Assessment',
    description: 'Health Insurance Portability and Accountability Act compliance review',
    status: ComplianceStatus.PENDING,
    priority: 'high',
    dueDate: '2024-09-15',
    assignee: 'Alice Brown',
    department: 'Healthcare',
    framework: 'HIPAA',
    lastUpdated: '2024-06-17',
    progress: 20,
    riskLevel: 'medium',
    tags: ['healthcare', 'privacy', 'hipaa'],
  },
];

const defaultProps = {
  data: mockComplianceData,
  loading: false,
  error: null,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onView: jest.fn(),
  onStatusChange: jest.fn(),
  onBulkAction: jest.fn(),
  pagination: {
    page: 1,
    pageSize: 10,
    total: 4,
    totalPages: 1,
  },
  onPageChange: jest.fn(),
  onPageSizeChange: jest.fn(),
  sorting: {
    field: 'title',
    direction: 'asc' as const,
  },
  onSort: jest.fn(),
  filters: {},
  onFilterChange: jest.fn(),
  searchQuery: '',
  onSearchChange: jest.fn(),
};

describe('ComplianceTable Visual Regression Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Default States', () => {
    it('renders default table layout correctly', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with all compliance statuses', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with different priority levels', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Loading States', () => {
    it('renders loading state correctly', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} loading={true} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders loading skeleton for table rows', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            loading={true}
            data={[]}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Error States', () => {
    it('renders error state correctly', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            error="Failed to load compliance data. Please try again."
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders network error with retry button', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            error="Network connection failed"
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Empty States', () => {
    it('renders empty state when no data', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            data={[]}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders empty state with search query', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            data={[]}
            searchQuery="nonexistent"
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders empty state with active filters', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            data={[]}
            filters={{ status: ComplianceStatus.COMPLIANT }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Sorting States', () => {
    it('renders with ascending sort on title', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            sorting={{ field: 'title', direction: 'asc' }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with descending sort on due date', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            sorting={{ field: 'dueDate', direction: 'desc' }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with sort on status', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            sorting={{ field: 'status', direction: 'asc' }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Filter States', () => {
    it('renders with status filter applied', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            filters={{ status: ComplianceStatus.NON_COMPLIANT }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with multiple filters applied', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            filters={{ 
              status: ComplianceStatus.IN_PROGRESS,
              priority: 'high',
              department: 'Legal'
            }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with search query', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            searchQuery="GDPR"
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Pagination States', () => {
    it('renders with pagination controls', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            pagination={{
              page: 2,
              pageSize: 10,
              total: 25,
              totalPages: 3,
            }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders first page with disabled previous button', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            pagination={{
              page: 1,
              pageSize: 10,
              total: 25,
              totalPages: 3,
            }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders last page with disabled next button', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            pagination={{
              page: 3,
              pageSize: 10,
              total: 25,
              totalPages: 3,
            }}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Selection States', () => {
    it('renders with row selection enabled', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            enableSelection={true}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with bulk actions toolbar', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            enableSelection={true}
            selectedItems={['1', '2']}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Responsive States', () => {
    beforeEach(() => {
      // Mock different viewport sizes
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });
    });

    it('renders desktop layout', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders tablet layout', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders mobile layout', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480,
      });

      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Theme Variations', () => {
    it('renders with dark theme', () => {
      const tree = renderer
        .create(
          <div className="dark">
            <ComplianceTable {...defaultProps} />
          </div>
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with high contrast theme', () => {
      const tree = renderer
        .create(
          <div className="high-contrast">
            <ComplianceTable {...defaultProps} />
          </div>
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Data Variations', () => {
    it('renders with minimal data', () => {
      const minimalData = [{
        id: '1',
        title: 'Basic Compliance Item',
        status: ComplianceStatus.PENDING,
        priority: 'low',
        dueDate: '2024-12-31',
        assignee: 'User',
        department: 'General',
        framework: 'Basic',
        lastUpdated: '2024-06-20',
        progress: 0,
        riskLevel: 'low',
        tags: [],
      }];

      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            data={minimalData}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with rich data including long descriptions', () => {
      const richData = [{
        id: '1',
        title: 'Comprehensive GDPR Compliance Assessment with Extended Requirements',
        description: 'This is a very long description that tests how the table handles extensive text content. It includes multiple sentences and should demonstrate text truncation and expansion capabilities. The description covers various aspects of GDPR compliance including data processing activities, consent management, data subject rights, and cross-border data transfers.',
        status: ComplianceStatus.IN_PROGRESS,
        priority: 'critical',
        dueDate: '2024-12-31',
        assignee: 'John Doe (Senior Compliance Officer)',
        department: 'Legal & Compliance Department',
        framework: 'GDPR (General Data Protection Regulation)',
        lastUpdated: '2024-06-20',
        progress: 75,
        riskLevel: 'high',
        tags: ['privacy', 'data-protection', 'eu-regulation', 'gdpr', 'consent', 'data-subjects'],
      }];

      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            data={richData}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Action States', () => {
    it('renders with action buttons visible', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            showActions={true}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders with limited actions for read-only mode', () => {
      const tree = renderer
        .create(
          <ComplianceTable 
            {...defaultProps} 
            readOnly={true}
          />
        )
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Progress Indicators', () => {
    it('renders progress bars correctly', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders risk level indicators', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });

  describe('Interactive Elements', () => {
    it('renders dropdown menus in closed state', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });

    it('renders tooltips on hover elements', () => {
      const tree = renderer
        .create(<ComplianceTable {...defaultProps} />)
        .toJSON();
      expect(tree).toMatchSnapshot();
    });
  });
});

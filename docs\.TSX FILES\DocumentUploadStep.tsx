
'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  FileText, 
  X, 
  CheckCircle, 
  AlertTriangle,
  Download
} from 'lucide-react'
import { formatFileSize } from '@/lib/utils'

interface Document {
  id: string
  name: string
  type: string
  size: number
  uploadedAt: Date
  status?: 'uploading' | 'completed' | 'error'
  progress?: number
}

interface DocumentUploadStepProps {
  documents: Document[]
  onUpdate: (documents: Document[]) => void
}

const allowedTypes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png',
  'text/plain'
]

const maxFileSize = 10 * 1024 * 1024 // 10MB

export function DocumentUploadStep({ documents, onUpdate }: DocumentUploadStepProps) {
  const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set())

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const newDocuments: Document[] = []

    for (const file of acceptedFiles) {
      const documentId = Math.random().toString(36).substr(2, 9)
      const newDoc: Document = {
        id: documentId,
        name: file.name,
        type: file.type,
        size: file.size,
        uploadedAt: new Date(),
        status: 'uploading',
        progress: 0
      }

      newDocuments.push(newDoc)
      setUploadingFiles(prev => new Set(prev).add(documentId))

      // Simulate upload progress
      const uploadPromise = simulateUpload(documentId, (progress) => {
        onUpdate([...documents, ...newDocuments.map(doc => 
          doc.id === documentId ? { ...doc, progress } : doc
        )])
      })

      uploadPromise.then(() => {
        setUploadingFiles(prev => {
          const newSet = new Set(prev)
          newSet.delete(documentId)
          return newSet
        })
        
        onUpdate([...documents, ...newDocuments.map(doc => 
          doc.id === documentId ? { ...doc, status: 'completed' as const, progress: 100 } : doc
        )])
      }).catch(() => {
        setUploadingFiles(prev => {
          const newSet = new Set(prev)
          newSet.delete(documentId)
          return newSet
        })
        
        onUpdate([...documents, ...newDocuments.map(doc => 
          doc.id === documentId ? { ...doc, status: 'error' as const } : doc
        )])
      })
    }

    onUpdate([...documents, ...newDocuments])
  }, [documents, onUpdate])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'text/plain': ['.txt']
    },
    maxSize: maxFileSize,
    multiple: true
  })

  const simulateUpload = (documentId: string, onProgress: (progress: number) => void): Promise<void> => {
    return new Promise((resolve, reject) => {
      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 15
        if (progress >= 100) {
          progress = 100
          clearInterval(interval)
          setTimeout(() => resolve(), 500)
        }
        onProgress(progress)
      }, 200)

      // Simulate occasional failures
      if (Math.random() < 0.1) {
        setTimeout(() => {
          clearInterval(interval)
          reject(new Error('Upload failed'))
        }, 2000)
      }
    })
  }

  const removeDocument = (documentId: string) => {
    onUpdate(documents.filter(doc => doc.id !== documentId))
  }

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return '📄'
    if (type.includes('word') || type.includes('document')) return '📝'
    if (type.includes('image')) return '🖼️'
    return '📄'
  }

  const requiredDocuments = [
    'Project Scope Statement',
    'Cost Estimates',
    'Environmental Assessment',
    'Insurance Documentation',
    'Damage Assessment Photos'
  ]

  return (
    <div className="space-y-6">
      {/* Required Documents Checklist */}
      <Card className="bg-yellow-500/10 border-yellow-400/20">
        <CardContent className="p-4">
          <h3 className="text-white font-medium mb-3 flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-400" />
            Required Documents
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {requiredDocuments.map((doc, index) => (
              <div key={index} className="flex items-center gap-2 text-sm text-white/70">
                <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                {doc}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upload Area */}
      <Card className="glass border-white/20">
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-blue-400 bg-blue-400/10' 
                : 'border-white/30 hover:border-white/50'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="w-12 h-12 text-white/60 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">
              {isDragActive ? 'Drop files here' : 'Upload Documents'}
            </h3>
            <p className="text-white/60 mb-4">
              Drag and drop files here, or click to select files
            </p>
            <p className="text-xs text-white/40">
              Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB each)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Documents */}
      {documents.length > 0 && (
        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <h3 className="text-white font-medium mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Uploaded Documents ({documents.length})
            </h3>
            <div className="space-y-3">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center gap-3 p-3 bg-white/5 rounded-lg"
                >
                  <div className="text-2xl">{getFileIcon(doc.type)}</div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">{doc.name}</p>
                    <p className="text-white/60 text-sm">
                      {formatFileSize(doc.size)} • {new Date(doc.uploadedAt).toLocaleString()}
                    </p>
                    {doc.status === 'uploading' && (
                      <div className="mt-2">
                        <Progress value={doc.progress || 0} className="h-1" />
                        <p className="text-xs text-white/60 mt-1">
                          Uploading... {Math.round(doc.progress || 0)}%
                        </p>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {doc.status === 'completed' && (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    )}
                    {doc.status === 'error' && (
                      <AlertTriangle className="w-5 h-5 text-red-400" />
                    )}
                    {doc.status === 'uploading' && (
                      <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDocument(doc.id)}
                      disabled={uploadingFiles.has(doc.id)}
                      className="text-white/60 hover:text-white hover:bg-white/10"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Guidelines */}
      <Card className="bg-blue-500/10 border-blue-400/20">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div>
              <h4 className="text-white font-medium mb-1">Document Guidelines</h4>
              <ul className="text-white/70 text-sm space-y-1">
                <li>• Ensure all documents are clear and legible</li>
                <li>• Include all required documentation for complete review</li>
                <li>• File names should be descriptive and professional</li>
                <li>• Remove any sensitive personal information not required for review</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

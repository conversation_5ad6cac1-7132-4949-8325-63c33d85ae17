import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Paper,
  useTheme,
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Gavel as GavelIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';

const Home: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  const features = [
    {
      icon: <SpeedIcon sx={{ fontSize: 40 }} />,
      title: 'Quick Check',
      description: 'Rapid compliance assessment of your documents against FEMA policies.',
      action: () => navigate('/quick-check'),
    },
    {
      icon: <GavelIcon sx={{ fontSize: 40 }} />,
      title: 'Full Review',
      description: 'Comprehensive compliance review with detailed analysis and recommendations.',
      action: () => navigate('/compliance'),
    },
    {
      icon: <SecurityIcon sx={{ fontSize: 40 }} />,
      title: 'Policy Updates',
      description: 'Stay current with real-time FEMA policy updates and changes.',
      action: () => navigate('/policies'),
    },
    {
      icon: <AnalyticsIcon sx={{ fontSize: 40 }} />,
      title: 'Analytics',
      description: 'Advanced analytics and insights for your compliance data.',
      action: () => navigate('/analytics'),
    },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          textAlign: 'center',
          mb: 8,
          mt: 4,
        }}
      >
        <Typography
          variant="h2"
          component="h1"
          sx={{
            fontWeight: 700,
            mb: 3,
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Modern FEMA Compliance
        </Typography>
        <Typography
          variant="h5"
          sx={{
            mb: 4,
            color: 'rgba(255, 255, 255, 0.7)',
            maxWidth: 600,
            mx: 'auto',
          }}
        >
          Streamline your disaster recovery process with AI-powered compliance checks
          and real-time policy updates.
        </Typography>
        <Button
          variant="contained"
          size="large"
          onClick={() => navigate('/quick-check')}
          sx={{
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
          }}
        >
          Get Started
        </Button>
      </Box>

      {/* Features Grid */}
      <Grid container spacing={4}>
        {features.map((feature, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Paper
              sx={{
                p: 4,
                height: '100%',
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: theme.shape.borderRadius * 2,
                transition: 'transform 0.2s ease-in-out',
                cursor: 'pointer',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  background: 'rgba(255, 255, 255, 0.08)',
                },
              }}
              onClick={feature.action}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                }}
              >
                <Box
                  sx={{
                    mb: 2,
                    color: theme.palette.primary.main,
                  }}
                >
                  {feature.icon}
                </Box>
                <Typography
                  variant="h5"
                  component="h2"
                  sx={{
                    mb: 2,
                    color: 'white',
                    fontWeight: 600,
                  }}
                >
                  {feature.title}
                </Typography>
                <Typography
                  sx={{
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}
                >
                  {feature.description}
                </Typography>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Home;

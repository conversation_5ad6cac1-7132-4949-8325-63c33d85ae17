import React, { useState, useRef, useCallback } from 'react';
import axios from '../lib/axios';
import { validateFileUpload } from '../config';
import { useDropzone } from 'react-dropzone';

interface FileUploadProps {
  onFileUploaded?: (file: UploadedFile) => void;
  onFilesUploaded?: (files: UploadedFile[]) => void;
  multiple?: boolean;
  accept?: string;
  maxFiles?: number;
  className?: string;
  // Enhanced props for consolidation
  mode?: 'basic' | 'enhanced' | 'pod-integrated';
  category?: string; // For FEMA category-specific uploads
  projectId?: string;
  enablePodRouting?: boolean;
  enableComplianceAnalysis?: boolean;
  showProgress?: boolean;
  allowedTypes?: string[];
  maxSizePerFile?: number;
  onAnalysisComplete?: (results: any) => void;
}

interface UploadedFile {
  id: string;
  filename: string;
  size: number;
  content_type: string;
  upload_date: string;
  // Enhanced properties
  category?: string;
  analysis_status?: 'pending' | 'processing' | 'complete' | 'error';
  compliance_score?: number;
  pod_routing?: string[];
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileUploaded,
  onFilesUploaded,
  multiple = false,
  accept = '.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png',
  maxFiles = 10,
  className = '',
  // Enhanced props with defaults
  mode = 'basic',
  category,
  projectId,
  enablePodRouting = false,
  enableComplianceAnalysis = false,
  showProgress = true,
  allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png'],
  maxSizePerFile = 50 * 1024 * 1024, // 50MB default
  onAnalysisComplete,
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [error, setError] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setError(null);
    setUploadProgress({});

    // Validate files
    const filesToUpload = Array.from(files);
    
    if (filesToUpload.length > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Validate each file
    for (const file of filesToUpload) {
      const validation = validateFileUpload(file);
      if (!validation.valid) {
        setError(validation.error || 'File validation failed');
        return;
      }
    }

    setUploading(true);

    try {
      if (multiple && filesToUpload.length > 1) {
        // Upload multiple files
        const formData = new FormData();
        filesToUpload.forEach((file) => {
          formData.append('files', file);
        });

        const response = await axios.post('/api/files/upload-multiple', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress({ multiple: progress });
            }
          },
        });

        const uploadedFiles = response.data;
        setUploadedFiles(prev => [...prev, ...uploadedFiles]);
        onFilesUploaded?.(uploadedFiles);
        
      } else {
        // Upload single file
        const file = filesToUpload[0];
        const formData = new FormData();
        formData.append('file', file);

        const response = await axios.post('/api/files/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress({ [file.name]: progress });
            }
          },
        });

        const uploadedFile = response.data;
        setUploadedFiles(prev => [...prev, uploadedFile]);
        onFileUploaded?.(uploadedFile);
      }

      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error: any) {
      console.error('File upload failed:', error);
      if (error.response?.data?.detail) {
        setError(error.response.data.detail);
      } else {
        setError('File upload failed. Please try again.');
      }
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  const removeFile = async (fileId: string) => {
    try {
      await axios.delete(`/api/files/${fileId}`);
      setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    } catch (error) {
      console.error('File deletion failed:', error);
      setError('Failed to delete file');
    }
  };

  return (
    <div className={`file-upload-component ${className}`}>
      <div className="upload-area">
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={handleFileSelect}
          disabled={uploading}
          className="hidden"
          id="file-upload-input"
        />
        
        <label
          htmlFor="file-upload-input"
          className={`
            flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer
            ${uploading ? 'border-gray-300 bg-gray-50' : 'border-gray-300 bg-gray-50 hover:bg-gray-100'}
            ${uploading ? 'cursor-not-allowed' : 'cursor-pointer'}
          `}
        >
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <svg
              className="w-8 h-8 mb-4 text-gray-500"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 16"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
              />
            </svg>
            <p className="mb-2 text-sm text-gray-500">
              <span className="font-semibold">Click to upload</span> or drag and drop
            </p>
            <p className="text-xs text-gray-500">
              {accept.replace(/\./g, '').toUpperCase()} files up to 10MB
              {multiple && ` (max ${maxFiles} files)`}
            </p>
          </div>
        </label>
      </div>

      {/* Upload Progress */}
      {uploading && Object.keys(uploadProgress).length > 0 && (
        <div className="mt-4">
          {Object.entries(uploadProgress).map(([fileName, progress]) => (
            <div key={fileName} className="mb-2">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>{fileName === 'multiple' ? 'Uploading files...' : fileName}</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Uploaded Files</h4>
          <div className="space-y-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded"
              >
                <div className="flex items-center space-x-3">
                  <svg
                    className="w-5 h-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.filename}</p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)} • {file.content_type}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(file.id)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
import * as React from 'react';
<%_ if (!minimal) { _%>
import NxWelcome from "./nx-welcome";
<%_ } _%>
import { Link, Route, Routes } from 'react-router-dom';
<%_ if (dynamic) { _%>
import { loadRemote } from '@module-federation/enhanced/runtime';
<%_ } _%>

<%_ if (remotes.length > 0) {
 remotes.forEach(function(r) { _%>
<%_ if (dynamic) { _%>
const <%= r.className %> = React.lazy(() => loadRemote('<%= r.fileName %>/Module') as any)
<%_ } else  { _%>
const <%= r.className %> = React.lazy(() => import('<%= r.fileName %>/Module'));
<%_ } _%>
 <%_ }); _%>
<%_ } _%>

export function App() {
  return (
    <React.Suspense fallback={null}>
      <ul>
        <li><Link to="/">Home</Link></li>
       <%_ remotes.forEach(function(r) { _%>
        <li><Link to="/<%=r.fileName%>"><%=r.className%></Link></li>
       <%_ }); _%>
      </ul>
      <Routes>
       <%_ if (!minimal) { _%>
        <Route path="/" element={<NxWelcome title="<%= projectName %>"/>} />
       <%_ } _%>
       <%_ remotes.forEach(function(r) { _%>
        <Route path="/<%=r.fileName%>" element={<<%= r.className %>/>} />
       <%_ }); _%>
      </Routes>
    </React.Suspense>
  );
}

export default App;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBCS Work (C-G) - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .header {
            background: #059669;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .nav {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .nav a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        .nav a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #059669;
        }
        
        .btn {
            background: #059669;
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #047857;
        }
        
        .status {
            background: #dcfce7;
            color: #166534;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .category-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .category-card:hover {
            border-color: #059669;
            transform: translateY(-2px);
        }
        
        .category-card.selected {
            border-color: #059669;
            background: #f0fdf4;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🏗️ CBCS Permanent Work (Categories C-G)</h1>
            <p>FEMA Public Assistance Permanent Work Categories</p>
            <div class="nav">
                <a href="/direct">Dashboard</a>
                <a href="/emergency">Emergency Work (A&B)</a>
                <a href="/api/status">System Status</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="status">
            ✅ ComplianceMax V74 Operational | 53,048 FEMA Records | CBCS Integration Active
        </div>

        <div class="card">
            <h2>📊 Select Work Category</h2>
            <div class="category-grid">
                <div class="category-card" data-category="C">
                    <h3>Category C</h3>
                    <p>Roads & Bridges</p>
                </div>
                <div class="category-card" data-category="D">
                    <h3>Category D</h3>
                    <p>Water Control</p>
                </div>
                <div class="category-card" data-category="E">
                    <h3>Category E</h3>
                    <p>Buildings & Equipment</p>
                </div>
                <div class="category-card" data-category="F">
                    <h3>Category F</h3>
                    <p>Public Utilities</p>
                </div>
                <div class="category-card" data-category="G">
                    <h3>Category G</h3>
                    <p>Parks & Recreation</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🔧 Project Information</h2>
            <form id="cbcsForm">
                <div class="form-group">
                    <label for="selectedCategory">Selected Category</label>
                    <input type="text" id="selectedCategory" name="selectedCategory" readonly placeholder="Select a category above">
                </div>
                
                <div class="form-group">
                    <label for="facilityType">Facility Type</label>
                    <select id="facilityType" name="facilityType">
                        <option value="">Select facility type...</option>
                        <option value="school">School</option>
                        <option value="hospital">Hospital</option>
                        <option value="government">Government Building</option>
                        <option value="infrastructure">Infrastructure</option>
                        <option value="utility">Utility System</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="workDescription">Work Description</label>
                    <textarea id="workDescription" name="workDescription" rows="3" placeholder="Describe the permanent work to be performed"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="estimatedCost">Estimated Cost ($)</label>
                    <input type="number" id="estimatedCost" name="estimatedCost" placeholder="Enter estimated cost">
                </div>
                
                <button type="submit" class="btn">Generate CBCS Requirements</button>
            </form>
        </div>
    </div>

    <script>
        // Category selection handler
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.category-card').forEach(c => c.classList.remove('selected'));
                
                // Select current card
                this.classList.add('selected');
                
                // Update form
                const category = this.dataset.category;
                document.getElementById('selectedCategory').value = `Category ${category}`;
            });
        });

        // Form submission handler
        document.getElementById('cbcsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const category = document.getElementById('selectedCategory').value;
            if (!category) {
                alert('Please select a work category first');
                return;
            }
            alert(`CBCS requirements generated for ${category}`);
        });
    </script>
</body>
</html> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Public Assistance Disaster Recovery - ComplianceMax V74</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            min-height: 100vh;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .navbar {
            background: rgba(15, 23, 42, 0.95);
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            padding: 1.5rem 0;
        }
        
        .hero-section {
            padding: 3rem 2rem 2rem;
            text-align: center;
        }
        
        .features-section {
            background: rgba(15, 23, 42, 0.8);
            padding: 4rem 2rem;
        }
        
        .process-section {
            padding: 4rem 2rem;
        }
        
        .feature-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 3rem 2rem;
            transition: all 0.3s ease;
            height: 100%;
            min-height: 280px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        
        .feature-card:hover {
            background: rgba(30, 41, 59, 1);
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1.5rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }
        
        .feature-description {
            font-size: 1rem;
            line-height: 1.6;
            color: rgba(156, 163, 175, 1);
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
            padding: 1rem 2.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: rgba(30, 41, 59, 0.9);
            color: white;
            padding: 1rem 2.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            border: 1px solid rgba(75, 85, 99, 0.5);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: rgba(75, 85, 99, 0.5);
            transform: translateY(-1px);
        }

        .grid-features {
            gap: 2.5rem;
        }

        .grid-process {
            gap: 2rem;
        }

        .auth-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .auth-modal.active {
            display: flex;
        }

        .auth-content {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .pathway-selector {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pathway-selector:hover {
            background: rgba(30, 41, 59, 1);
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateY(-2px);
        }

        .quick-access {
            background: rgba(15, 23, 42, 0.8);
            padding: 2.5rem 2rem;
            margin: 1rem 0;
            border-radius: 1rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: white;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: rgba(156, 163, 175, 1);
            margin-bottom: 3rem;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1.5rem;
        }

        .hero-description {
            font-size: 1.2rem;
            line-height: 1.5;
            color: rgba(156, 163, 175, 1);
            margin-bottom: 2rem;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .feature-card {
                padding: 2rem 1.5rem;
                min-height: 240px;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="max-w-7xl mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <span class="text-white font-semibold text-xl">ComplianceMax; Public Assistance Compliance Tools</span>
            </div>
            <div class="flex items-center space-x-8">
                <a href="/" class="text-gray-300 hover:text-white text-lg">Home</a>
                <a href="/emergency" class="text-gray-300 hover:text-white text-lg">Emergency Work</a>
                <a href="/cbcs" class="text-gray-300 hover:text-white text-lg">CBCS Work</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="max-w-5xl mx-auto">
            <h1 class="hero-title">
                Streamline Your <span class="text-blue-400">Public Assistance</span><br>
                Disaster Recovery <span class="text-blue-400">Compliance</span> Process
            </h1>
            <p class="hero-description max-w-3xl mx-auto">
                Our multi-step wizard guides you through Public Assistance 
                Disaster Recovery compliance issues, with document management, automated QA, and 
                comprehensive reporting.
            </p>
            <div class="flex justify-center space-x-6 mb-4">
                <a href="/emergency" class="btn-primary">Emergency Work (A&B)</a>
                <a href="/cbcs" class="btn-secondary">CBCS Work (C-G)</a>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="quick-access">
        <div class="max-w-5xl mx-auto text-center">
            <h2 class="section-title" style="font-size: 2rem; margin-bottom: 1rem;">Choose Your Compliance Pathway</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <a href="/emergency" class="pathway-selector text-left block">
                    <div class="flex items-center space-x-6">
                        <div class="w-16 h-16 bg-red-600 rounded-xl flex items-center justify-center">
                            <span class="text-white font-bold text-xl">A&B</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-white mb-2">Emergency Work</h4>
                            <p class="text-gray-400">Categories A & B - Debris removal and emergency protective measures</p>
                        </div>
                    </div>
                </a>

                <a href="/cbcs" class="pathway-selector text-left block">
                    <div class="flex items-center space-x-6">
                        <div class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center">
                            <span class="text-white font-bold text-xl">C-G</span>
                        </div>
                        <div>
                            <h4 class="text-xl font-semibold text-white mb-2">CBCS Permanent Work</h4>
                            <p class="text-gray-400">Categories C-G - Infrastructure rebuilding with full compliance</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Key Features Section -->
    <div class="features-section">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="section-title">Key Features</h2>
                <p class="section-subtitle">Everything you need to streamline your disaster recovery compliance process</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 grid-features">
                <!-- Multi-Step Wizard -->
                <div class="feature-card">
                    <div class="feature-icon bg-blue-600">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Multi-Step Wizard</h3>
                    <p class="feature-description">Guided process that walks you through each step of disaster recovery compliance requirements.</p>
                </div>

                <!-- Document Management -->
                <div class="feature-card">
                    <div class="feature-icon bg-blue-600">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Document Management</h3>
                    <p class="feature-description">Easily upload, organize, and manage all required documentation in one place.</p>
                </div>

                <!-- Automated QA -->
                <div class="feature-card">
                    <div class="feature-icon bg-blue-600">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Automated QA</h3>
                    <p class="feature-description">Built-in quality assurance checks to ensure compliance with disaster recovery requirements.</p>
                </div>

                <!-- Comprehensive Reports -->
                <div class="feature-card">
                    <div class="feature-icon bg-blue-600">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Comprehensive Reports</h3>
                    <p class="feature-description">Generate detailed reports for submission and record-keeping purposes.</p>
                </div>

                <!-- Compliance Tracking -->
                <div class="feature-card">
                    <div class="feature-icon bg-blue-600">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Compliance Tracking</h3>
                    <p class="feature-description">Monitor your progress and track compliance status across all projects.</p>
                </div>

                <!-- Secure & Reliable -->
                <div class="feature-card">
                    <div class="feature-icon bg-blue-600">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Secure & Reliable</h3>
                    <p class="feature-description">Enterprise-grade security to protect your sensitive compliance data.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- How It Works Section -->
    <div class="process-section">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="section-title">How It Works</h2>
                <p class="section-subtitle">Our streamlined process makes disaster recovery compliance simple and efficient</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 grid-process">
                <!-- Step 1 -->
                <div class="feature-card text-center">
                    <div class="feature-icon bg-blue-600 mx-auto">
                        <span class="text-2xl font-bold text-white">01</span>
                    </div>
                    <h3 class="feature-title">Create Project</h3>
                    <p class="feature-description">Start by creating a new compliance project and entering basic information.</p>
                </div>

                <!-- Step 2 -->
                <div class="feature-card text-center">
                    <div class="feature-icon bg-blue-600 mx-auto">
                        <span class="text-2xl font-bold text-white">02</span>
                    </div>
                    <h3 class="feature-title">Follow Wizard</h3>
                    <p class="feature-description">Complete each step of the compliance wizard, uploading required documents.</p>
                </div>

                <!-- Step 3 -->
                <div class="feature-card text-center">
                    <div class="feature-icon bg-blue-600 mx-auto">
                        <span class="text-2xl font-bold text-white">03</span>
                    </div>
                    <h3 class="feature-title">Submit & Track</h3>
                    <p class="feature-description">Submit your completed project and track its progress through the approval process.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Stats Section (if available) -->
    {% if db_stats %}
    <div class="quick-access">
        <div class="max-w-5xl mx-auto text-center">
            <h2 class="section-title">System Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card text-center">
                    <div class="feature-icon bg-green-600 mx-auto">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">{{ "{:,}".format(db_stats.get('total_records', 0)) }}</h3>
                    <p class="feature-description">FEMA Policy Records Available</p>
                </div>
                
                <div class="feature-card text-center">
                    <div class="feature-icon bg-blue-600 mx-auto">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">{{ db_stats.get('categories_available', 7) }}</h3>
                    <p class="feature-description">FEMA Categories (A-G)</p>
                </div>
                
                <div class="feature-card text-center">
                    <div class="feature-icon bg-purple-600 mx-auto">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="feature-title">Phase 9</h3>
                    <p class="feature-description">Wizard Integration Active</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <script>
        // Add any JavaScript functionality here
        console.log('ComplianceMax V74 - Phase 9 Wizard Integration Loaded');
        console.log('✅ Frontend Wizards → Compliance Pods → Phase 8 Database');
        console.log('📋 NO POWERSHELL DEPENDENCIES');
    </script>
</body>
</html> 
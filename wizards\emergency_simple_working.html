<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Work (A&B) - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }
        
        .header {
            background: #2563eb;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .nav {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .nav a {
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        .nav a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #2563eb;
        }
        
        .btn {
            background: #2563eb;
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #1d4ed8;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .status {
            background: #dcfce7;
            color: #166534;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🚨 Emergency Work (Categories A & B)</h1>
            <p>FEMA Public Assistance Emergency Response</p>
            <div class="nav">
                <a href="/direct">Dashboard</a>
                <a href="/cbcs">CBCS Work (C-G)</a>
                <a href="/api/status">System Status</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="status">
            ✅ ComplianceMax V74 Operational | 53,048 FEMA Records | Phase 9 Integration
        </div>

        <div class="grid">
            <div class="card">
                <h2>📋 Category A: Debris Removal</h2>
                <form id="categoryAForm">
                    <div class="form-group">
                        <label for="debrisType">Debris Type</label>
                        <select id="debrisType" name="debrisType">
                            <option value="">Select debris type...</option>
                            <option value="vegetative">Vegetative Debris</option>
                            <option value="construction">Construction & Demolition</option>
                            <option value="household">Household Hazardous Materials</option>
                            <option value="vehicle">Vehicles & Vessels</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="estimatedVolume">Estimated Volume (cubic yards)</label>
                        <input type="number" id="estimatedVolume" name="estimatedVolume" placeholder="Enter volume">
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Work Location</label>
                        <input type="text" id="location" name="location" placeholder="Street address or area description">
                    </div>
                    
                    <button type="submit" class="btn">Submit Category A Request</button>
                </form>
            </div>

            <div class="card">
                <h2>🛡️ Category B: Emergency Protective Measures</h2>
                <form id="categoryBForm">
                    <div class="form-group">
                        <label for="emergencyType">Emergency Measure Type</label>
                        <select id="emergencyType" name="emergencyType">
                            <option value="">Select emergency type...</option>
                            <option value="evacuation">Evacuation Services</option>
                            <option value="emergency_operations">Emergency Operations Center</option>
                            <option value="sandbagging">Sandbagging Operations</option>
                            <option value="warning_systems">Warning Systems</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="duration">Duration (hours)</label>
                        <input type="number" id="duration" name="duration" placeholder="Estimated duration">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Work Description</label>
                        <textarea id="description" name="description" rows="3" placeholder="Describe the emergency protective measures"></textarea>
                    </div>
                    
                    <button type="submit" class="btn">Submit Category B Request</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Simple form submission handler
        document.getElementById('categoryAForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Category A request submitted for processing');
        });
        
        document.getElementById('categoryBForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Category B request submitted for processing');
        });
    </script>
</body>
</html> 
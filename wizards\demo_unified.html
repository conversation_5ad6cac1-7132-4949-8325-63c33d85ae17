<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Compliance Intake - ComplianceMax V74</title>
    <style>
        /* SCOPED STYLES - Only apply to professional intake page */
        .professional-intake-page * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .professional-intake-page {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            min-height: 100vh;
            color: white;
            line-height: 1.6;
        }
        
        .professional-intake-page .intake-header {
            background: rgba(15, 23, 42, 0.95);
            color: white;
            padding: 2rem 0;
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            backdrop-filter: blur(10px);
        }
        
        .professional-intake-page .intake-header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            color: white;
        }
        
        .professional-intake-page .intake-header p {
            color: #94a3b8;
            font-size: 1.2rem;
        }
        
        .professional-intake-page .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .professional-intake-page .pathway-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .professional-intake-page .pathway-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .professional-intake-page .pathway-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            border-color: #3b82f6;
        }
        
        .professional-intake-page .pathway-card.selected {
            background: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }
        
        .professional-intake-page .pathway-card h3 {
            color: white;
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }
        
        .professional-intake-page .pathway-card p {
            color: #cbd5e1;
            font-size: 0.9rem;
        }
        
        .professional-intake-page .intake-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .professional-intake-page .form-section {
            padding: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .professional-intake-page .form-section:last-child {
            border-bottom: none;
        }
        
        .professional-intake-page .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: white;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .professional-intake-page .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .professional-intake-page .form-group {
            margin-bottom: 1.5rem;
        }
        
        .professional-intake-page .form-label {
            display: block;
            font-weight: 500;
            color: #e2e8f0;
            margin-bottom: 0.5rem;
        }
        
        .professional-intake-page .form-input, 
        .professional-intake-page .form-select, 
        .professional-intake-page .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            font-size: 1rem;
            transition: all 0.2s;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .professional-intake-page .form-input::placeholder, 
        .professional-intake-page .form-textarea::placeholder {
            color: #94a3b8;
        }
        
        .professional-intake-page .form-input:focus, 
        .professional-intake-page .form-select:focus, 
        .professional-intake-page .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .professional-intake-page .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .professional-intake-page .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 0.75rem;
            margin-top: 0.5rem;
        }
        
        .professional-intake-page .checkbox-item {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            transition: all 0.2s;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .professional-intake-page .checkbox-item:hover {
            border-color: #3b82f6;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .professional-intake-page .checkbox-item input[type="checkbox"] {
            margin-top: 0.25rem;
        }
        
        .professional-intake-page .checkbox-label {
            flex: 1;
            cursor: pointer;
        }
        
        .professional-intake-page .checkbox-title {
            display: block;
            font-weight: 500;
            color: white;
            font-size: 0.9rem;
        }
        
        .professional-intake-page .checkbox-desc {
            display: block;
            color: #94a3b8;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .professional-intake-page .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .professional-intake-page .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .professional-intake-page .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .professional-intake-page .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .professional-intake-page .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .professional-intake-page .btn-full {
            width: 100%;
        }
        
        .professional-intake-page .form-actions {
            padding: 2rem;
            background: rgba(0, 0, 0, 0.2);
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        
        .professional-intake-page .help-text {
            font-size: 0.85rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
        
        .professional-intake-page .required {
            color: #f87171;
        }
        
        .professional-intake-page .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 500;
            background: rgba(34, 197, 94, 0.2);
            color: #4ade80;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        @media (max-width: 768px) {
            .professional-intake-page .container {
                margin: 1rem;
            }
            
            .professional-intake-page .form-grid, 
            .professional-intake-page .pathway-selector {
                grid-template-columns: 1fr;
            }
            
            .professional-intake-page .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            .professional-intake-page .form-actions {
                flex-direction: column;
            }
            
            .professional-intake-page .intake-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body class="professional-intake-page">
    <!-- Dashboard-Style Header -->
    <header class="intake-header">
        <h1>Professional Compliance Analysis</h1>
        <p>Comprehensive FEMA Public Assistance Project Evaluation</p>
    </header>

    <div class="container">
        <!-- Pathway Selector -->
        <div class="pathway-selector">
            <div class="pathway-card" onclick="selectPathway('emergency')">
                <h3>🚨 Emergency Work</h3>
                <p>Categories A & B - Debris Removal & Emergency Protective Measures</p>
            </div>
            <div class="pathway-card selected" onclick="selectPathway('cbcs')">
                <h3>🏗️ CBCS Permanent Work</h3>
                <p>Categories C-G - Infrastructure & Facility Restoration</p>
            </div>
            <div class="pathway-card" onclick="selectPathway('professional')">
                <h3>📋 Professional Analysis</h3>
                <p>Comprehensive Compliance & Regulatory Review</p>
            </div>
        </div>

        <form class="intake-form" id="professionalIntakeForm">
            <!-- Project Information -->
            <section class="form-section">
                <h2 class="section-title">
                    📋 Project Information
                </h2>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Project Name <span class="required">*</span></label>
                        <input type="text" class="form-input" name="projectName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">FEMA Category <span class="required">*</span></label>
                        <select class="form-select" name="femaCategory" required>
                            <option value="">Select Category</option>
                            <option value="A">Category A - Debris Removal</option>
                            <option value="B">Category B - Emergency Protective Measures</option>
                            <option value="C">Category C - Roads and Bridges</option>
                            <option value="D">Category D - Water Control Facilities</option>
                            <option value="E">Category E - Buildings and Equipment</option>
                            <option value="F">Category F - Public Utilities</option>
                            <option value="G">Category G - Parks and Recreation</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Estimated Cost <span class="required">*</span></label>
                        <input type="number" class="form-input" name="estimatedCost" placeholder="$0.00" required>
                        <div class="help-text">Enter total project cost estimate</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Disaster Declaration Date</label>
                        <input type="date" class="form-input" name="disasterDate">
                        <div class="help-text">Determines applicable PAPPG version</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Project Description <span class="required">*</span></label>
                    <textarea class="form-textarea" name="projectDescription" placeholder="Describe the scope of work, damage, and proposed repairs..." required></textarea>
                </div>
            </section>

            <!-- Facility Information -->
            <section class="form-section">
                <h2 class="section-title">
                    🏢 Facility Information
                </h2>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Facility Type <span class="required">*</span></label>
                        <select class="form-select" name="facilityType" required>
                            <option value="">Select Facility Type</option>
                            <option value="building">Building/Structure</option>
                            <option value="bridge">Bridge</option>
                            <option value="road">Road/Highway</option>
                            <option value="water">Water Treatment Facility</option>
                            <option value="wastewater">Wastewater Treatment</option>
                            <option value="electric">Electric Power System</option>
                            <option value="park">Park/Recreation Facility</option>
                            <option value="debris">Debris Removal Site</option>
                            <option value="emergency">Emergency Facility</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Year Built</label>
                        <input type="number" class="form-input" name="yearBuilt" placeholder="YYYY">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Facility Address</label>
                    <input type="text" class="form-input" name="facilityAddress" placeholder="Street address, City, State, ZIP">
                </div>
            </section>

            <!-- Compliance Requirements -->
            <section class="form-section">
                <h2 class="section-title">
                    ⚖️ Compliance Requirements
                </h2>
                
                <div class="form-group">
                    <label class="form-label">Applicable Building Codes <span class="required">*</span></label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="ibc" name="buildingCodes" value="IBC">
                            <label class="checkbox-label" for="ibc">
                                <span class="checkbox-title">International Building Code (IBC)</span>
                                <span class="checkbox-desc">Current edition building standards</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="asce7" name="buildingCodes" value="ASCE7">
                            <label class="checkbox-label" for="asce7">
                                <span class="checkbox-title">ASCE 7 - Minimum Design Loads</span>
                                <span class="checkbox-desc">Wind, seismic, and other load requirements</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="aashto" name="buildingCodes" value="AASHTO">
                            <label class="checkbox-label" for="aashto">
                                <span class="checkbox-title">AASHTO Bridge Design</span>
                                <span class="checkbox-desc">For bridge and highway projects</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="nfpa" name="buildingCodes" value="NFPA">
                            <label class="checkbox-label" for="nfpa">
                                <span class="checkbox-title">NFPA Fire Protection</span>
                                <span class="checkbox-desc">Fire safety and protection standards</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="fema" name="buildingCodes" value="FEMA">
                            <label class="checkbox-label" for="fema">
                                <span class="checkbox-title">FEMA Guidelines</span>
                                <span class="checkbox-desc">Emergency management standards</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="ada" name="buildingCodes" value="ADA">
                            <label class="checkbox-label" for="ada">
                                <span class="checkbox-title">ADA Compliance</span>
                                <span class="checkbox-desc">Accessibility requirements</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Environmental Considerations</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="floodplain" name="environmental" value="floodplain">
                            <label class="checkbox-label" for="floodplain">
                                <span class="checkbox-title">Located in Floodplain</span>
                                <span class="checkbox-desc">Requires flood-resistant design</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="historic" name="environmental" value="historic">
                            <label class="checkbox-label" for="historic">
                                <span class="checkbox-title">Historic Property</span>
                                <span class="checkbox-desc">Section 106 review required</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="wetlands" name="environmental" value="wetlands">
                            <label class="checkbox-label" for="wetlands">
                                <span class="checkbox-title">Near Wetlands</span>
                                <span class="checkbox-desc">Environmental impact assessment</span>
                            </label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="seismic" name="environmental" value="seismic">
                            <label class="checkbox-label" for="seismic">
                                <span class="checkbox-title">Seismic Zone</span>
                                <span class="checkbox-desc">Enhanced structural requirements</span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Information -->
            <section class="form-section">
                <h2 class="section-title">
                    📞 Contact Information
                </h2>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Primary Contact <span class="required">*</span></label>
                        <input type="text" class="form-input" name="contactName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Title/Position</label>
                        <input type="text" class="form-input" name="contactTitle">
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Email <span class="required">*</span></label>
                        <input type="email" class="form-input" name="contactEmail" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Phone</label>
                        <input type="tel" class="form-input" name="contactPhone">
                    </div>
                </div>
            </section>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                    Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    Submit for Analysis
                </button>
            </div>
        </form>
    </div>

    <script>
        function selectPathway(pathway) {
            // Remove selected class from all cards
            document.querySelectorAll('.pathway-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.target.closest('.pathway-card').classList.add('selected');
            
            // Update form based on pathway
            const categorySelect = document.querySelector('select[name="femaCategory"]');
            const facilitySelect = document.querySelector('select[name="facilityType"]');
            
            if (pathway === 'emergency') {
                // Focus on Categories A & B
                categorySelect.innerHTML = `
                    <option value="">Select Category</option>
                    <option value="A">Category A - Debris Removal</option>
                    <option value="B">Category B - Emergency Protective Measures</option>
                `;
                facilitySelect.value = 'debris';
            } else if (pathway === 'cbcs') {
                // Focus on Categories C-G
                categorySelect.innerHTML = `
                    <option value="">Select Category</option>
                    <option value="C">Category C - Roads and Bridges</option>
                    <option value="D">Category D - Water Control Facilities</option>
                    <option value="E">Category E - Buildings and Equipment</option>
                    <option value="F">Category F - Public Utilities</option>
                    <option value="G">Category G - Parks and Recreation</option>
                `;
            } else {
                // All categories for professional analysis
                categorySelect.innerHTML = `
                    <option value="">Select Category</option>
                    <option value="A">Category A - Debris Removal</option>
                    <option value="B">Category B - Emergency Protective Measures</option>
                    <option value="C">Category C - Roads and Bridges</option>
                    <option value="D">Category D - Water Control Facilities</option>
                    <option value="E">Category E - Buildings and Equipment</option>
                    <option value="F">Category F - Public Utilities</option>
                    <option value="G">Category G - Parks and Recreation</option>
                `;
            }
        }

        document.getElementById('professionalIntakeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            alert('Professional intake submitted successfully! You will receive a detailed analysis within 2-3 business days.');
            
            // In real implementation, this would submit to the API
            console.log('Form submitted:', new FormData(this));
        });
        
        // Auto-populate codes based on facility type
        document.querySelector('select[name="facilityType"]').addEventListener('change', function() {
            const facilityType = this.value;
            const checkboxes = document.querySelectorAll('input[name="buildingCodes"]');
            
            // Reset all checkboxes
            checkboxes.forEach(cb => cb.checked = false);
            
            // Auto-select relevant codes
            if (facilityType === 'building') {
                document.getElementById('ibc').checked = true;
                document.getElementById('asce7').checked = true;
                document.getElementById('nfpa').checked = true;
                document.getElementById('ada').checked = true;
            } else if (facilityType === 'bridge' || facilityType === 'road') {
                document.getElementById('aashto').checked = true;
                document.getElementById('asce7').checked = true;
            } else if (facilityType === 'debris' || facilityType === 'emergency') {
                document.getElementById('fema').checked = true;
                document.getElementById('nfpa').checked = true;
            }
        });
        
        // Auto-populate category based on facility type
        document.querySelector('select[name="facilityType"]').addEventListener('change', function() {
            const facilityType = this.value;
            const categorySelect = document.querySelector('select[name="femaCategory"]');
            
            if (facilityType === 'debris') {
                categorySelect.value = 'A';
            } else if (facilityType === 'emergency') {
                categorySelect.value = 'B';
            } else if (facilityType === 'road' || facilityType === 'bridge') {
                categorySelect.value = 'C';
            } else if (facilityType === 'water' || facilityType === 'wastewater') {
                categorySelect.value = 'D';
            } else if (facilityType === 'building') {
                categorySelect.value = 'E';
            } else if (facilityType === 'electric') {
                categorySelect.value = 'F';
            } else if (facilityType === 'park') {
                categorySelect.value = 'G';
            }
        });
    </script>
</body>
</html> 
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Typography,
  Divider,
} from '@mui/material';
import { WorkflowStep } from '../../types/workflow';

interface StepEditorProps {
  step: WorkflowStep;
  onUpdate: (step: WorkflowStep) => void;
}

const StepEditor: React.FC<StepEditorProps> = ({ step, onUpdate }) => {
  const [config, setConfig] = useState(step.config);

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({ ...step, name: event.target.value });
  };

  const handleTypeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onUpdate({
      ...step,
      type: event.target.value as WorkflowStep['type'],
      config: {},
    });
  };

  const handleConfigChange = (field: string, value: any) => {
    const newConfig = { ...config, [field]: value };
    setConfig(newConfig);
    onUpdate({ ...step, config: newConfig });
  };

  const renderActionConfig = () => {
    return (
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Action Type</InputLabel>
            <Select
              value={config.action_type || ''}
              onChange={(e) => handleConfigChange('action_type', e.target.value)}
            >
              <MenuItem value="http">HTTP Request</MenuItem>
              <MenuItem value="database">Database Operation</MenuItem>
              <MenuItem value="file">File Operation</MenuItem>
              <MenuItem value="notification">Notification</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        {config.action_type && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Parameters"
              multiline
              rows={4}
              value={JSON.stringify(config.action_params || {}, null, 2)}
              onChange={(e) => {
                try {
                  const params = JSON.parse(e.target.value);
                  handleConfigChange('action_params', params);
                } catch (error) {
                  // Invalid JSON, keep the current value
                }
              }}
            />
          </Grid>
        )}
      </Grid>
    );
  };

  const renderConditionConfig = () => {
    return (
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Condition Type</InputLabel>
            <Select
              value={config.condition_type || ''}
              onChange={(e) => handleConfigChange('condition_type', e.target.value)}
            >
              <MenuItem value="comparison">Comparison</MenuItem>
              <MenuItem value="regex">Regular Expression</MenuItem>
              <MenuItem value="custom">Custom</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        {config.condition_type && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Parameters"
              multiline
              rows={4}
              value={JSON.stringify(config.condition_params || {}, null, 2)}
              onChange={(e) => {
                try {
                  const params = JSON.parse(e.target.value);
                  handleConfigChange('condition_params', params);
                } catch (error) {
                  // Invalid JSON, keep the current value
                }
              }}
            />
          </Grid>
        )}
      </Grid>
    );
  };

  const renderNotificationConfig = () => {
    return (
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Notification Type</InputLabel>
            <Select
              value={config.notification_type || ''}
              onChange={(e) => handleConfigChange('notification_type', e.target.value)}
            >
              <MenuItem value="email">Email</MenuItem>
              <MenuItem value="slack">Slack</MenuItem>
              <MenuItem value="teams">Microsoft Teams</MenuItem>
              <MenuItem value="webhook">Webhook</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        {config.notification_type && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Parameters"
              multiline
              rows={4}
              value={JSON.stringify(config.notification_params || {}, null, 2)}
              onChange={(e) => {
                try {
                  const params = JSON.parse(e.target.value);
                  handleConfigChange('notification_params', params);
                } catch (error) {
                  // Invalid JSON, keep the current value
                }
              }}
            />
          </Grid>
        )}
      </Grid>
    );
  };

  return (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Step Name"
            value={step.name}
            onChange={handleNameChange}
          />
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Step Type</InputLabel>
            <Select
              value={step.type}
              onChange={handleTypeChange}
            >
              <MenuItem value="action">Action</MenuItem>
              <MenuItem value="condition">Condition</MenuItem>
              <MenuItem value="notification">Notification</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Divider sx={{ my: 2 }} />

      {step.type === 'action' && renderActionConfig()}
      {step.type === 'condition' && renderConditionConfig()}
      {step.type === 'notification' && renderNotificationConfig()}
    </Box>
  );
};

export default StepEditor; 
import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Grid, Card, CardContent } from '@mui/material';
import { useApi } from '../services/api';

const Dashboard: React.FC = () => {
    const [projects, setProjects] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const { getProjects } = useApi();

    useEffect(() => {
        const fetchProjects = async () => {
            try {
                const response = await getProjects();
                setProjects(response);
            } catch (err: any) {
                setError(err.response?.data?.detail || 'Failed to load projects');
            } finally {
                setLoading(false);
            }
        };
        fetchProjects();
    }, []);

    if (loading) return <CircularProgress aria-label="Loading dashboard" />;
    if (error) return <Alert severity="error" role="alert">{error}</Alert>;

    return (
        <Box sx={{ p: 3 }} role="main">
            <Typography variant="h4" gutterBottom>
                Dashboard
            </Typography>
            <Grid container spacing={3}>
                {projects.map((project) => (
                    <Grid item xs={12} sm={6} md={4} key={project.id}>
                        <Card>
                            <CardContent>
                                <Typography variant="h6" component="h2">
                                    {project.name}
                                </Typography>
                                <Typography color="textSecondary">
                                    Status: {project.status}
                                </Typography>
                                <Typography variant="body2">
                                    Start Date: {project.start_date}
                                </Typography>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
};

export default Dashboard;
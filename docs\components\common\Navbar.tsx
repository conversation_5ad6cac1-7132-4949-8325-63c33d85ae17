import React from 'react';
import {
  AppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  Button,
  IconButton,
  Box,
  Menu,
  MenuItem,
  Avatar,
  useTheme,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  KeyboardArrowDown,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const Navbar: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [userMenuAnchor, setUserMenuAnchor] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  return (
    <AppBar 
      position="fixed"
      elevation={0}
      sx={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
      }}
    >
      <Toolbar>
        <IconButton
          edge="start"
          color="inherit"
          aria-label="menu"
          onClick={handleMenu}
          sx={{ mr: 2, display: { sm: 'none' } }}
        >
          <MenuIcon />
        </IconButton>

        <Typography
          variant="h6"
          component="div"
          sx={{
            flexGrow: 1,
            color: '#fff',
            fontWeight: 600,
            cursor: 'pointer',
          }}
          onClick={() => navigate('/')}
        >
          ComplianceMax
        </Typography>

        {/* Desktop Navigation */}
        <Box sx={{ display: { xs: 'none', sm: 'flex' }, gap: 2 }}>
          <Button 
            color="inherit"
            onClick={() => navigate('/quick-check')}
            sx={{ color: '#fff' }}
          >
            Quick Check
          </Button>
          <Button 
            color="inherit"
            onClick={() => navigate('/compliance')}
            sx={{ color: '#fff' }}
          >
            Compliance
          </Button>
          <Button 
            color="inherit"
            onClick={() => navigate('/technical')}
            sx={{ color: '#fff' }}
          >
            Technical
          </Button>
        </Box>

        {/* Notifications */}
        <IconButton 
          color="inherit"
          sx={{ ml: 2, color: '#fff' }}
        >
          <Notifications />
        </IconButton>

        {/* User Menu */}
        <Box sx={{ ml: 2 }}>
          <Button
            onClick={handleUserMenu}
            sx={{
              color: '#fff',
              textTransform: 'none',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.1)',
              },
            }}
            endIcon={<KeyboardArrowDown />}
          >
            <Avatar
              sx={{
                width: 32,
                height: 32,
                mr: 1,
                background: theme.palette.primary.main,
              }}
            >
              U
            </Avatar>
            User
          </Button>
          <Menu
            anchorEl={userMenuAnchor}
            open={Boolean(userMenuAnchor)}
            onClose={handleUserMenuClose}
            sx={{
              '& .MuiPaper-root': {
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: theme.shape.borderRadius,
                minWidth: 200,
              },
            }}
          >
            <MenuItem onClick={() => navigate('/profile')}>Profile</MenuItem>
            <MenuItem onClick={() => navigate('/settings')}>Settings</MenuItem>
            <MenuItem onClick={() => navigate('/logout')}>Logout</MenuItem>
          </Menu>
        </Box>
      </Toolbar>

      {/* Mobile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        sx={{
          '& .MuiPaper-root': {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: theme.shape.borderRadius,
            minWidth: 200,
          },
        }}
      >
        <MenuItem onClick={() => { navigate('/quick-check'); handleClose(); }}>
          Quick Check
        </MenuItem>
        <MenuItem onClick={() => { navigate('/compliance'); handleClose(); }}>
          Compliance
        </MenuItem>
        <MenuItem onClick={() => { navigate('/technical'); handleClose(); }}>
          Technical
        </MenuItem>
      </Menu>
    </AppBar>
  );
};

export default Navbar;

(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[489],{4775:function(e,t,a){Promise.resolve().then(a.bind(a,437))},437:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ComplianceWizardPage}});var r=a(7437),s=a(2265),n=a(4033),i=a(7430),o=a(4702),l=a(1865),d=a(8110),c=a(3449),u=a(734),m=a(5131),x=a(7171),p=a(7730),h=a(4919),g=a(6773),f=a(8539),j=a(8346),v=a(1372),b=a(5253),y=a(1173),N=a(1666),w=a(8879),k=a(3408),C=a(3611),D=a(9598),R=a(877),F=a(708),S=a(7256),P=a(1628),z=a(4323),Z=a(6061);let A=(0,Z.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),M=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(z.f,{ref:t,className:(0,P.cn)(A(),a),...s})});M.displayName=z.f.displayName;let q=l.RV,V=s.createContext({}),FormField=e=>{let{...t}=e;return(0,r.jsx)(V.Provider,{value:{name:t.name},children:(0,r.jsx)(l.Qr,{...t})})},useFormField=()=>{let e=s.useContext(V),t=s.useContext(E),{getFieldState:a,formState:r}=(0,l.Gc)(),n=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},E=s.createContext({}),T=s.forwardRef((e,t)=>{let{className:a,...n}=e,i=s.useId();return(0,r.jsx)(E.Provider,{value:{id:i},children:(0,r.jsx)("div",{ref:t,className:(0,P.cn)("space-y-2",a),...n})})});T.displayName="FormItem";let I=s.forwardRef((e,t)=>{let{className:a,...s}=e,{error:n,formItemId:i}=useFormField();return(0,r.jsx)(M,{ref:t,className:(0,P.cn)(n&&"text-destructive",a),htmlFor:i,...s})});I.displayName="FormLabel";let _=s.forwardRef((e,t)=>{let{...a}=e,{error:s,formItemId:n,formDescriptionId:i,formMessageId:o}=useFormField();return(0,r.jsx)(S.g7,{ref:t,id:n,"aria-describedby":s?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!s,...a})});_.displayName="FormControl";let H=s.forwardRef((e,t)=>{let{className:a,...s}=e,{formDescriptionId:n}=useFormField();return(0,r.jsx)("p",{ref:t,id:n,className:(0,P.cn)("text-sm text-muted-foreground",a),...s})});H.displayName="FormDescription";let B=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e,{error:i,formMessageId:o}=useFormField(),l=i?String(null==i?void 0:i.message):s;return l?(0,r.jsx)("p",{ref:t,id:o,className:(0,P.cn)("text-sm font-medium text-destructive",a),...n,children:l}):null});B.displayName="FormMessage";var Y=a(1),O=a(4457),Q=a(7661);let $=Y.fC;Y.ZA;let G=Y.B4,L=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,r.jsxs)(Y.xz,{ref:t,className:(0,P.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[s,(0,r.jsx)(Y.JO,{asChild:!0,children:(0,r.jsx)(O.Z,{className:"h-4 w-4 opacity-50"})})]})});L.displayName=Y.xz.displayName;let W=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(Y.u_,{ref:t,className:(0,P.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(Q.Z,{className:"h-4 w-4"})})});W.displayName=Y.u_.displayName;let X=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(Y.$G,{ref:t,className:(0,P.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(O.Z,{className:"h-4 w-4"})})});X.displayName=Y.$G.displayName;let U=s.forwardRef((e,t)=>{let{className:a,children:s,position:n="popper",...i}=e;return(0,r.jsx)(Y.h_,{children:(0,r.jsxs)(Y.VY,{ref:t,className:(0,P.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...i,children:[(0,r.jsx)(W,{}),(0,r.jsx)(Y.l_,{className:(0,P.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(X,{})]})})});U.displayName=Y.VY.displayName;let J=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(Y.__,{ref:t,className:(0,P.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})});J.displayName=Y.__.displayName;let K=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,r.jsxs)(Y.ck,{ref:t,className:(0,P.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(Y.wU,{children:(0,r.jsx)(k.Z,{className:"h-4 w-4"})})}),(0,r.jsx)(Y.eT,{children:s})]})});K.displayName=Y.ck.displayName;let ee=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(Y.Z0,{ref:t,className:(0,P.cn)("-mx-1 my-1 h-px bg-muted",a),...s})});ee.displayName=Y.Z0.displayName;var et=a(2355);let ea=c.Ry({applicantType:c.Z_().min(1,"Please select an applicant type"),disasterDeclaration:c.Z_().min(1,"Please select a disaster declaration"),categories:c.IX(c.Z_()).min(1,"Please select at least one category"),estimatedCost:c.Z_().min(1,"Please select an estimated cost")}),er=c.Ry({damageDocumentation:c.Z_().min(1,"Please select an option"),costDocumentation:c.Z_().min(1,"Please select an option"),procurementDocumentation:c.Z_().min(1,"Please select an option")}),es=c.Ry({environmentalReviews:c.Z_().min(1,"Please select an option"),insuranceRequirements:c.Z_().min(1,"Please select an option"),hazardMitigation:c.Z_().min(1,"Please select an option")}),en=c.Ry({dedicatedStaff:c.Z_().min(1,"Please select an option"),documentationManagement:c.Z_().min(1,"Please select an option"),progressTracking:c.Z_().min(1,"Please select an option")});function ComplianceWizardPage(){let e=(0,n.useRouter)(),[t,a]=(0,s.useState)(1),[c,S]=(0,s.useState)({applicantType:"",disasterDeclaration:"",categories:[],estimatedCost:"",damageDocumentation:"",costDocumentation:"",procurementDocumentation:"",environmentalReviews:"",insuranceRequirements:"",hazardMitigation:"",dedicatedStaff:"",documentationManagement:"",progressTracking:""}),[P,z]=(0,o.YD)({triggerOnce:!0,threshold:.1}),[Z,A]=(0,o.YD)({triggerOnce:!0,threshold:.1}),M=(0,l.cI)({resolver:(0,d.F)(ea),defaultValues:{applicantType:c.applicantType,disasterDeclaration:c.disasterDeclaration,categories:c.categories,estimatedCost:c.estimatedCost}}),V=(0,l.cI)({resolver:(0,d.F)(er),defaultValues:{damageDocumentation:c.damageDocumentation,costDocumentation:c.costDocumentation,procurementDocumentation:c.procurementDocumentation}}),E=(0,l.cI)({resolver:(0,d.F)(es),defaultValues:{environmentalReviews:c.environmentalReviews,insuranceRequirements:c.insuranceRequirements,hazardMitigation:c.hazardMitigation}}),Y=(0,l.cI)({resolver:(0,d.F)(en),defaultValues:{dedicatedStaff:c.dedicatedStaff,documentationManagement:c.documentationManagement,progressTracking:c.progressTracking}}),O=(t-1)/4*100,handleNext=e=>{S({...c,...e}),a(t+1),window.scrollTo(0,0)},handleBack=()=>{a(t-1),window.scrollTo(0,0)},Q=(()=>{let e=[];return("No documentation yet"===c.damageDocumentation||"Limited documentation available"===c.damageDocumentation)&&e.push({title:"Damage Documentation",description:"Develop comprehensive damage documentation with photos and detailed assessments",priority:"Critical",icon:(0,r.jsx)(u.Z,{className:"h-5 w-5 text-destructive"})}),("No documentation yet"===c.costDocumentation||"Partial documentation available"===c.costDocumentation)&&e.push({title:"Cost Documentation",description:"Implement systematic cost tracking and documentation for all project expenses",priority:"Critical",icon:(0,r.jsx)(m.Z,{className:"h-5 w-5 text-destructive"})}),("No procurement documentation"===c.procurementDocumentation||"Limited procurement documentation"===c.procurementDocumentation)&&e.push({title:"Procurement Documentation",description:"Establish procurement procedures that comply with federal requirements",priority:"High",icon:(0,r.jsx)(x.Z,{className:"h-5 w-5 text-warning"})}),("Reviews not started"===c.environmentalReviews||"Not sure if reviews are required"===c.environmentalReviews)&&e.push({title:"Environmental Reviews",description:"Initiate environmental and historic preservation reviews immediately",priority:"High",icon:(0,r.jsx)(p.Z,{className:"h-5 w-5 text-warning"})}),("Insurance requirements not identified"===c.insuranceRequirements||"Not sure about insurance requirements"===c.insuranceRequirements)&&e.push({title:"Insurance Documentation",description:"Identify and address all insurance requirements for FEMA compliance",priority:"Medium",icon:(0,r.jsx)(h.Z,{className:"h-5 w-5 text-primary"})}),"No dedicated staff"===c.dedicatedStaff&&e.push({title:"Dedicated Staff",description:"Assign dedicated personnel to manage FEMA Public Assistance projects",priority:"Medium",icon:(0,r.jsx)(g.Z,{className:"h-5 w-5 text-primary"})}),e.length<3&&(e.push({title:"Documentation System",description:"Implement a centralized documentation system for all FEMA-related records",priority:"Medium",icon:(0,r.jsx)(x.Z,{className:"h-5 w-5 text-primary"})}),e.push({title:"Regular Progress Reporting",description:"Establish regular progress reporting to track project milestones and deadlines",priority:"Medium",icon:(0,r.jsx)(f.Z,{className:"h-5 w-5 text-primary"})})),e})();return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)(i.E.div,{ref:P,initial:{opacity:0,y:-20},animate:z?{opacity:1,y:0}:{},transition:{duration:.5},className:"bg-gradient-to-r from-blue-600 to-indigo-600 p-8 rounded-lg shadow-md",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:"FEMA Compliance Wizard"}),(0,r.jsx)("p",{className:"text-blue-100 mt-2",children:"Complete this wizard to assess your compliance readiness and get personalized recommendations"}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-blue-100 text-sm mb-2",children:[(0,r.jsxs)("span",{children:["Step ",t," of ",5]}),(0,r.jsxs)("span",{children:[Math.round(O),"% Complete"]})]}),(0,r.jsx)(R.E,{value:O,className:"h-2 bg-blue-400/30"})]})]}),(0,r.jsxs)(i.E.div,{ref:Z,initial:{opacity:0,y:20},animate:A?{opacity:1,y:0}:{},transition:{duration:.5,delay:.2},className:"bg-card rounded-lg border shadow-sm p-6",children:[1===t&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full",children:(0,r.jsx)(x.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold tracking-tight",children:"Project Information"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Tell us about your Public Assistance project"})]})]}),(0,r.jsx)(q,{...M,children:(0,r.jsxs)("form",{onSubmit:M.handleSubmit(handleNext),className:"space-y-6",children:[(0,r.jsx)(FormField,{control:M.control,name:"applicantType",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Applicant Type"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select applicant type"})})}),(0,r.jsxs)(U,{children:[(0,r.jsx)(K,{value:"state",children:"State Government"}),(0,r.jsx)(K,{value:"local",children:"Local Government"}),(0,r.jsx)(K,{value:"tribal",children:"Tribal Government"}),(0,r.jsx)(K,{value:"pnp",children:"Private Non-Profit"})]})]}),(0,r.jsx)(H,{children:"Select the type of entity applying for FEMA Public Assistance"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:M.control,name:"disasterDeclaration",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Disaster Declaration"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select disaster declaration"})})}),(0,r.jsxs)(U,{children:[(0,r.jsx)(K,{value:"DR-4611",children:"DR-4611: Hurricane (2023)"}),(0,r.jsx)(K,{value:"DR-4582",children:"DR-4582: Severe Storms (2023)"}),(0,r.jsx)(K,{value:"DR-4547",children:"DR-4547: COVID-19 Pandemic (2020)"}),(0,r.jsx)(K,{value:"other",children:"Other Declaration"})]})]}),(0,r.jsx)(H,{children:"Select the FEMA disaster declaration number"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:M.control,name:"categories",render:()=>{var e;return(0,r.jsxs)(T,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)(I,{children:"FEMA Categories"}),(0,r.jsx)(H,{children:"Select all applicable FEMA Public Assistance categories"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:et.Yi.map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{children:(0,r.jsx)("input",{type:"checkbox",id:e.id,checked:c.categories.includes(e.id),onChange:t=>{let a=t.target.checked?[...c.categories,e.id]:c.categories.filter(t=>t!==e.id);S({...c,categories:a}),M.setValue("categories",a,{shouldValidate:!0})},className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-gray-900 dark:text-gray-100 block",children:e.name}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-xs mt-1",children:e.description})]})]},e.id))}),(0,r.jsx)(B,{className:"mt-2",children:null===(e=M.formState.errors.categories)||void 0===e?void 0:e.message})]})}}),(0,r.jsx)(FormField,{control:M.control,name:"estimatedCost",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Estimated Project Cost"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select estimated cost range"})})}),(0,r.jsxs)(U,{children:[(0,r.jsx)(K,{value:"small",children:"Small Project (Under $139,800)"}),(0,r.jsx)(K,{value:"large",children:"Large Project ($139,800 - $1 million)"}),(0,r.jsx)(K,{value:"verylarge",children:"Very Large Project (Over $1 million)"})]})]}),(0,r.jsx)(H,{children:"Select the estimated total cost range for your project"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)("div",{className:"flex justify-end space-x-2",children:(0,r.jsxs)(C.z,{type:"button",className:"bg-blue-600 hover:bg-blue-700",onClick:()=>{let e=M.getValues();handleNext(e)},children:["Next",(0,r.jsx)(j.Z,{className:"ml-2 h-4 w-4"})]})})]})})]}),2===t&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full",children:(0,r.jsx)(m.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold tracking-tight",children:"Documentation Assessment"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Evaluate your current documentation status"})]})]}),(0,r.jsx)(q,{...V,children:(0,r.jsxs)("form",{onSubmit:V.handleSubmit(handleNext),className:"space-y-6",children:[(0,r.jsx)(FormField,{control:V.control,name:"damageDocumentation",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Damage Documentation Status"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select documentation status"})})}),(0,r.jsx)(U,{children:et.Xi.documentation.map(e=>(0,r.jsx)(K,{value:e.options[0],children:e.options[0]},e.id))})]}),(0,r.jsx)(H,{children:"How well are your damages documented with photos, descriptions, and assessments?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:V.control,name:"costDocumentation",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Cost Documentation Status"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select cost documentation status"})})}),(0,r.jsx)(U,{children:et.Xi.documentation.map(e=>(0,r.jsx)(K,{value:e.options[1],children:e.options[1]},e.id))})]}),(0,r.jsx)(H,{children:"How complete is your cost tracking and documentation?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:V.control,name:"procurementDocumentation",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Procurement Documentation Status"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select procurement status"})})}),(0,r.jsx)(U,{children:et.Xi.documentation.map(e=>(0,r.jsx)(K,{value:e.options[2],children:e.options[2]},e.id))})]}),(0,r.jsx)(H,{children:"How well documented are your procurement processes for this project?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsxs)("div",{className:"flex justify-between space-x-2",children:[(0,r.jsxs)(C.z,{type:"button",variant:"outline",onClick:handleBack,className:"border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50",children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,r.jsxs)(C.z,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",children:["Next",(0,r.jsx)(j.Z,{className:"ml-2 h-4 w-4"})]})]})]})})]}),3===t&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full",children:(0,r.jsx)(b.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold tracking-tight",children:"Compliance Requirements"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Evaluate key compliance areas for your project"})]})]}),(0,r.jsx)(q,{...E,children:(0,r.jsxs)("form",{onSubmit:E.handleSubmit(handleNext),className:"space-y-6",children:[(0,r.jsx)(FormField,{control:E.control,name:"environmentalReviews",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Environmental & Historic Preservation Reviews"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select review status"})})}),(0,r.jsx)(U,{children:et.Xi.compliance.map(e=>(0,r.jsx)(K,{value:e.options[0],children:e.options[0]},e.id))})]}),(0,r.jsx)(H,{children:"What is the status of environmental and historic preservation reviews?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:E.control,name:"insuranceRequirements",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Insurance Requirements"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select insurance status"})})}),(0,r.jsx)(U,{children:et.Xi.compliance.map(e=>(0,r.jsx)(K,{value:e.options[1],children:e.options[1]},e.id))})]}),(0,r.jsx)(H,{children:"Have you identified and addressed all insurance requirements?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:E.control,name:"hazardMitigation",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Hazard Mitigation"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select mitigation status"})})}),(0,r.jsx)(U,{children:et.Xi.compliance.map(e=>(0,r.jsx)(K,{value:e.options[2],children:e.options[2]},e.id))})]}),(0,r.jsx)(H,{children:"Have you addressed hazard mitigation opportunities in your project?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsxs)("div",{className:"flex justify-between space-x-2",children:[(0,r.jsxs)(C.z,{type:"button",variant:"outline",onClick:handleBack,className:"border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50",children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,r.jsxs)(C.z,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",children:["Next",(0,r.jsx)(j.Z,{className:"ml-2 h-4 w-4"})]})]})]})})]}),4===t&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full",children:(0,r.jsx)(y.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold tracking-tight",children:"Project Management"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Assess project management resources and capabilities"})]})]}),(0,r.jsx)(q,{...Y,children:(0,r.jsxs)("form",{onSubmit:Y.handleSubmit(handleNext),className:"space-y-6",children:[(0,r.jsx)(FormField,{control:Y.control,name:"dedicatedStaff",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Dedicated Staff"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select staff availability"})})}),(0,r.jsxs)(U,{children:[(0,r.jsx)(K,{value:"Full-time dedicated staff",children:"Full-time dedicated staff"}),(0,r.jsx)(K,{value:"Part-time dedicated staff",children:"Part-time dedicated staff"}),(0,r.jsx)(K,{value:"Staff assigned multiple duties",children:"Staff assigned multiple duties"}),(0,r.jsx)(K,{value:"No dedicated staff",children:"No dedicated staff"})]})]}),(0,r.jsx)(H,{children:"Do you have dedicated personnel assigned to manage this project?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:Y.control,name:"documentationManagement",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Documentation Management"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select documentation system"})})}),(0,r.jsxs)(U,{children:[(0,r.jsx)(K,{value:"Specialized compliance system",children:"Specialized compliance system"}),(0,r.jsx)(K,{value:"Document management system",children:"Document management system"}),(0,r.jsx)(K,{value:"Basic file organization",children:"Basic file organization"}),(0,r.jsx)(K,{value:"No systematic approach",children:"No systematic approach"})]})]}),(0,r.jsx)(H,{children:"How do you manage project documentation?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsx)(FormField,{control:Y.control,name:"progressTracking",render:e=>{let{field:t}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(I,{children:"Progress Tracking"}),(0,r.jsxs)($,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,r.jsx)(_,{children:(0,r.jsx)(L,{children:(0,r.jsx)(G,{placeholder:"Select tracking method"})})}),(0,r.jsxs)(U,{children:[(0,r.jsx)(K,{value:"Detailed project management system",children:"Detailed project management system"}),(0,r.jsx)(K,{value:"Regular status meetings and reports",children:"Regular status meetings and reports"}),(0,r.jsx)(K,{value:"Basic milestone tracking",children:"Basic milestone tracking"}),(0,r.jsx)(K,{value:"No formal tracking",children:"No formal tracking"})]})]}),(0,r.jsx)(H,{children:"How do you track project progress and deadlines?"}),(0,r.jsx)(B,{})]})}}),(0,r.jsxs)("div",{className:"flex justify-between space-x-2",children:[(0,r.jsxs)(C.z,{type:"button",variant:"outline",onClick:handleBack,className:"border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50",children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,r.jsxs)(C.z,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",children:["Next",(0,r.jsx)(j.Z,{className:"ml-2 h-4 w-4"})]})]})]})})]}),5===t&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full",children:(0,r.jsx)(N.Z,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold tracking-tight",children:"Summary & Recommendations"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:"Review your assessment and get personalized recommendations"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Project Overview"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Applicant Type:"})," ",(0,r.jsx)("span",{className:"text-gray-900 dark:text-gray-200",children:c.applicantType||"Not specified"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Disaster Declaration:"})," ",(0,r.jsx)("span",{className:"text-gray-900 dark:text-gray-200",children:c.disasterDeclaration||"Not specified"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Categories:"})," ",(0,r.jsx)("span",{className:"text-gray-900 dark:text-gray-200",children:c.categories.length>0?c.categories.join(", "):"None selected"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Estimated Cost:"})," ",(0,r.jsx)("span",{className:"text-gray-900 dark:text-gray-200",children:c.estimatedCost||"Not specified"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Recommended Actions"}),(0,r.jsx)("div",{className:"space-y-4",children:Q.map((e,t)=>(0,r.jsxs)(D.Zb,{children:[(0,r.jsx)(D.Ol,{className:"py-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon,(0,r.jsxs)("div",{children:[(0,r.jsx)(D.ll,{className:"text-base",children:e.title}),(0,r.jsxs)(F.C,{variant:"Critical"===e.priority?"destructive":"High"===e.priority?"warning":"secondary",className:"mt-1",children:[e.priority," Priority"]})]})]})}),(0,r.jsx)(D.aY,{className:"py-3 text-sm text-gray-600 dark:text-gray-300",children:e.description})]},t))})]}),(0,r.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("div",{className:"p-1.5 bg-blue-100 dark:bg-blue-800 rounded-full",children:(0,r.jsx)(w.Z,{className:"h-4 w-4 text-blue-700 dark:text-blue-300"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-blue-700 dark:text-blue-300 text-sm",children:"Next Steps"}),(0,r.jsx)("p",{className:"text-blue-600 dark:text-blue-400 text-xs mt-1",children:"Complete your compliance improvement plan by addressing the recommendations above. Download your custom report and track your progress in the dashboard."})]})]})}),(0,r.jsxs)("div",{className:"flex justify-between space-x-2",children:[(0,r.jsxs)(C.z,{type:"button",variant:"outline",onClick:handleBack,className:"border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50",children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Back"]}),(0,r.jsxs)("div",{className:"space-x-2",children:[(0,r.jsxs)(C.z,{type:"button",variant:"outline",onClick:()=>console.log("Download report"),className:"border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50",children:[(0,r.jsx)(x.Z,{className:"mr-2 h-4 w-4"}),"Download Report"]}),(0,r.jsxs)(C.z,{type:"button",onClick:()=>{console.log("Final form data:",c),e.push("/dashboard")},className:"bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(k.Z,{className:"mr-2 h-4 w-4"}),"Complete"]})]})]})]})]})]})]})}},708:function(e,t,a){"use strict";a.d(t,{C:function(){return Badge}});var r=a(7437);a(2265);var s=a(6061),n=a(1628);let i=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Badge(e){let{className:t,variant:a,...s}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:a}),t),...s})}},3611:function(e,t,a){"use strict";a.d(t,{z:function(){return l}});var r=a(7437),s=a(2265),n=a(6061),i=a(1628);let o=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,t)=>{let{className:a,variant:s,size:n,asChild:l=!1,...d}=e;return(0,r.jsx)("button",{className:(0,i.cn)(o({variant:s,size:n,className:a})),ref:t,...d})});l.displayName="Button"},9598:function(e,t,a){"use strict";a.d(t,{Ol:function(){return o},Zb:function(){return i},aY:function(){return c},ll:function(){return l}});var r=a(7437),s=a(2265),n=a(1628);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm transition-all hover:shadow-md card-hover-effect",a),...s})});i.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...s})});o.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight font-heading",a),...s})});l.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...s})});c.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...s})});u.displayName="CardFooter"},877:function(e,t,a){"use strict";a.d(t,{E:function(){return o}});var r=a(7437),s=a(2265),n=a(6828),i=a(1628);let o=s.forwardRef((e,t)=>{let{className:a,value:s,indicatorClassName:o,...l}=e;return(0,r.jsx)(n.fC,{ref:t,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...l,children:(0,r.jsx)(n.z$,{className:(0,i.cn)("h-full w-full flex-1 bg-primary transition-all",o),style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});o.displayName=n.fC.displayName},2355:function(e,t,a){"use strict";a.d(t,{Hw:function(){return i},Xi:function(){return s},Yi:function(){return r},_H:function(){return o},v:function(){return n}});let r=[{id:"CAT001",name:"Category A - Debris Removal",description:"Clearance of debris from public roads and spaces"},{id:"CAT002",name:"Category B - Emergency Protective Measures",description:"Actions taken to save lives and protect property"},{id:"CAT003",name:"Category C - Roads and Bridges",description:"Repair of roads, bridges, and associated features"},{id:"CAT004",name:"Category D - Water Control Facilities",description:"Repair of dams, levees, drainage channels, and similar facilities"},{id:"CAT005",name:"Category E - Buildings and Equipment",description:"Repair or replacement of public buildings and equipment"},{id:"CAT006",name:"Category F - Utilities",description:"Repair of water treatment plants, power generation facilities, and similar utilities"},{id:"CAT007",name:"Category G - Parks & Recreation",description:"Repair of parks, recreational areas, and other facilities"}],s={general:[{id:"Q001",question:"What type of applicant are you?",options:["State Government","Local Government","Tribal Government","Private Non-Profit Organization","Other"]},{id:"Q002",question:"What is the disaster declaration number for your project?",options:["DR-4339-PR","DR-4340-VI","DR-4335-VI","DR-4336-PR","DR-4337-FL","DR-4338-TX","Other/Not Sure"]},{id:"Q003",question:"Which FEMA Public Assistance categories apply to your project?",options:["Category A - Debris Removal","Category B - Emergency Protective Measures","Category C - Roads and Bridges","Category D - Water Control Facilities","Category E - Buildings and Equipment","Category F - Utilities","Category G - Parks & Recreation"],multiSelect:!0},{id:"Q004",question:"What is the estimated cost of your project?",options:["Less than $131,100 (Small Project)","$131,100 - $1,000,000","$1,000,001 - $5,000,000","$5,000,001 - $10,000,000","More than $10,000,000"]}],documentation:[{id:"Q005",question:"Do you have detailed damage documentation?",options:["Yes, comprehensive documentation with photos and assessments","Yes, but documentation is partial","Limited documentation available","No documentation yet","Not sure what documentation is needed"]},{id:"Q006",question:"Do you have cost documentation (invoices, labor records, etc.)?",options:["Yes, comprehensive and organized","Yes, but not fully organized","Partial documentation available","No documentation yet","Not sure what documentation is needed"]},{id:"Q007",question:"Do you have procurement documentation for contracts?",options:["Yes, followed all federal procurement requirements","Yes, followed some procurement requirements","Limited procurement documentation","No procurement documentation","Not applicable (no contracts)"]}],compliance:[{id:"Q008",question:"Have you completed environmental and historic preservation reviews?",options:["Yes, all reviews completed and documented","Some reviews completed","Reviews in progress","Reviews not started","Not sure if reviews are required"]},{id:"Q009",question:"Have you identified and addressed insurance requirements?",options:["Yes, all insurance documentation is complete","Insurance documentation is in progress","Insurance requirements identified but not addressed","Insurance requirements not identified","Not sure about insurance requirements"]},{id:"Q010",question:"Have you addressed hazard mitigation opportunities?",options:["Yes, hazard mitigation measures identified and documented","Hazard mitigation opportunities identified but not documented","Hazard mitigation assessment in progress","Hazard mitigation not considered","Not sure about hazard mitigation opportunities"]}],management:[{id:"Q011",question:"Do you have dedicated staff for managing FEMA Public Assistance?",options:["Yes, dedicated team with FEMA experience","Yes, dedicated staff but limited FEMA experience","Staff assigned as additional duty","No dedicated staff","Using external consultants"]},{id:"Q012",question:"How do you manage project documentation?",options:["Centralized electronic system specific to FEMA requirements","General document management system","Organized file structure but not electronic","Ad-hoc documentation management","No systematic documentation management"]},{id:"Q013",question:"How do you track project progress and deadlines?",options:["Dedicated project management system with FEMA milestones","General project management system","Spreadsheet tracking","Manual tracking","No formal tracking system"]}]},n={approved:42,pending:15,rejected:8,total:65},i=[{id:1,action:"Project status updated",project:"Hurricane Relief Infrastructure",user:"Alex Johnson",timestamp:new Date(2023,6,15,14,30),status:"Approved"},{id:2,action:"Document uploaded",project:"Flood Mitigation Program",user:"Maria Garcia",timestamp:new Date(2023,6,15,11,45),document:"Damage Assessment Report v2.1"},{id:3,action:"Request for Information (RFI) submitted",project:"Emergency Protective Measures",user:"David Kim",timestamp:new Date(2023,6,14,16,20),task:"Additional documentation for labor costs"},{id:4,action:"Eligibility review completed",project:"Debris Removal Project",user:"Sarah Williams",timestamp:new Date(2023,6,14,10,15),result:"3 critical findings"},{id:5,action:"New compliance requirement added",project:"Public Building Restoration",user:"James Taylor",timestamp:new Date(2023,6,13,15,30),requirement:"Quarterly progress reporting"}],o=[{month:"Jan",approved:30,pending:10,rejected:5},{month:"Feb",approved:32,pending:12,rejected:6},{month:"Mar",approved:35,pending:10,rejected:5},{month:"Apr",approved:38,pending:8,rejected:4},{month:"May",approved:40,pending:10,rejected:5},{month:"Jun",approved:42,pending:15,rejected:8}]},1628:function(e,t,a){"use strict";a.d(t,{cn:function(){return cn},o0:function(){return formatDateTime},z2:function(){return getStatusColor}});var r=a(348),s=a(3986);function cn(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.m)((0,r.W)(t))}function formatDateTime(e){let t=new Date(e);return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"numeric",minute:"numeric"}).format(t)}function getStatusColor(e){switch(null==e?void 0:e.toLowerCase()){case"approved":return"success";case"pending":return"warning";case"rejected":return"destructive";case"in progress":return"secondary";default:return"default"}}}},function(e){e.O(0,[100,735,617,802,971,472,744],function(){return e(e.s=4775)}),_N_E=e.O()}]);
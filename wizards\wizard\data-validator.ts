#!/usr/bin/env ts-node

import fs from 'fs';
import path from 'path';

// ================= File Discovery =================

function findDataFiles(): { path: string; size: number; type: string }[] {
  const searchDirs = [
    path.resolve(process.cwd(), 'DOCS'),
    path.resolve(process.cwd(), 'DOCS', 'source_data'),
    path.resolve(process.cwd(), 'ALL NEW APP', 'data'),
    path.resolve(process.cwd(), 'wizard'),
  ];

  const files: { path: string; size: number; type: string }[] = [];

  for (const dir of searchDirs) {
    if (!fs.existsSync(dir)) continue;
    
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isFile()) {
        const filePath = path.join(dir, entry.name);
        const stats = fs.statSync(filePath);
        const ext = path.extname(entry.name).toLowerCase();
        
        if (['.json', '.xlsx', '.csv', '.md'].includes(ext)) {
          files.push({
            path: filePath,
            size: stats.size,
            type: ext.slice(1),
          });
        }
      }
    }
  }

  return files.sort((a, b) => b.size - a.size);
}

// ================= Validation Functions =================

async function validateJson(filePath: string): Promise<{
  valid: boolean;
  error?: string;
  structure?: string;
  itemCount?: number;
  schema?: string;
}> {
  try {
    const content = await fs.promises.readFile(filePath, 'utf-8');
    const data = JSON.parse(content);
    
    let structure = 'unknown';
    let itemCount = 0;
    let schema = 'unknown';
    
    if (Array.isArray(data)) {
      structure = 'array';
      itemCount = data.length;
      
      if (data.length > 0) {
        const firstItem = data[0];
        if (firstItem.trigger_condition_if || firstItem.action_required_then) {
          schema = 'checklist';
        } else if (firstItem.questions) {
          schema = 'wizard';
        }
      }
    } else if (typeof data === 'object' && data !== null) {
      structure = 'object';
      const keys = Object.keys(data);
      itemCount = keys.length;
      
      // Check if it's grouped data
      const firstValue = data[keys[0]];
      if (Array.isArray(firstValue)) {
        structure = 'grouped-object';
        itemCount = keys.reduce((sum, key) => sum + (Array.isArray(data[key]) ? data[key].length : 0), 0);
        schema = 'checklist-grouped';
      }
    }
    
    return {
      valid: true,
      structure,
      itemCount,
      schema,
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message,
    };
  }
}

// ================= Main Validation Logic =================

async function main() {
  console.log('🔍 ComplianceMax Data Validator (Simplified)');
  console.log('============================================');
  
  try {
    // Discover all data files
    console.log('📂 Discovering data files...');
    const files = findDataFiles();
    console.log(`Found ${files.length} data files`);
    
    if (files.length === 0) {
      console.log('❌ No data files found!');
      process.exit(1);
    }
    
    // Create output directory
    const outputDir = path.resolve(process.cwd(), 'validation-results');
    await fs.promises.mkdir(outputDir, { recursive: true });
    
    // Validate each file
    const results = [];
    let hasErrors = false;
    
    for (let i = 0; i < Math.min(files.length, 20); i++) { // Limit to first 20 files
      const file = files[i];
      const fileName = path.basename(file.path);
      console.log(`\n[${i + 1}] Validating: ${fileName}`);
      
      const result = {
        path: file.path,
        size: file.size,
        type: file.type,
        validation: file.type === 'json' ? await validateJson(file.path) : { valid: true, schema: 'non-json' },
      };
      
      results.push(result);
      
      // Status indicator
      if (result.validation.valid) {
        console.log(`   ✅ Valid - ${result.validation.itemCount || 'N/A'} items (${result.validation.schema})`);
      } else {
        console.log(`   ❌ Invalid - ${result.validation.error}`);
        hasErrors = true;
      }
    }
    
    // Generate summary
    console.log('\n📊 Validation Summary:');
    console.log(`   Total files checked: ${results.length}`);
    console.log(`   Valid files: ${results.filter(r => r.validation.valid).length}`);
    console.log(`   Invalid files: ${results.filter(r => !r.validation.valid).length}`);
    
    const totalSize = results.reduce((sum, r) => sum + r.size, 0);
    console.log(`   Total data size: ${Math.round(totalSize / 1024 / 1024)}MB`);
    
    // Generate simple report
    const reportPath = path.join(outputDir, 'validation-summary.json');
    await fs.promises.writeFile(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Summary saved: ${reportPath}`);
    
    // Exit with error code if any files are invalid
    if (hasErrors) {
      console.log('\n⚠️  Some files have validation errors');
      process.exit(1);
    } else {
      console.log('\n✅ All checked files are valid');
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
} 
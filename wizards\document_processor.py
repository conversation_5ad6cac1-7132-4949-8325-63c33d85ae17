"""
Enhanced Document Processor for ComplianceMax

This module implements an improved document processing service that integrates the
advanced features from the refactored policy matcher implementation, including
parallel processing, caching, and optimized text extraction.
"""

import os
import logging
import time
import asyncio
from concurrent.futures import ProcessPoolExecutor
from typing import Dict, List, Optional, Any
import re
import hashlib

logger = logging.getLogger(__name__)

# Configuration constants
MAX_WORKERS = 4
MAX_FILE_SIZE_MB = 50
CACHE_TTL = 3600  # 1 hour
SKIP_FILE_EXTENSIONS = ['exe', 'dll', 'zip', 'rar', 'tar', 'gz', 'jpg', 'jpeg', 'png', 'gif', 'mp3', 'mp4']


class DocumentProcessor:
    """
    Enhanced document processor with optimized text extraction, caching, and parallel processing.
    """
    
    def __init__(self, use_cache: bool = True):
        """
        Initialize the document processor.
        
        Args:
            use_cache: Whether to use caching for extracted text
        """
        self.use_cache = use_cache
        
        # Create cache directory if needed
        if self.use_cache:
            os.makedirs("cache", exist_ok=True)
    
    async def process_document_text(self, text: str) -> Dict[str, Any]:
        """
        Process document text directly without file extraction.
        
        Args:
            text: Document text to process
            
        Returns:
            Dictionary with analysis results
        """
        try:
            start_time = time.time()
            logger.info("Processing document text")
            
            # Generate cache key
            text_hash = hashlib.md5(text.encode()).hexdigest()
            cache_key = f"text_process:{text_hash}"
            
            # Extract metadata (limited without file info)
            metadata = {
                "text_length": len(text),
                "word_count": len(text.split())
            }
            
            # Extract requirements
            requirements = self._extract_requirements(text)
            
            # Extract temporal information
            temporal_info = self._extract_temporal_info(text)
            
            # Analyze document structure
            structure = self._analyze_structure(text)
            
            # Combine results
            results = {
                "metadata": metadata,
                "text": text,
                "requirements": requirements,
                "temporal_info": temporal_info,
                "structure": structure
            }
            
            processing_time = time.time() - start_time
            logger.info(f"Text processing completed in {processing_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing document text: {str(e)}")
            raise
    
    def _extract_requirements(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract requirements from text.
        
        Args:
            text: Document text
            
        Returns:
            List of extracted requirements
        """
        requirements = []
        
        try:
            # Compile regex patterns for requirement indicators
            requirement_patterns = [
                re.compile(r'\bmust\s+', re.I),
                re.compile(r'\bshall\s+', re.I),
                re.compile(r'\brequired\s+to\s+', re.I),
                re.compile(r'\brequirements?\s+include\s+', re.I),
                re.compile(r'\bshould\s+', re.I),
                re.compile(r'\bnecessary\s+to\s+', re.I)
            ]
            
            # Split text into sentences
            sentences = re.split(r'[.!?]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            for i, sent in enumerate(sentences):
                # Skip very short sentences
                if len(sent) < 10:
                    continue
                
                # Check for requirement patterns
                is_requirement = False
                for pattern in requirement_patterns:
                    if pattern.search(sent):
                        is_requirement = True
                        break
                
                if is_requirement:
                    # Try to determine category
                    category = "General"
                    if "cost" in sent.lower() or "price" in sent.lower() or "budget" in sent.lower():
                        category = "Cost"
                    elif "schedule" in sent.lower() or "deadline" in sent.lower() or "timeline" in sent.lower():
                        category = "Schedule"
                    elif "quality" in sent.lower() or "standard" in sent.lower() or "specification" in sent.lower():
                        category = "Quality"
                    elif "document" in sent.lower() or "report" in sent.lower() or "record" in sent.lower():
                        category = "Documentation"
                    elif "compliance" in sent.lower() or "regulation" in sent.lower() or "law" in sent.lower():
                        category = "Compliance"
                    
                    # Determine priority based on language
                    priority = "medium"
                    if "must" in sent.lower() or "shall" in sent.lower() or "required" in sent.lower():
                        priority = "high"
                    elif "should" in sent.lower() or "recommended" in sent.lower():
                        priority = "medium"
                    elif "may" in sent.lower() or "optional" in sent.lower():
                        priority = "low"
                    
                    requirements.append({
                        "id": f"REQ-{i+1:03d}",
                        "text": sent,
                        "category": category,
                        "priority": priority,
                        "type": "requirement"
                    })
            
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting requirements: {str(e)}")
            return []
    
    def _extract_temporal_info(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract temporal information from text.
        
        Args:
            text: Document text
            
        Returns:
            List of extracted temporal information
        """
        temporal_info = []
        
        try:
            # Compile regex pattern for dates
            date_pattern = re.compile(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b')
            
            # Split text into sentences
            sentences = re.split(r'[.!?]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            for i, sent in enumerate(sentences):
                # Skip very short sentences
                if len(sent) < 10:
                    continue
                
                # Extract dates from this sentence
                dates = date_pattern.findall(sent)
                if dates:
                    for date in dates:
                        temporal_info.append({
                            "id": f"DATE-{i+1:03d}",
                            "date": date,
                            "context": sent,
                            "type": "temporal"
                        })
            
            return temporal_info
            
        except Exception as e:
            logger.error(f"Error extracting temporal information: {str(e)}")
            return []
    
    def _analyze_structure(self, text: str) -> Dict[str, Any]:
        """
        Analyze document structure.
        
        Args:
            text: Document text
            
        Returns:
            Dictionary with document structure analysis
        """
        try:
            # Split text into lines
            lines = text.split('\n')
            
            # Identify potential headings
            heading_patterns = [
                re.compile(r'^\s*\d+\.\s+[A-Z]'),  # Numbered headings (1. TITLE)
                re.compile(r'^\s*[A-Z][A-Z\s]+$'),  # ALL CAPS headings
                re.compile(r'^\s*[A-Z][a-z]+\s+\d+'),  # Section/Article headings (Section 1)
                re.compile(r'^\s*[IVX]+\.\s+'),  # Roman numeral headings (IV. Title)
            ]
            
            headings = []
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                    
                is_heading = False
                for pattern in heading_patterns:
                    if pattern.match(line):
                        is_heading = True
                        break
                
                if is_heading and len(line) < 100:  # Avoid treating paragraphs as headings
                    headings.append({
                        "line_number": i,
                        "text": line,
                        "level": 1 if line.isupper() else 2
                    })
            
            # Identify sections based on headings
            sections = []
            for i in range(len(headings)):
                start_line = headings[i]["line_number"] + 1
                end_line = headings[i+1]["line_number"] if i < len(headings) - 1 else len(lines)
                
                section_text = "\n".join(lines[start_line:end_line]).strip()
                if section_text:
                    sections.append({
                        "id": f"SEC-{i+1:03d}",
                        "heading": headings[i]["text"],
                        "text": section_text,
                        "level": headings[i]["level"]
                    })
            
            # If no sections were found, create artificial sections based on paragraphs
            if not sections:
                paragraphs = []
                current_paragraph = []
                
                for line in lines:
                    if line.strip():
                        current_paragraph.append(line)
                    elif current_paragraph:
                        paragraphs.append("\n".join(current_paragraph))
                        current_paragraph = []
                
                if current_paragraph:
                    paragraphs.append("\n".join(current_paragraph))
                
                # Group paragraphs into sections (max 5 paragraphs per section)
                for i in range(0, len(paragraphs), 5):
                    section_paragraphs = paragraphs[i:i+5]
                    sections.append({
                        "id": f"SEC-{i//5+1:03d}",
                        "heading": f"Section {i//5+1}",
                        "text": "\n\n".join(section_paragraphs),
                        "level": 1
                    })
            
            return {
                "headings": headings,
                "sections": sections,
                "total_sections": len(sections)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            return {
                "headings": [],
                "sections": [],
                "total_sections": 0,
                "error": str(e)
            }


'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Activity } from 'lucide-react';

interface DataCategoryCardProps {
  category: string;
  data: any;
  lastUpdate: Date;
  icon: React.ComponentType<any>;
  color: string;
  isLoading?: boolean;
}

const DataCategoryCard: React.FC<DataCategoryCardProps> = ({
  category,
  data,
  lastUpdate,
  icon: IconComponent,
  color,
  isLoading = false
}) => {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
      case 'high':
        return 'bg-red-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const renderDataContent = () => {
    if (!data) {
      return (
        <div className="text-center text-muted-foreground py-4">
          <Activity className={`h-8 w-8 mx-auto mb-2 opacity-50 ${isLoading ? 'animate-pulse' : ''}`} />
          <p>Waiting for {category} data...</p>
          {isLoading && <p className="text-xs">Loading...</p>}
        </div>
      );
    }

    switch (category) {
      case 'disaster':
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span>Alert Level:</span>
              <Badge className={getPriorityBadgeColor(data.severity || data.alert_level)}>
                {data.severity || data.alert_level || 'Unknown'}
              </Badge>
            </div>
            {data.location && (
              <div className="flex items-center justify-between">
                <span>Location:</span>
                <span className="text-sm">{data.location}</span>
              </div>
            )}
            {data.event_type && (
              <div className="flex items-center justify-between">
                <span>Event Type:</span>
                <Badge variant="outline">{data.event_type}</Badge>
              </div>
            )}
            {data.description && (
              <div className="text-sm text-muted-foreground mt-2">
                {data.description}
              </div>
            )}
          </div>
        );

      case 'compliance':
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span>Status:</span>
              <Badge className={getPriorityBadgeColor(data.status)}>
                {data.status || 'Unknown'}
              </Badge>
            </div>
            {data.compliance_type && (
              <div className="flex items-center justify-between">
                <span>Type:</span>
                <Badge variant="outline">{data.compliance_type}</Badge>
              </div>
            )}
            {data.deadline && (
              <div className="text-xs text-muted-foreground">
                Deadline: {isClient ? new Date(data.deadline).toLocaleDateString() : 'Loading...'}
              </div>
            )}
            {data.description && (
              <div className="text-sm text-muted-foreground mt-2">
                {data.description}
              </div>
            )}
          </div>
        );

      case 'security':
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span>Threat Level:</span>
              <Badge className={getPriorityBadgeColor(data.threat_level || data.severity)}>
                {data.threat_level || data.severity || 'Unknown'}
              </Badge>
            </div>
            {data.incident_type && (
              <div className="flex items-center justify-between">
                <span>Incident Type:</span>
                <Badge variant="outline">{data.incident_type}</Badge>
              </div>
            )}
            {data.affected_systems && (
              <div className="text-sm text-muted-foreground">
                Affected: {data.affected_systems}
              </div>
            )}
            {data.description && (
              <div className="text-sm text-muted-foreground mt-2">
                {data.description}
              </div>
            )}
          </div>
        );

      case 'news':
        return (
          <div className="space-y-2">
            {data.headline && (
              <div className="font-medium text-sm">
                {data.headline}
              </div>
            )}
            {data.source && (
              <div className="flex items-center justify-between">
                <span>Source:</span>
                <Badge variant="outline">{data.source}</Badge>
              </div>
            )}
            {data.category && (
              <div className="flex items-center justify-between">
                <span>Category:</span>
                <Badge variant="outline">{data.category}</Badge>
              </div>
            )}
            {data.summary && (
              <div className="text-sm text-muted-foreground mt-2">
                {data.summary}
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="space-y-2">
            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    <Card className="relative hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg capitalize flex items-center gap-2">
            <IconComponent className={`h-5 w-5 ${color}`} />
            {category}
          </CardTitle>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {isClient ? formatTimestamp(lastUpdate) : '--:--:--'}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {renderDataContent()}
      </CardContent>
    </Card>
  );
};

export default DataCategoryCard;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax - Enhanced Two-Pronged Wizard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .starlink-gradient { background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #1e40af 50%, #1e293b 75%, #0f172a 100%); }
        .glass-card { background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); }
        .pulse-dot { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .slide-in { animation: slideIn 0.5s ease-out; }
        @keyframes slideIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .loading-spinner { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        .compliance-pod { background: linear-gradient(135deg, #065f46 0%, #059669 50%, #065f46 100%); }
        .doc-req-panel { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #1e40af 100%); }
    </style>
</head>
<body class="starlink-gradient min-h-screen text-white">

    <!-- Unified Navigation -->
    <nav class="relative z-50 px-6 py-4 glass-effect bg-slate-900/30 border-b border-slate-700/50">
        <div class="mx-auto max-w-7xl flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="h-10 w-10 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">CM</span>
                </div>
                <span class="text-2xl font-bold text-white">ComplianceMax</span>
                <span class="px-3 py-1 text-xs font-semibold bg-purple-600/20 text-purple-300 rounded-full border border-purple-500/30">
                    Compliance Wizard
                </span>
            </div>
            
            <div class="hidden md:flex items-center space-x-8">
                <button onclick="goHome()" class="text-slate-300 hover:text-white transition-colors font-medium">
                    🏠 Home
                </button>
                <button onclick="goToOnboarding()" class="text-slate-300 hover:text-white transition-colors font-medium">
                    🚀 Get Started
                </button>
                <span class="text-purple-300 font-medium">
                    🧭 Compliance Wizard
                </span>
                <div class="flex items-center space-x-2 text-sm text-slate-300">
                    <div class="w-2 h-2 bg-green-400 rounded-full pulse-dot"></div>
                    <span>Live FEMA Integration</span>
                </div>
            </div>

            <!-- Mobile Menu Button -->
            <button class="md:hidden text-white" onclick="toggleMobileMenu()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobileMenu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700/50">
            <div class="flex flex-col space-y-3 pt-4">
                <button onclick="goHome()" class="text-slate-300 hover:text-white transition-colors text-left">🏠 Home</button>
                <button onclick="goToOnboarding()" class="text-slate-300 hover:text-white transition-colors text-left">🚀 Get Started</button>
                <span class="text-purple-300 font-medium">🧭 Compliance Wizard (Current)</span>
            </div>
        </div>
    </nav>

    <!-- Progress Header -->
    <div class="max-w-7xl mx-auto px-6 py-8">
        <div class="glass-card rounded-2xl p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-white">Enhanced Compliance Wizard</h1>
                <span id="step-counter" class="text-blue-300 font-medium">Step 1 of 4</span>
            </div>
            
            <div class="w-full bg-slate-700 rounded-full h-3 mb-6">
                <div id="progress-bar" class="bg-gradient-to-r from-blue-400 to-cyan-400 h-3 rounded-full transition-all duration-500" style="width: 25%"></div>
            </div>

            <div id="step-indicators" class="grid grid-cols-4 gap-4">
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 text-white flex items-center justify-center font-bold">1</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-white">Applicant Type</p>
                        <p class="text-xs text-slate-300">GOV/PNP/Other</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold">2</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-slate-400">Disaster Selection</p>
                        <p class="text-xs text-slate-500">DR# from live data</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold">3</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-slate-400">Category Selection</p>
                        <p class="text-xs text-slate-500">ONE category (A-G)</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold">4</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-slate-400">Two-Pronged Result</p>
                        <p class="text-xs text-slate-500">Docs + Compliance Pod</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Content Container -->
    <div class="max-w-7xl mx-auto px-6 pb-12">
        
        <!-- Step 1: Applicant Type Selection -->
        <div id="step-1" class="step-content slide-in">
            <div class="max-w-3xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-bold text-white mb-4">What Type of Applicant Are You?</h2>
                        <p class="text-slate-300 text-lg">
                            This determines your eligibility requirements and documentation needs.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="applicant-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectApplicantType('government')">
                            <div class="text-center">
                                <div class="text-6xl mb-4">🏛️</div>
                                <h3 class="text-xl font-bold text-white mb-2">Government</h3>
                                <p class="text-slate-300 text-sm mb-4">State, Local, Tribal, or Territorial Government</p>
                                <ul class="text-xs text-slate-400 space-y-1">
                                    <li>• State Government Agencies</li>
                                    <li>• Local Municipalities</li>
                                    <li>• Tribal Nations</li>
                                    <li>• Special Districts</li>
                                </ul>
                            </div>
                        </div>

                        <div class="applicant-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectApplicantType('pnp')">
                            <div class="text-center">
                                <div class="text-6xl mb-4">🏥</div>
                                <h3 class="text-xl font-bold text-white mb-2">Private Nonprofit</h3>
                                <p class="text-slate-300 text-sm mb-4">501(c)(3) Organizations providing essential services</p>
                                <ul class="text-xs text-slate-400 space-y-1">
                                    <li>• Educational institutions</li>
                                    <li>• Hospitals & medical facilities</li>
                                    <li>• Utility cooperatives</li>
                                    <li>• Community organizations</li>
                                </ul>
                            </div>
                        </div>

                        <div class="applicant-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectApplicantType('other')">
                            <div class="text-center">
                                <div class="text-6xl mb-4">🏢</div>
                                <h3 class="text-xl font-bold text-white mb-2">Other Eligible</h3>
                                <p class="text-slate-300 text-sm mb-4">Other entities with special eligibility</p>
                                <ul class="text-xs text-slate-400 space-y-1">
                                    <li>• Public utilities</li>
                                    <li>• Certain private entities</li>
                                    <li>• Critical infrastructure</li>
                                    <li>• Special circumstances</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Disaster Selection -->
        <div id="step-2" class="step-content hidden">
            <div class="max-w-4xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-bold text-white mb-4">Select Your Disaster Declaration</h2>
                        <p class="text-slate-300 text-lg">
                            Choose the specific disaster declaration for your project.
                        </p>
                    </div>

                    <div id="disasters-loading" class="text-center py-12">
                        <div class="loading-spinner w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                        <p class="text-slate-300">Loading live disaster data...</p>
                    </div>

                    <div id="disasters-list" class="space-y-4 hidden">
                        <!-- Populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Category Selection (ONE at a time) -->
        <div id="step-3" class="step-content hidden">
            <div class="max-w-4xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-bold text-white mb-4">Select ONE Category of Work</h2>
                        <p class="text-slate-300 text-lg">
                            Choose the specific FEMA category for this project. We'll handle ONE category at a time for efficiency.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('A')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">🚛</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category A</h3>
                                <p class="text-blue-300 font-medium mb-2">Debris Removal</p>
                                <p class="text-slate-300 text-sm">Removal of disaster-generated debris from public property</p>
                            </div>
                        </div>

                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('B')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">🚨</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category B</h3>
                                <p class="text-orange-300 font-medium mb-2">Emergency Protective Measures</p>
                                <p class="text-slate-300 text-sm">Emergency actions to save lives and protect property</p>
                            </div>
                        </div>

                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('C')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">🛣️</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category C</h3>
                                <p class="text-green-300 font-medium mb-2">Roads and Bridges</p>
                                <p class="text-slate-300 text-sm">Repair of roads, bridges, and related infrastructure</p>
                            </div>
                        </div>

                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('D')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">🌊</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category D</h3>
                                <p class="text-cyan-300 font-medium mb-2">Water Control Facilities</p>
                                <p class="text-slate-300 text-sm">Repair of water control and flood protection systems</p>
                            </div>
                        </div>

                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('E')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">🏢</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category E</h3>
                                <p class="text-purple-300 font-medium mb-2">Buildings and Equipment</p>
                                <p class="text-slate-300 text-sm">Repair of public buildings and equipment</p>
                            </div>
                        </div>

                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('F')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">⚡</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category F</h3>
                                <p class="text-yellow-300 font-medium mb-2">Utilities</p>
                                <p class="text-slate-300 text-sm">Repair of water, sewer, and power systems</p>
                            </div>
                        </div>

                        <div class="category-card bg-white/5 border border-slate-600 rounded-xl p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" onclick="selectCategory('G')">
                            <div class="text-center">
                                <div class="text-4xl mb-4">🏞️</div>
                                <h3 class="text-xl font-bold text-white mb-2">Category G</h3>
                                <p class="text-pink-300 font-medium mb-2">Parks & Recreation</p>
                                <p class="text-slate-300 text-sm">Repair of recreational and other facilities</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Two-Pronged Result -->
        <div id="step-4" class="step-content hidden">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-4xl font-bold text-white mb-4">🎯 Two-Pronged Compliance System Activated</h2>
                    <p class="text-slate-300 text-lg">
                        Your personalized workflow has been generated based on your selections.
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Prong 1: Applicant Documentation Requirements -->
                    <div class="doc-req-panel rounded-2xl p-6 text-white">
                        <div class="flex items-center mb-6">
                            <div class="text-3xl mr-4">📋</div>
                            <div>
                                <h3 class="text-2xl font-bold">FOR YOU: Documentation Requirements</h3>
                                <p class="text-blue-100">Required documents with status tracking</p>
                            </div>
                        </div>

                        <div id="applicant-docs" class="space-y-4">
                            <!-- Populated by JavaScript based on category -->
                        </div>

                        <div class="mt-6 p-4 bg-white/10 rounded-lg">
                            <h4 class="font-bold mb-2">📊 Your Progress:</h4>
                            <div class="flex items-center space-x-4">
                                <div class="text-sm">
                                    <span class="text-green-300">✓ Have it: </span><span id="have-count">0</span>
                                </div>
                                <div class="text-sm">
                                    <span class="text-yellow-300">⏳ Can get: </span><span id="can-get-count">0</span>
                                </div>
                                <div class="text-sm">
                                    <span class="text-red-300">❌ Need help: </span><span id="need-help-count">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Prong 2: Compliance Pod (System Side) -->
                    <div class="compliance-pod rounded-2xl p-6 text-white">
                        <div class="flex items-center mb-6">
                            <div class="text-3xl mr-4">🏗️</div>
                            <div>
                                <h3 class="text-2xl font-bold">FOR US: Compliance Pod</h3>
                                <p class="text-green-100">All docs/data for your project category</p>
                            </div>
                        </div>

                        <div id="compliance-pod-content" class="space-y-4">
                            <!-- Populated by JavaScript based on category -->
                        </div>

                        <div class="mt-6 p-4 bg-white/10 rounded-lg">
                            <h4 class="font-bold mb-2">🤖 System Status:</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse-dot"></div>
                                    <span>Policy documents loaded</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse-dot"></div>
                                    <span>Cost reasonableness tools ready</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse-dot"></div>
                                    <span>Auto-scraping configured</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8 text-center">
                    <button onclick="launchDashboard()" class="bg-gradient-to-r from-green-500 to-emerald-500 text-white py-4 px-12 rounded-lg text-xl font-bold hover:from-green-600 hover:to-emerald-600 transition-all shadow-xl transform hover:scale-105">
                        🚀 Launch Your Compliance Dashboard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let wizardData = {
            applicantType: '',
            disaster: '',
            category: '',
            applicantDocStatus: {}
        };

        // Category-specific documentation requirements
        const categoryRequirements = {
            'A': {
                name: 'Debris Removal',
                requirements: [
                    'Detailed descriptions of debris removal activities',
                    'Photographs/video of debris',
                    'Maps of affected areas',
                    'Labor records',
                    'Equipment usage logs',
                    'Procurement documents'
                ],
                policies: [
                    'FEMA PAPPG v5.0',
                    'FEMA Debris Monitoring Guide',
                    'Public Assistance 9500-Series Policies',
                    'Corps of Engineers coordination docs'
                ]
            },
            'B': {
                name: 'Emergency Protective Measures',
                requirements: [
                    'Building safety evaluations',
                    'Emergency work labor records',
                    'Equipment usage logs',
                    'Procurement documents for emergency services'
                ],
                policies: [
                    'DRRA Section 1241 Building Safety Guide',
                    'FEMA PAPPG v5.0',
                    'Emergency Work guidance'
                ]
            },
            'C': {
                name: 'Roads and Bridges',
                requirements: [
                    'Damage assessments',
                    'Repair/replacement plans',
                    'Cost estimates',
                    'Labor and equipment records'
                ],
                policies: [
                    'FEMA PAPPG v5.0',
                    'Public Assistance 9500-Series Policies',
                    'DOT coordination guidance'
                ]
            }
            // Add other categories...
        };

        function updateStepIndicators(step) {
            const indicators = document.querySelectorAll('#step-indicators > div');
            indicators.forEach((indicator, index) => {
                const circle = indicator.querySelector('div');
                const title = indicator.querySelector('p:first-of-type');
                const subtitle = indicator.querySelector('p:last-of-type');
                
                if (index + 1 <= step) {
                    circle.className = 'w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 text-white flex items-center justify-center font-bold';
                    title.className = 'text-sm font-semibold text-white';
                    subtitle.className = 'text-xs text-slate-300';
                } else {
                    circle.className = 'w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold';
                    title.className = 'text-sm font-semibold text-slate-400';
                    subtitle.className = 'text-xs text-slate-500';
                }
            });

            document.getElementById('step-counter').textContent = `Step ${step} of 4`;
            document.getElementById('progress-bar').style.width = `${(step / 4) * 100}%`;
        }

        function goToStep(step) {
            document.querySelectorAll('.step-content').forEach(el => {
                el.classList.add('hidden');
                el.classList.remove('slide-in');
            });
            
            const targetStep = document.getElementById(`step-${step}`);
            targetStep.classList.remove('hidden');
            setTimeout(() => targetStep.classList.add('slide-in'), 50);
            
            currentStep = step;
            updateStepIndicators(step);

            if (step === 2) loadDisasters();
            if (step === 4) generateTwoProngedResult();
        }

        function selectApplicantType(type) {
            wizardData.applicantType = type;
            
            // Visual feedback
            document.querySelectorAll('.applicant-card').forEach(card => {
                card.classList.remove('border-blue-400', 'bg-blue-900/20');
                card.classList.add('border-slate-600');
            });
            
            event.currentTarget.classList.remove('border-slate-600');
            event.currentTarget.classList.add('border-blue-400', 'bg-blue-900/20');
            
            setTimeout(() => goToStep(2), 1000);
        }

        function loadDisasters() {
            const loadingEl = document.getElementById('disasters-loading');
            const listEl = document.getElementById('disasters-list');
            
            loadingEl.classList.remove('hidden');
            listEl.classList.add('hidden');

            // Use live disaster data
            fetch('http://localhost:5000/api/fema/disasters/enhanced?format=json')
                .then(response => response.json())
                .then(data => {
                    const disasters = data.data || [];
                    displayDisasters(disasters);
                })
                .catch(() => {
                    displayDisasters(getMockDisasters());
                })
                .finally(() => {
                    loadingEl.classList.add('hidden');
                    listEl.classList.remove('hidden');
                });
        }

        function getMockDisasters() {
            return [
                { drNumber: 'DR-4873-AR', title: 'Arkansas Severe Storms', status: 'Active' },
                { drNumber: 'DR-4868-NE', title: 'Nebraska Severe Weather', status: 'Active' },
                { drNumber: 'DR-4875-KY', title: 'Kentucky Storms', status: 'Active' }
            ];
        }

        function displayDisasters(disasters) {
            const listEl = document.getElementById('disasters-list');
            listEl.innerHTML = disasters.map(disaster => `
                <div class="disaster-card bg-white/5 border border-slate-600 rounded-lg p-6 cursor-pointer hover:border-blue-400 hover:bg-white/10 transition-all" 
                     onclick="selectDisaster('${disaster.drNumber}')">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-semibold text-white">${disaster.drNumber}</h3>
                            <p class="text-slate-300">${disaster.title}</p>
                        </div>
                        <span class="px-3 py-1 bg-green-900/50 text-green-300 rounded-full text-sm">${disaster.status}</span>
                    </div>
                </div>
            `).join('');
        }

        function selectDisaster(drNumber) {
            wizardData.disaster = drNumber;
            
            document.querySelectorAll('.disaster-card').forEach(card => {
                card.classList.remove('border-blue-400', 'bg-blue-900/20');
            });
            
            event.currentTarget.classList.add('border-blue-400', 'bg-blue-900/20');
            
            setTimeout(() => goToStep(3), 1000);
        }

        function selectCategory(category) {
            wizardData.category = category;
            
            document.querySelectorAll('.category-card').forEach(card => {
                card.classList.remove('border-blue-400', 'bg-blue-900/20');
            });
            
            event.currentTarget.classList.add('border-blue-400', 'bg-blue-900/20');
            
            setTimeout(() => goToStep(4), 1000);
        }

        function generateTwoProngedResult() {
            const category = wizardData.category;
            const categoryData = categoryRequirements[category];

            if (!categoryData) return;

            // Prong 1: Applicant Documentation Requirements
            const applicantDocsEl = document.getElementById('applicant-docs');
            applicantDocsEl.innerHTML = categoryData.requirements.map((req, index) => `
                <div class="bg-white/10 p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium">${req}</h4>
                        <select onchange="updateDocStatus('${index}', this.value)" class="bg-slate-700 text-white px-2 py-1 rounded text-sm">
                            <option value="">Status?</option>
                            <option value="have">✓ Have it</option>
                            <option value="can-get">⏳ Can get it</option>
                            <option value="need-help">❌ Need help</option>
                        </select>
                    </div>
                    <div id="doc-status-${index}" class="text-sm text-slate-400">
                        Please indicate if you have this documentation
                    </div>
                </div>
            `).join('');

            // Prong 2: Compliance Pod
            const compliancePodEl = document.getElementById('compliance-pod-content');
            compliancePodEl.innerHTML = `
                <div class="space-y-3">
                    <h4 class="font-bold text-lg">📚 Policy Documents Loaded:</h4>
                    ${categoryData.policies.map(policy => `
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                            <span>${policy}</span>
                        </div>
                    `).join('')}
                </div>
                
                <div class="space-y-3 mt-6">
                    <h4 class="font-bold text-lg">🤖 Auto-Scraping Active:</h4>
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                        <span>Permits database for ${wizardData.disaster}</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                        <span>Cost reasonableness data</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                        <span>Corps of Engineers coordination</span>
                    </div>
                </div>
            `;
        }

        function updateDocStatus(docIndex, status) {
            wizardData.applicantDocStatus[docIndex] = status;
            
            const statusEl = document.getElementById(`doc-status-${docIndex}`);
            const statusText = {
                'have': '✅ Great! You have this document ready.',
                'can-get': '⏳ Good! Please obtain this within 30 days.',
                'need-help': '❌ We\'ll help you understand requirements for this document.'
            };
            
            statusEl.textContent = statusText[status] || 'Please select a status';
            statusEl.className = status === 'have' ? 'text-sm text-green-300' : 
                                status === 'can-get' ? 'text-sm text-yellow-300' : 
                                status === 'need-help' ? 'text-sm text-red-300' : 'text-sm text-slate-400';
            
            updateProgressCounts();
        }

        function updateProgressCounts() {
            const statuses = Object.values(wizardData.applicantDocStatus);
            document.getElementById('have-count').textContent = statuses.filter(s => s === 'have').length;
            document.getElementById('can-get-count').textContent = statuses.filter(s => s === 'can-get').length;
            document.getElementById('need-help-count').textContent = statuses.filter(s => s === 'need-help').length;
        }

        function launchDashboard() {
            alert(`🎉 WIZARD COMPLETE!

Applicant: ${wizardData.applicantType}
Disaster: ${wizardData.disaster}
Category: ${wizardData.category}

✅ Documentation requirements generated
✅ Compliance pod populated with ${categoryRequirements[wizardData.category]?.policies.length || 0} policy documents
✅ Auto-scraping configured
✅ Workflow ready!

Launching your personalized compliance dashboard...`);
            
            window.location.href = 'http://localhost:5000/api/fema/disasters/enhanced';
        }

        // Initialize
        updateStepIndicators(1);

        // Navigation Functions
        function goHome() {
            // If we're in an iframe, send message to parent
            if (window.parent !== window) {
                window.parent.postMessage({ action: 'navigate', page: 'home' }, '*');
            } else {
                window.location.href = '../index.html';
            }
        }

        function goToOnboarding() {
            // If we're in an iframe, send message to parent
            if (window.parent !== window) {
                window.parent.postMessage({ action: 'navigate', page: 'onboarding' }, '*');
            } else {
                window.location.href = '../intelligent-onboarding.html';
            }
        }

        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }
    </script>
</body>
</html> 
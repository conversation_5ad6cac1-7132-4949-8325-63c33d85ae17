import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { Prism as Synta<PERSON><PERSON>ighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

const ApiDocs: React.FC = () => {
  const curlExample = `# Initialize a compliance review
curl -X POST "https://api.maxcompliance.net/v1/compliance/reviews" \\
  -H "Authorization: Bearer your_api_key_here" \\
  -H "Content-Type: application/json" \\
  -d '{
    "project_name": "Sample Project",
    "dr_number": "DR-4999",
    "applicant": "Sample County",
    "total_cost": 1000000
  }'

# Upload documents
curl -X POST "https://api.maxcompliance.net/v1/compliance/reviews/{review_id}/documents" \\
  -H "Authorization: Bearer your_api_key_here" \\
  -F "file=@document.pdf"

# Get review status
curl "https://api.maxcompliance.net/v1/compliance/reviews/{review_id}" \\
  -H "Authorization: Bearer your_api_key_here"`;

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', py: 4 }}>
      <Typography variant="h4" component="h1" sx={{ mb: 4, color: 'white', textAlign: 'center' }}>
        API Documentation
      </Typography>

      <Paper
        sx={{
          p: 4,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          color: 'white',
        }}
      >
        <SyntaxHighlighter
          language="bash"
          style={atomDark}
          customStyle={{
            background: 'rgba(0, 0, 0, 0.2)',
            padding: '20px',
            borderRadius: '4px',
            marginBottom: '24px',
          }}
        >
          {curlExample}
        </SyntaxHighlighter>

        <Typography variant="h5" sx={{ mt: 4, mb: 2 }}>
          Authentication
        </Typography>
        <Typography sx={{ mb: 3, color: 'rgba(255, 255, 255, 0.7)' }}>
          All API requests must include your API key in the Authorization header:
        </Typography>
        <SyntaxHighlighter
          language="bash"
          style={atomDark}
          customStyle={{
            background: 'rgba(0, 0, 0, 0.2)',
            padding: '12px',
            borderRadius: '4px',
            marginBottom: '24px',
          }}
        >
          Authorization: Bearer your_api_key_here
        </SyntaxHighlighter>

        <Typography variant="h5" sx={{ mt: 4, mb: 2 }}>
          Rate Limits
        </Typography>
        <Typography sx={{ mb: 2, color: 'rgba(255, 255, 255, 0.7)' }}>
          The API has the following rate limits:
        </Typography>
        <Box component="ul" sx={{ color: 'rgba(255, 255, 255, 0.7)', pl: 3 }}>
          <li>100 requests per minute per IP address</li>
          <li>1000 requests per hour per API key</li>
          <li>10,000 requests per day per API key</li>
        </Box>
      </Paper>

      <Typography
        variant="body2"
        sx={{
          mt: 4,
          textAlign: 'center',
          color: 'rgba(255, 255, 255, 0.5)',
        }}
      >
        2025 ComplianceMax. All rights reserved.
      </Typography>
    </Box>
  );
};

export default ApiDocs;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBCS Professional Analysis Service - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            font-size: 18px;
            transform: scale(1.3);
            transform-origin: top left;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: grid;
            grid-template-rows: 80px 1fr 60px;
            gap: 0;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            z-index: 100;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 18px;
            margin-left: 15px;
        }
        
        .badges {
            display: flex;
            gap: 15px;
        }
        
        .badge {
            padding: 10px 18px;
            border-radius: 20px;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }
        
        .badge.policy { background: #e74c3c; }
        .badge.database { background: #27ae60; }
        .badge.cbcs { background: #f39c12; }
        
        .main-content {
            background: white;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 0;
            overflow: hidden;
        }
        
        .column {
            padding: 30px;
            overflow-y: auto;
            border-right: 1px solid #e1e8ed;
            height: 100%;
        }
        
        .column:last-child {
            border-right: none;
        }
        
        .column h3 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 25px;
            padding-bottom: 12px;
            border-bottom: 3px solid #3498db;
            font-weight: 700;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            font-weight: 700;
            margin-bottom: 10px;
            font-size: 18px;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 18px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .auto-populate-btn {
            background: #f39c12;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
            transition: background 0.3s;
        }
        
        .auto-populate-btn:hover {
            background: #e67e22;
        }
        
        .cbcs-codes {
            background: #f8f9fa;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .cbcs-codes h4 {
            margin: 0 0 18px 0;
            font-size: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            font-weight: 700;
        }
        
        .code-item {
            display: flex;
            align-items: flex-start;
            padding: 12px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
            border-radius: 6px;
            margin-bottom: 10px;
        }
        
        .code-item:hover {
            background: #e3f2fd;
        }
        
        .code-item.selected {
            background: #e8f5e8;
            border: 2px solid #27ae60;
        }
        
        .code-item.mandatory {
            background: #fff3cd;
            border: 2px solid #ffc107;
        }
        
        .code-item input {
            margin-right: 15px;
            margin-top: 3px;
            transform: scale(1.5);
        }
        
        .code-item label {
            cursor: pointer;
            line-height: 1.4;
            flex: 1;
        }
        
        .code-title {
            font-weight: 700;
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }
        
        .code-desc {
            color: #7f8c8d;
            font-size: 15px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #27ae60;
            color: white;
        }
        
        .btn-primary:hover {
            background: #219a52;
            transform: translateY(-1px);
        }
        
        .footer {
            background: white;
            border-top: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }
        
        .status-text {
            color: #7f8c8d;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>CBCS Professional Analysis Service</h1>
                <span class="subtitle">Complete this comprehensive intake form. Our compliance experts will analyze your project and provide a detailed report within 2-3 business days.</span>
            </div>
            <div class="badges">
                <span class="badge policy">DRRA 1235b</span>
                <span class="badge database">53,048 Policies</span>
                <span class="badge cbcs">Auto-Populate</span>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Column 1: Project Information -->
            <div class="column">
                <h3>📋 Project Information</h3>
                
                <div class="form-group">
                    <label class="form-label">Project Name *</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Enter project name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Facility Type *</label>
                    <select class="form-control" id="facilityType" required>
                        <option value="">Select Facility Type</option>
                        <option value="Bridge">Bridge</option>
                        <option value="Road">Road/Highway</option>
                        <option value="Building">Building/Structure</option>
                        <option value="Water Treatment">Water Treatment Facility</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Estimated Project Cost *</label>
                    <input type="number" class="form-control" id="estimatedCost" placeholder="Enter cost in USD" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Work Description *</label>
                    <textarea class="form-control" id="workDescription" rows="4" placeholder="Describe the work to be performed" required></textarea>
                    <button type="button" class="auto-populate-btn" onclick="autoPopulateCodes()">🔄 Auto-Populate CBCS Codes</button>
                </div>
            </div>
            
            <!-- Column 2: Additional Details -->
            <div class="column">
                <h3>⚖️ Additional Details</h3>
                
                <div class="form-group">
                    <label class="form-label">Damage Date *</label>
                    <input type="date" class="form-control" id="damageDate" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">FEMA Disaster Declaration</label>
                    <input type="text" class="form-control" id="disasterNumber" placeholder="e.g., FEMA-4XXX-DR">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Special Considerations</label>
                    <textarea class="form-control" rows="8" placeholder="Environmental concerns, historic preservation, accessibility requirements, etc."></textarea>
                </div>
            </div>
            
            <!-- Column 3: CBCS Codes -->
            <div class="column">
                <h3>📋 CBCS Codes (Auto-Selected)</h3>
                
                <div class="cbcs-codes">
                    <h4>Bridge/Road Standards</h4>
                    <div class="code-item mandatory">
                        <input type="checkbox" id="aashto_lrfd" name="cbcs_codes" value="AASHTO_LRFD_Bridge" checked>
                        <label for="aashto_lrfd">
                            <span class="code-title">AASHTO LRFD Bridge Design</span>
                            <div class="code-desc">Load and Resistance Factor Design for bridges</div>
                        </label>
                    </div>
                    <div class="code-item mandatory">
                        <input type="checkbox" id="aashto_geometric" name="cbcs_codes" value="AASHTO_Highway_Geometric" checked>
                        <label for="aashto_geometric">
                            <span class="code-title">AASHTO Highway Geometric Design</span>
                            <div class="code-desc">Highway and roadway design standards</div>
                        </label>
                    </div>
                </div>
                
                <div class="cbcs-codes">
                    <h4>Structural Standards</h4>
                    <div class="code-item">
                        <input type="checkbox" id="asce_7" name="cbcs_codes" value="ASCE_7_16">
                        <label for="asce_7">
                            <span class="code-title">ASCE 7-16 Minimum Design Loads</span>
                            <div class="code-desc">Wind, seismic, and load requirements</div>
                        </label>
                    </div>
                    <div class="code-item">
                        <input type="checkbox" id="aci_318" name="cbcs_codes" value="ACI_318_19">
                        <label for="aci_318">
                            <span class="code-title">ACI 318-19 Reinforced Concrete</span>
                            <div class="code-desc">Concrete design standards</div>
                        </label>
                    </div>
                </div>
                
                <div class="cbcs-codes">
                    <h4>Safety Standards</h4>
                    <div class="code-item">
                        <input type="checkbox" id="nfpa_1141" name="cbcs_codes" value="NFPA_1141">
                        <label for="nfpa_1141">
                            <span class="code-title">NFPA 1141 Fire Protection</span>
                            <div class="code-desc">Fire protection infrastructure</div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="status-text">
                Phase 7 Complete • 53,048 FEMA policy records • CBCS integration active
            </div>
            <div>
                <button type="button" class="btn btn-primary" onclick="submitIntake()">Submit for Analysis</button>
            </div>
        </div>
    </div>
    
    <script>
        async function autoPopulateCodes() {
            const facilityType = document.getElementById('facilityType').value;
            const workDescription = document.getElementById('workDescription').value;
            const estimatedCost = parseFloat(document.getElementById('estimatedCost').value) || 0;
            
            if (!facilityType || !workDescription) {
                alert('Please fill in Facility Type and Work Description first');
                return;
            }
            
            try {
                const response = await fetch('/api/cbcs/auto-populate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ facilityType, workDescription, estimatedCost, workCategory: 'C' })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Clear and set auto-selected codes
                    document.querySelectorAll('input[name="cbcs_codes"]').forEach(cb => cb.checked = false);
                    data.auto_selected_codes.forEach(code => {
                        const checkbox = document.querySelector(`input[value="${code.code}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.closest('.code-item').classList.add('selected');
                        }
                    });
                    
                    alert(`Auto-populated ${data.total_codes} codes based on your project details`);
                }
            } catch (error) {
                console.error('Auto-populate error:', error);
            }
        }
        
        async function submitIntake() {
            const formData = {
                projectName: document.getElementById('projectName').value,
                facilityType: document.getElementById('facilityType').value,
                estimatedCost: document.getElementById('estimatedCost').value,
                workDescription: document.getElementById('workDescription').value,
                damageDate: document.getElementById('damageDate').value,
                workCategory: 'C',
                selectedCodes: Array.from(document.querySelectorAll('input[name="cbcs_codes"]:checked')).map(cb => cb.value),
                start_wizard: true
            };
            
            if (!formData.projectName || !formData.facilityType || !formData.workDescription) {
                alert('Please fill in all required fields');
                return;
            }
            
            try {
                const response = await fetch('/api/intake/cbcs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert(`CBCS Intake submitted successfully!\nIntake ID: ${data.intake_id}`);
                    window.location.href = '/professional-intake';
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Submit error:', error);
                alert('Error submitting intake');
            }
        }
        
        // Auto-select codes when facility type changes
        document.getElementById('facilityType').addEventListener('change', function() {
            if (this.value && document.getElementById('workDescription').value) {
                autoPopulateCodes();
            }
        });
    </script>
</body>
</html> 
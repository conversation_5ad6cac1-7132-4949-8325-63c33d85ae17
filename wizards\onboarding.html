<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax - Intelligent Onboarding</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e293b 0%, #1e40af 50%, #1e293b 100%);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .service-card.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .loading-dots {
            animation: loading 1.4s infinite ease-in-out both;
        }
        @keyframes loading {
            0%, 80%, 100% { opacity: 0; }
            40% { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-6 py-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                    <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">CM</span>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900">ComplianceMax Setup</h1>
                </div>
                <span class="text-sm text-gray-500" id="step-counter">Step 1 of 3</span>
            </div>
            
            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 33%"></div>
            </div>

            <!-- Step Indicators -->
            <div class="flex justify-between mt-4" id="step-indicators">
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">1</div>
                    <div class="mt-2 text-center">
                        <p class="text-sm font-medium text-gray-900">FEMA Registration</p>
                        <p class="text-xs text-gray-500">Auto-populate information</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center text-sm font-medium">2</div>
                    <div class="mt-2 text-center">
                        <p class="text-sm font-medium text-gray-900">Disaster Selection</p>
                        <p class="text-xs text-gray-500">Choose your specific disaster</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center text-sm font-medium">3</div>
                    <div class="mt-2 text-center">
                        <p class="text-sm font-medium text-gray-900">Service Level</p>
                        <p class="text-xs text-gray-500">Select subscription tier</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Content Container -->
    <div class="max-w-6xl mx-auto px-6 py-12">
        
        <!-- Step 1: FEMA Number Entry -->
        <div id="step-1" class="step-content">
            <div class="max-w-lg mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Enter Your FEMA Registration Number</h2>
                    <p class="text-gray-600">
                        We'll automatically populate your information and identify eligible services based on your registration.
                    </p>
                </div>

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            FEMA Registration Number
                        </label>
                        <input
                            type="text"
                            id="fema-number"
                            placeholder="e.g., FEMA-12345-TX, PA-67890-FL, DR-4873-AR"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <p id="fema-error" class="mt-2 text-sm text-red-600 hidden"></p>
                    </div>

                    <button
                        id="lookup-btn"
                        onclick="handleFEMALookup()"
                        class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Auto-Populate Information
                    </button>

                    <div class="text-center text-sm text-gray-500">
                        <p>Don't have a FEMA registration number? <a href="#" class="text-blue-600 hover:underline">Register with FEMA first</a></p>
                    </div>
                </div>

                <div id="profile-info" class="mt-8 p-6 bg-green-50 border border-green-200 rounded-lg hidden">
                    <h3 class="text-lg font-semibold text-green-800 mb-4">✓ Information Retrieved</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">State:</span>
                            <span id="profile-state" class="ml-2 text-gray-900"></span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">County:</span>
                            <span id="profile-county" class="ml-2 text-gray-900"></span>
                        </div>
                        <div class="col-span-2">
                            <span class="font-medium text-gray-700">Applicant Type:</span>
                            <span id="profile-type" class="ml-2 text-gray-900"></span>
                        </div>
                    </div>
                    <button
                        onclick="goToStep(2)"
                        class="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Continue to Disaster Selection
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Disaster Selection -->
        <div id="step-2" class="step-content hidden">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Select Your Disaster</h2>
                    <p class="text-gray-600">
                        Based on your location, here are the currently eligible disasters:
                    </p>
                </div>

                <div id="disasters-list" class="space-y-4">
                    <!-- Disasters will be populated by JavaScript -->
                </div>

                <div class="mt-8 text-center">
                    <button
                        id="continue-disaster-btn"
                        onclick="goToStep(3)"
                        class="bg-blue-600 text-white py-3 px-8 rounded-lg hover:bg-blue-700 transition-colors hidden"
                    >
                        Continue to Service Selection
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 3: Service Selection -->
        <div id="step-3" class="step-content hidden">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Choose Your Service Level</h2>
                    <p class="text-gray-600">
                        Select the plan that best fits your compliance needs and project complexity.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Basic Plan -->
                    <div class="service-card relative p-8 border-2 border-gray-200 rounded-xl cursor-pointer transition-all card-hover" onclick="selectService('basic')">
                        <div class="text-center mb-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Basic Compliance</h3>
                            <p class="text-gray-600 text-sm mb-4">Essential tools for straightforward projects</p>
                            <div class="text-3xl font-bold text-gray-900">
                                $199
                                <span class="text-lg font-normal text-gray-500">/project</span>
                            </div>
                        </div>

                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Basic PAPPG compliance checklist</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Document templates</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Email support</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Standard reporting</span>
                            </li>
                        </ul>

                        <button class="w-full py-3 px-6 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors">
                            Select Plan
                        </button>
                    </div>

                    <!-- Professional Plan (Recommended) -->
                    <div class="service-card relative p-8 border-2 border-gray-200 rounded-xl cursor-pointer transition-all card-hover" onclick="selectService('professional')">
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                                Recommended
                            </span>
                        </div>

                        <div class="text-center mb-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Professional</h3>
                            <p class="text-gray-600 text-sm mb-4">Advanced automation for complex projects</p>
                            <div class="text-3xl font-bold text-gray-900">
                                $499
                                <span class="text-lg font-normal text-gray-500">/project</span>
                            </div>
                        </div>

                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Full PAPPG + DRRA compliance</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Intelligent workflow automation</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Real-time FEMA integration</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Advanced reporting & analytics</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Priority support</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Document OCR processing</span>
                            </li>
                        </ul>

                        <button class="w-full py-3 px-6 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors">
                            Select Plan
                        </button>
                    </div>

                    <!-- Enterprise Plan -->
                    <div class="service-card relative p-8 border-2 border-gray-200 rounded-xl cursor-pointer transition-all card-hover" onclick="selectService('enterprise')">
                        <div class="text-center mb-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Enterprise</h3>
                            <p class="text-gray-600 text-sm mb-4">Complete solution for large organizations</p>
                            <div class="text-3xl font-bold text-gray-900">
                                $999
                                <span class="text-lg font-normal text-gray-500">/project</span>
                            </div>
                        </div>

                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Everything in Professional</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Multi-project management</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Custom policy integration</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Dedicated account manager</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">Training & onboarding</span>
                            </li>
                            <li class="flex items-center text-sm">
                                <span class="w-4 h-4 text-green-500 mr-3">✓</span>
                                <span class="text-gray-700">API access</span>
                            </li>
                        </ul>

                        <button class="w-full py-3 px-6 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors">
                            Select Plan
                        </button>
                    </div>
                </div>

                <div id="subscribe-section" class="mt-12 text-center hidden">
                    <button
                        onclick="handleSubscription()"
                        class="bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-4 px-12 rounded-lg text-lg font-semibold hover:from-blue-700 hover:to-cyan-700 transition-all shadow-lg"
                    >
                        Subscribe & Launch Dashboard
                    </button>
                    <p class="mt-4 text-sm text-gray-500">
                        30-day money-back guarantee • Cancel anytime
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let userProfile = {};
        let selectedDisaster = '';
        let selectedService = '';

        function updateStepIndicators(step) {
            const indicators = document.querySelectorAll('#step-indicators > div');
            indicators.forEach((indicator, index) => {
                const circle = indicator.querySelector('div');
                if (index + 1 <= step) {
                    circle.className = 'w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium';
                } else {
                    circle.className = 'w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center text-sm font-medium';
                }
            });

            document.getElementById('step-counter').textContent = `Step ${step} of 3`;
            document.getElementById('progress-bar').style.width = `${(step / 3) * 100}%`;
        }

        function goToStep(step) {
            // Hide all steps
            document.querySelectorAll('.step-content').forEach(el => el.classList.add('hidden'));
            
            // Show target step
            document.getElementById(`step-${step}`).classList.remove('hidden');
            
            currentStep = step;
            updateStepIndicators(step);

            if (step === 2) {
                loadDisasters();
            }
        }

        async function handleFEMALookup() {
            const femaNumber = document.getElementById('fema-number').value.trim();
            const errorEl = document.getElementById('fema-error');
            const lookupBtn = document.getElementById('lookup-btn');

            if (!femaNumber) {
                errorEl.textContent = 'Please enter your FEMA registration number';
                errorEl.classList.remove('hidden');
                return;
            }

            errorEl.classList.add('hidden');
            lookupBtn.textContent = 'Looking up...';
            lookupBtn.disabled = true;

            // Simulate API call with realistic delay
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Mock profile data based on FEMA number
            let profile;
            if (femaNumber.includes('-TX') || femaNumber.includes('TX')) {
                profile = { state: 'Texas', county: 'Harris', applicantType: 'Local Government' };
            } else if (femaNumber.includes('-FL') || femaNumber.includes('FL')) {
                profile = { state: 'Florida', county: 'Miami-Dade', applicantType: 'Nonprofit Organization' };
            } else if (femaNumber.includes('-AR') || femaNumber.includes('AR')) {
                profile = { state: 'Arkansas', county: 'Pulaski', applicantType: 'State Government' };
            } else {
                profile = { state: 'Nebraska', county: 'Douglas', applicantType: 'Local Government' };
            }

            userProfile = { femaNumber, ...profile };

            // Display profile info
            document.getElementById('profile-state').textContent = profile.state;
            document.getElementById('profile-county').textContent = profile.county;
            document.getElementById('profile-type').textContent = profile.applicantType;
            document.getElementById('profile-info').classList.remove('hidden');

            lookupBtn.textContent = 'Auto-Populate Information';
            lookupBtn.disabled = false;
        }

        function loadDisasters() {
            const disastersList = document.getElementById('disasters-list');
            
            // Mock disaster data
            const disasters = [
                {
                    drNumber: 'DR-4873-AR',
                    title: 'Arkansas Severe Storms and Flooding',
                    declarationDate: '2024-06-15',
                    status: 'Open',
                    eligiblePrograms: ['Public Assistance', 'Individual Assistance']
                },
                {
                    drNumber: 'DR-4868-NE',
                    title: 'Nebraska Severe Weather',
                    declarationDate: '2024-05-20',
                    status: 'Open',
                    eligiblePrograms: ['Public Assistance']
                },
                {
                    drNumber: 'DR-4875-KY',
                    title: 'Kentucky Storms and Flooding',
                    declarationDate: '2024-07-01',
                    status: 'Open',
                    eligiblePrograms: ['Public Assistance', 'Hazard Mitigation']
                }
            ];

            disastersList.innerHTML = disasters.map(disaster => `
                <div class="p-6 border-2 border-gray-200 rounded-lg cursor-pointer transition-all hover:border-gray-300" 
                     onclick="selectDisaster('${disaster.drNumber}')">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">${disaster.drNumber}</h3>
                            <p class="text-gray-600">${disaster.title}</p>
                            <p class="text-sm text-gray-500">
                                Declared: ${new Date(disaster.declarationDate).toLocaleDateString()}
                            </p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ${disaster.status}
                            </span>
                            <p class="text-sm text-gray-500 mt-1">
                                Programs: ${disaster.eligiblePrograms.join(', ')}
                            </p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function selectDisaster(drNumber) {
            selectedDisaster = drNumber;
            
            // Highlight selected disaster
            document.querySelectorAll('#disasters-list > div').forEach(el => {
                el.classList.remove('border-blue-500', 'bg-blue-50');
                el.classList.add('border-gray-200');
            });
            
            event.currentTarget.classList.remove('border-gray-200');
            event.currentTarget.classList.add('border-blue-500', 'bg-blue-50');
            
            document.getElementById('continue-disaster-btn').classList.remove('hidden');
        }

        function selectService(serviceId) {
            selectedService = serviceId;
            
            // Reset all service cards
            document.querySelectorAll('.service-card').forEach(card => {
                card.classList.remove('selected');
                const button = card.querySelector('button');
                button.textContent = 'Select Plan';
                button.className = 'w-full py-3 px-6 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors';
            });
            
            // Highlight selected service
            event.currentTarget.classList.add('selected');
            const selectedButton = event.currentTarget.querySelector('button');
            selectedButton.textContent = 'Selected';
            selectedButton.className = 'w-full py-3 px-6 rounded-lg bg-blue-600 text-white transition-colors';
            
            document.getElementById('subscribe-section').classList.remove('hidden');
        }

        function handleSubscription() {
            // Simulate subscription process
            alert(`Subscription successful! 
            
Profile: ${userProfile.applicantType} in ${userProfile.county}, ${userProfile.state}
Disaster: ${selectedDisaster}
Service: ${selectedService.charAt(0).toUpperCase() + selectedService.slice(1)} plan

Redirecting to your personalized compliance dashboard...`);
            
            // In real implementation, this would redirect to the dashboard
            window.location.href = 'http://localhost:5000/api/fema/disasters/enhanced';
        }

        // Initialize the page
        updateStepIndicators(1);
    </script>
</body>
</html> 
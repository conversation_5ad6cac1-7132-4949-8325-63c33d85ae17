{% extends "base/base.html" %}

{% block title %}Dashboard | ComplianceMax{% endblock %}

{% block extra_head %}
<script src="https://cdn.tailwindcss.com"></script>
{% endblock %}

{% block content %}
{% extends "base/base.html" %}

{% block title %}ComplianceMax V74 - Dashboard{% endblock %}

{% block extra_head %}
<script src="https://cdn.tailwindcss.com"></script>
<style>
    /* Enhanced Dashboard Styles - Optimized for ComplianceMax V74 */
    body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        min-height: 100vh;
        color: white;
        margin: 0;
        padding: 0;
    }
    
    .navbar {
        background: rgba(15, 23, 42, 0.95);
        border-bottom: 1px solid rgba(59, 130, 246, 0.2);
        padding: 1.5rem 0;
        backdrop-filter: blur(10px);
    }
    
    .hero-section {
        padding: 3rem 2rem 2rem;
        text-align: center;
    }
    
    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        line-height: 1.2;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ffffff 0%, #3b82f6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .hero-description {
        font-size: 1.2rem;
        line-height: 1.5;
        color: rgba(156, 163, 175, 1);
        margin-bottom: 2rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        padding: 1rem 2.5rem;
        border-radius: 0.75rem;
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        border: 1px solid rgba(59, 130, 246, 0.3);
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }
    
    .btn-secondary {
        background: rgba(30, 41, 59, 0.9);
        color: white;
        padding: 1rem 2.5rem;
        border-radius: 0.75rem;
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-block;
        border: 1px solid rgba(75, 85, 99, 0.5);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .btn-secondary:hover {
        background: rgba(75, 85, 99, 0.7);
        transform: translateY(-2px);
        border-color: rgba(59, 130, 246, 0.5);
    }
    
    .pathway-selector {
        background: rgba(30, 41, 59, 0.9);
        border: 1px solid rgba(75, 85, 99, 0.5);
        border-radius: 1rem;
        padding: 2rem;
        margin: 1rem 0;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }
    
    .pathway-selector::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
        transition: left 0.5s;
    }
    
    .pathway-selector:hover::before {
        left: 100%;
    }
    
    .pathway-selector:hover {
        background: rgba(30, 41, 59, 1);
        border-color: rgba(59, 130, 246, 0.5);
        transform: translateY(-4px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .quick-access {
        background: rgba(15, 23, 42, 0.8);
        padding: 2.5rem 2rem;
        margin: 2rem 0;
        border-radius: 1rem;
        border: 1px solid rgba(59, 130, 246, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }
    
    .stat-card {
        background: rgba(30, 41, 59, 0.9);
        border: 1px solid rgba(75, 85, 99, 0.5);
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        border-color: rgba(59, 130, 246, 0.5);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: rgba(156, 163, 175, 1);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 3rem 0;
    }
    
    .feature-card {
        background: rgba(30, 41, 59, 0.9);
        border: 1px solid rgba(75, 85, 99, 0.5);
        border-radius: 1rem;
        padding: 2rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .feature-card:hover {
        transform: translateY(-4px);
        border-color: rgba(59, 130, 246, 0.5);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
    }
    
    .feature-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    }
    
    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .section-subtitle {
        font-size: 1.1rem;
        color: rgba(156, 163, 175, 1);
        text-align: center;
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .status-operational {
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }
    
    .pulse {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .pathway-selector {
            padding: 1.5rem;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .feature-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Navigation -->
<nav class="navbar">
    <div class="max-w-7xl mx-auto px-4 flex justify-between items-center">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <span class="text-white font-semibold text-xl">ComplianceMax V74</span>
            <span class="status-indicator status-operational">
                <span class="w-2 h-2 bg-green-400 rounded-full pulse"></span>
                Operational
            </span>
        </div>
        <div class="flex items-center space-x-6">
            <a href="{{ url_for('emergency_work') }}" class="text-gray-300 hover:text-white transition-colors">Emergency Work</a>
            <a href="{{ url_for('cbcs_pathway') }}" class="text-gray-300 hover:text-white transition-colors">CBCS Work</a>
            <a href="{{ url_for('professional_intake_page') }}" class="text-gray-300 hover:text-white transition-colors">Professional Intake</a>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<div class="hero-section">
    <div class="max-w-6xl mx-auto">
        <h1 class="hero-title">
            Streamline Your Public Assistance<br>
            Disaster Recovery Compliance Process
        </h1>
        <p class="hero-description">
            Advanced multi-step wizard system with intelligent compliance pods, comprehensive document management, 
            and real-time guidance powered by 53,000+ FEMA policy records.
        </p>
        <div class="flex justify-center space-x-6 mb-6">
            <a href="{{ url_for('emergency_work') }}" class="btn-primary">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Emergency Work (A&B)
            </a>
            <a href="{{ url_for('cbcs_pathway') }}" class="btn-secondary">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                CBCS Work (C-G)
            </a>
        </div>
    </div>
</div>

<!-- System Statistics -->
{% if db_stats %}
<div class="max-w-6xl mx-auto px-4">
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number text-blue-400">{{ db_stats.total_records|default(53048)|number_format }}</span>
            <span class="stat-label">FEMA Records</span>
        </div>
        <div class="stat-card">
            <span class="stat-number text-green-400">{{ db_stats.categories|length|default(7) }}</span>
            <span class="stat-label">Categories (A-G)</span>
        </div>
        <div class="stat-card">
            <span class="stat-number text-yellow-400">{{ db_stats.policy_versions|length|default(5) }}</span>
            <span class="stat-label">PAPPG Versions</span>
        </div>
        <div class="stat-card">
            <span class="stat-number text-purple-400">{{ db_stats.status|default('Active')|title }}</span>
            <span class="stat-label">System Status</span>
        </div>
    </div>
</div>
{% endif %}

<!-- Pathway Selection -->
<div class="quick-access">
    <div class="max-w-6xl mx-auto">
        <h2 class="section-title">Choose Your Compliance Pathway</h2>
        <p class="section-subtitle">
            Select the appropriate workflow based on your project category and requirements
        </p>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <a href="{{ url_for('emergency_work') }}" class="pathway-selector text-left block">
                <div class="flex items-start space-x-6">
                    <div class="w-16 h-16 bg-red-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <span class="text-white font-bold text-xl">A&B</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-xl font-semibold text-white mb-2">Emergency Work</h4>
                        <p class="text-gray-400 mb-3">Categories A & B - Debris removal and emergency protective measures</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-red-600 bg-opacity-20 text-red-300 rounded text-sm">Debris Removal</span>
                            <span class="px-2 py-1 bg-red-600 bg-opacity-20 text-red-300 rounded text-sm">Emergency Protection</span>
                            <span class="px-2 py-1 bg-red-600 bg-opacity-20 text-red-300 rounded text-sm">Immediate Response</span>
                        </div>
                    </div>
                </div>
            </a>

            <a href="{{ url_for('cbcs_pathway') }}" class="pathway-selector text-left block">
                <div class="flex items-start space-x-6">
                    <div class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <span class="text-white font-bold text-xl">C-G</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-xl font-semibold text-white mb-2">CBCS Permanent Work</h4>
                        <p class="text-gray-400 mb-3">Categories C-G - Infrastructure rebuilding with comprehensive compliance</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-green-600 bg-opacity-20 text-green-300 rounded text-sm">Infrastructure</span>
                            <span class="px-2 py-1 bg-green-600 bg-opacity-20 text-green-300 rounded text-sm">Permanent Repair</span>
                            <span class="px-2 py-1 bg-green-600 bg-opacity-20 text-green-300 rounded text-sm">Code Compliance</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Professional Intake Option -->
        <div class="mt-8 text-center">
            <a href="{{ url_for('professional_intake_page') }}" class="pathway-selector inline-block text-center" style="max-width: 500px;">
                <div class="flex items-center justify-center space-x-4">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-1">Professional Compliance Analysis</h4>
                        <p class="text-gray-400 text-sm">Comprehensive intake for complex projects requiring detailed analysis</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Key Features -->
<div class="max-w-6xl mx-auto px-4 py-8">
    <h2 class="section-title">Advanced Compliance Features</h2>
    <p class="section-subtitle">
        Powered by Phase 8 database integration and intelligent wizard technology
    </p>
    
    <div class="feature-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">Intelligent Wizard System</h3>
            <p class="text-gray-400">Advanced multi-step guidance with context-aware compliance pods and real-time validation.</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">Enhanced Database</h3>
            <p class="text-gray-400">53,000+ FEMA policy records with full-text search and intelligent categorization.</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">Automated Compliance</h3>
            <p class="text-gray-400">Real-time validation against PAPPG requirements with automated code selection and verification.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Enhanced dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
    
    // Add loading states for pathway buttons
    document.querySelectorAll('.pathway-selector').forEach(selector => {
        selector.addEventListener('click', function(e) {
            const button = this;
            button.style.opacity = '0.7';
            button.style.transform = 'scale(0.98)';
            
            // Reset after navigation
            setTimeout(() => {
                button.style.opacity = '1';
                button.style.transform = 'scale(1)';
            }, 200);
        });
    });
    
    // Animate stat numbers on scroll
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.querySelector('.stat-number');
                if (statNumber && !statNumber.classList.contains('animated')) {
                    statNumber.classList.add('animated');
                    animateNumber(statNumber);
                }
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.stat-card').forEach(card => {
        observer.observe(card);
    });
    
    function animateNumber(element) {
        const finalNumber = element.textContent.replace(/,/g, '');
        const isNumeric = !isNaN(finalNumber);
        
        if (isNumeric) {
            const duration = 1500;
            const start = 0;
            const end = parseInt(finalNumber);
            const startTime = performance.now();
            
            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(start + (end - start) * progress);
                
                element.textContent = current.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }
            
            requestAnimationFrame(updateNumber);
        }
    }
});
</script>
{% endblock %} 
{% endblock %}

{% block extra_js %}
<!-- Additional JavaScript (optional) -->
{% endblock %}

import React, { useEffect } from 'react';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/lab';
import {
  Card,
  CardContent,
  Typography,
  Chip,
  Box,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import {
  Create as CreateIcon,
  Update as UpdateIcon,
  Delete as DeleteIcon,
  Assignment as AssignmentIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import { useHistory } from '../../hooks/useHistory';

interface ReviewHistoryProps {
  reviewId: number;
}

export const ReviewHistory: React.FC<ReviewHistoryProps> = ({ reviewId }) => {
  const { history, loading, error, fetchHistory } = useHistory();

  useEffect(() => {
    fetchHistory(reviewId);
  }, [reviewId]);

  if (loading) {
    return <Typography>Loading history...</Typography>;
  }

  if (error) {
    return <Typography color="error">Error loading history: {error.message}</Typography>;
  }

  const getChangeIcon = (changeType: string) => {
    switch (changeType) {
      case 'create':
        return <CreateIcon />;
      case 'update':
        return <UpdateIcon />;
      case 'delete':
        return <DeleteIcon />;
      case 'status_change':
        return <AssignmentIcon />;
      case 'document_upload':
        return <DescriptionIcon />;
      default:
        return <UpdateIcon />;
    }
  };

  const getChangeColor = (changeType: string) => {
    switch (changeType) {
      case 'create':
        return 'success';
      case 'update':
        return 'info';
      case 'delete':
        return 'error';
      case 'status_change':
        return 'warning';
      case 'document_upload':
        return 'primary';
      default:
        return 'default';
    }
  };

  const formatChanges = (previousState: any, newState: any) => {
    const changes: string[] = [];
    const keys = new Set([...Object.keys(previousState || {}), ...Object.keys(newState || {})]);

    keys.forEach(key => {
      const oldValue = previousState?.[key];
      const newValue = newState?.[key];

      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changes.push(`${key}: ${JSON.stringify(oldValue)} → ${JSON.stringify(newValue)}`);
      }
    });

    return changes;
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Review History
        </Typography>
        <Timeline position="alternate">
          {history.map((entry) => (
            <TimelineItem key={entry.id}>
              <TimelineOppositeContent>
                <Typography variant="body2" color="textSecondary">
                  {new Date(entry.timestamp).toLocaleString()}
                </Typography>
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot color={getChangeColor(entry.change_type)}>
                  {getChangeIcon(entry.change_type)}
                </TimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <TimelineContent>
                <Paper elevation={3} sx={{ p: 2, mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Chip
                      label={entry.change_type.replace('_', ' ').toUpperCase()}
                      color={getChangeColor(entry.change_type)}
                      size="small"
                    />
                    <Typography variant="caption">
                      User ID: {entry.user_id}
                    </Typography>
                  </Box>
                  {entry.comment && (
                    <Typography variant="body2" gutterBottom>
                      {entry.comment}
                    </Typography>
                  )}
                  {entry.previous_state && entry.new_state && (
                    <Box mt={1}>
                      <Typography variant="caption" color="textSecondary">
                        Changes:
                      </Typography>
                      <ul style={{ margin: 0, paddingLeft: 20 }}>
                        {formatChanges(entry.previous_state, entry.new_state).map((change, index) => (
                          <li key={index}>
                            <Typography variant="body2">
                              {change}
                            </Typography>
                          </li>
                        ))}
                      </ul>
                    </Box>
                  )}
                </Paper>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </CardContent>
    </Card>
  );
};

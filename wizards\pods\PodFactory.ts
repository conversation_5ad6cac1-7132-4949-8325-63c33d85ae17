/**
 * ComplianceMax V74 - Pod Factory
 * 
 * Manages creation and coordination of Compliance Pods
 * Replaces the juvenile ABACUS system with professional workflow management
 */

import { CompliancePod, Category, CompliancePodConfig } from './compliance/CompliancePod';

export interface Project {
  id: string;
  name: string;
  disasterId: string;
  incidentDate: Date;
  disasterType: string;
  location: string;
  applicantType: string;
  categories: Category['code'][];
}

export interface PodManager {
  project: Project;
  pods: Map<Category['code'], CompliancePod>;
  activeCategory: Category['code'] | null;
  overallProgress: number;
}

export class PodFactory {
  private podManagers: Map<string, PodManager> = new Map();

  // FEMA PA Category definitions
  private readonly CATEGORIES: Category[] = [
    { code: 'A', name: 'Debris Removal', description: 'Removal of debris and wreckage' },
    { code: 'B', name: 'Emergency Protective Measures', description: 'Emergency actions to save lives and protect property' },
    { code: 'C', name: 'Roads and Bridges', description: 'Repair and replacement of roads, bridges, and related facilities' },
    { code: 'D', name: 'Water Control Facilities', description: 'Repair of water control facilities' },
    { code: 'E', name: 'Buildings and Equipment', description: 'Repair and replacement of buildings and equipment' },
    { code: 'F', name: 'Utilities', description: 'Repair and replacement of utility systems' },
    { code: 'G', name: 'Parks and Other', description: 'Repair of parks, recreational facilities, and other items' }
  ];

  /**
   * Create a new project with compliance pods for specified categories
   */
  async createProject(project: Project): Promise<PodManager> {
    console.log(`🏗️ Creating project: ${project.name} for categories [${project.categories.join(', ')}]`);

    const podManager: PodManager = {
      project,
      pods: new Map(),
      activeCategory: null,
      overallProgress: 0
    };

    // Create pods for each requested category
    for (const categoryCode of project.categories) {
      const category = this.CATEGORIES.find(c => c.code === categoryCode);
      if (!category) {
        console.warn(`⚠️ Unknown category: ${categoryCode}`);
        continue;
      }

      const podConfig: CompliancePodConfig = {
        category,
        disaster: {
          id: project.disasterId,
          location: project.location,
          incidentDate: project.incidentDate,
          type: project.disasterType
        },
        project: {
          id: project.id,
          applicantType: project.applicantType,
          location: project.location
        }
      };

      const pod = new CompliancePod(podConfig);
      
      // Auto-populate the pod
      await pod.autoPopulate();
      
      podManager.pods.set(categoryCode, pod);
      console.log(`✅ Created and populated pod for Category ${categoryCode}`);
    }

    // Set first category as active
    if (project.categories.length > 0) {
      podManager.activeCategory = project.categories[0];
    }

    // Calculate initial progress
    podManager.overallProgress = await this.calculateOverallProgress(podManager);

    // Store the pod manager
    this.podManagers.set(project.id, podManager);

    console.log(`🎯 Project created with ${podManager.pods.size} pods, overall progress: ${podManager.overallProgress}%`);
    return podManager;
  }

  /**
   * Get pod manager for a project
   */
  getProject(projectId: string): PodManager | null {
    return this.podManagers.get(projectId) || null;
  }

  /**
   * Get specific compliance pod
   */
  getPod(projectId: string, categoryCode: Category['code']): CompliancePod | null {
    const manager = this.getProject(projectId);
    return manager?.pods.get(categoryCode) || null;
  }

  /**
   * Process review for a specific category pod
   */
  async processReview(
    projectId: string, 
    categoryCode: Category['code'], 
    reviewData: any
  ): Promise<{
    success: boolean;
    podResult: any;
    overallProgress: number;
    nextRecommendation: string;
  }> {
    const pod = this.getPod(projectId, categoryCode);
    if (!pod) {
      throw new Error(`Pod not found for project ${projectId}, category ${categoryCode}`);
    }

    console.log(`🔄 Processing review for Category ${categoryCode} in project ${projectId}`);

    // Process the review in the pod
    const podResult = await pod.processReview(reviewData);

    // Update overall progress
    const manager = this.getProject(projectId)!;
    manager.overallProgress = await this.calculateOverallProgress(manager);

    // Determine next recommendation
    const nextRecommendation = await this.getNextRecommendation(manager);

    return {
      success: podResult.success,
      podResult,
      overallProgress: manager.overallProgress,
      nextRecommendation
    };
  }

  /**
   * Switch active category for a project
   */
  async switchActiveCategory(projectId: string, categoryCode: Category['code']): Promise<boolean> {
    const manager = this.getProject(projectId);
    if (!manager || !manager.pods.has(categoryCode)) {
      return false;
    }

    manager.activeCategory = categoryCode;
    console.log(`🔄 Switched active category to ${categoryCode} for project ${projectId}`);
    return true;
  }

  /**
   * Get comprehensive project status
   */
  async getProjectStatus(projectId: string): Promise<{
    project: Project;
    overallProgress: number;
    activeCategory: Category['code'] | null;
    categoryStatuses: Array<{
      category: Category;
      status: any;
      progress: number;
    }>;
    nextActions: string[];
    blockers: string[];
  }> {
    const manager = this.getProject(projectId);
    if (!manager) {
      throw new Error(`Project not found: ${projectId}`);
    }

    const categoryStatuses = [];
    const nextActions = [];
    const blockers = [];

    for (const [categoryCode, pod] of manager.pods) {
      const category = this.CATEGORIES.find(c => c.code === categoryCode)!;
      const status = pod.getStatus();
      
      categoryStatuses.push({
        category,
        status,
        progress: status.complianceScore
      });

      // Analyze status for next actions and blockers
      if (status.complianceScore < 50) {
        blockers.push(`Category ${categoryCode}: Low compliance score (${status.complianceScore}%)`);
      }

      if (status.currentStep !== 'completed') {
        nextActions.push(`Category ${categoryCode}: Complete ${status.currentStep}`);
      }
    }

    return {
      project: manager.project,
      overallProgress: manager.overallProgress,
      activeCategory: manager.activeCategory,
      categoryStatuses,
      nextActions,
      blockers
    };
  }

  /**
   * Calculate overall progress across all pods
   */
  private async calculateOverallProgress(manager: PodManager): Promise<number> {
    if (manager.pods.size === 0) return 0;

    let totalProgress = 0;
    for (const pod of manager.pods.values()) {
      const status = pod.getStatus();
      totalProgress += status.complianceScore;
    }

    return Math.round(totalProgress / manager.pods.size);
  }

  /**
   * Get next recommended action for the project
   */
  private async getNextRecommendation(manager: PodManager): Promise<string> {
    const recommendations = [];

    for (const [categoryCode, pod] of manager.pods) {
      const status = pod.getStatus();
      
      if (status.complianceScore < 30) {
        recommendations.push(`URGENT: Complete basic requirements for Category ${categoryCode}`);
      } else if (status.complianceScore < 70) {
        recommendations.push(`Focus on Category ${categoryCode} compliance (${status.complianceScore}%)`);
      } else if (status.currentStep !== 'completed') {
        recommendations.push(`Advance Category ${categoryCode} to next step: ${status.currentStep}`);
      }
    }

    if (recommendations.length === 0) {
      return 'All categories are progressing well. Continue with current workflows.';
    }

    // Return highest priority recommendation
    return recommendations[0];
  }

  /**
   * Export all project data for backup/persistence
   */
  public exportProjectData(projectId: string): any {
    const manager = this.getProject(projectId);
    if (!manager) return null;

    const podData: { [categoryCode: string]: any } = {};
    
    for (const [categoryCode, pod] of manager.pods) {
      podData[categoryCode] = pod.exportState();
    }

    return {
      project: manager.project,
      activeCategory: manager.activeCategory,
      overallProgress: manager.overallProgress,
      pods: podData,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import project data from backup/persistence
   */
  public async importProjectData(projectData: any): Promise<string> {
    const project = projectData.project;
    const manager: PodManager = {
      project,
      pods: new Map(),
      activeCategory: projectData.activeCategory,
      overallProgress: projectData.overallProgress
    };

    // Recreate pods from exported data
    for (const [categoryCode, podData] of Object.entries(projectData.pods)) {
      const category = this.CATEGORIES.find(c => c.code === categoryCode);
      if (!category) continue;

      const podConfig: CompliancePodConfig = {
        category,
        disaster: {
          id: project.disasterId,
          location: project.location,
          incidentDate: new Date(project.incidentDate),
          type: project.disasterType
        },
        project: {
          id: project.id,
          applicantType: project.applicantType,
          location: project.location
        }
      };

      const pod = new CompliancePod(podConfig);
      pod.importState(podData);
      
      manager.pods.set(categoryCode as Category['code'], pod);
    }

    this.podManagers.set(project.id, manager);
    console.log(`📥 Imported project: ${project.name} with ${manager.pods.size} pods`);
    
    return project.id;
  }

  /**
   * List all projects
   */
  public listProjects(): Array<{
    id: string;
    name: string;
    categories: Category['code'][];
    progress: number;
    status: string;
  }> {
    const projects = [];
    
    for (const [projectId, manager] of this.podManagers) {
      projects.push({
        id: projectId,
        name: manager.project.name,
        categories: Array.from(manager.pods.keys()),
        progress: manager.overallProgress,
        status: manager.overallProgress >= 90 ? 'compliant' : 
                manager.overallProgress >= 70 ? 'mostly_compliant' :
                manager.overallProgress >= 50 ? 'partially_compliant' : 'non_compliant'
      });
    }

    return projects;
  }

  /**
   * Get available categories
   */
  public getAvailableCategories(): Category[] {
    return [...this.CATEGORIES];
  }
} 
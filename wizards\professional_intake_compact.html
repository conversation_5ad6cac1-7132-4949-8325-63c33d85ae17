<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Compliance Analysis - Compact | ComplianceMax V74</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='compliancemax-unified.css') }}">
    <style>
        body { margin: 0; padding: 0; font-size: 11px; height: 100vh; overflow: hidden; }
        .professional-intake { height: 100vh; background: var(--primary-gradient); display: grid; grid-template-rows: 45px 1fr; }
        .intake-header { background: white; padding: 6px 12px; display: flex; align-items: center; justify-content: space-between; box-shadow: var(--shadow-md); }
        .intake-header h1 { margin: 0; font-size: 14px; color: var(--text-primary); }
        .nav-buttons { display: flex; gap: 6px; }
        .nav-btn { padding: 3px 8px; border: none; border-radius: 3px; font-size: 9px; font-weight: 600; text-decoration: none; color: white; }
        .nav-btn.home { background: var(--primary-blue); }
        .nav-btn.dashboard { background: var(--success-green); }
        .intake-form { background: white; margin: 4px; border-radius: 4px; display: grid; grid-template-columns: 1fr 1fr 1fr; height: calc(100vh - 60px); }
        .column { padding: 8px; overflow-y: auto; border-right: 1px solid var(--border-light); }
        .column:last-child { border-right: none; }
        .section-title { font-size: 12px; margin: 0 0 6px 0; border-bottom: 2px solid var(--primary-blue); padding-bottom: 3px; font-weight: 700; }
        .form-group { margin-bottom: 8px; }
        .form-label { display: block; font-weight: 600; margin-bottom: 2px; font-size: 10px; }
        .form-control { width: 100%; padding: 3px 4px; border: 1px solid var(--border-medium); border-radius: 2px; font-size: 9px; }
        .btn { padding: 4px 8px; border: none; border-radius: 3px; font-size: 9px; font-weight: 600; width: 100%; margin-bottom: 4px; cursor: pointer; }
        .btn.primary { background: var(--primary-gradient); color: white; }
        .btn.success { background: var(--success-green); color: white; }
        .btn.warning { background: var(--warning-orange); color: white; }
    </style>
</head>
<body>
    <div class="professional-intake">
        <header class="intake-header">
            <h1>Professional Compliance Analysis</h1>
            <div class="cm-badges">
                <span class="cm-badge policy">PAPPG v5.0</span>
                <span class="cm-badge database">53,048 Records</span>
            </div>
            <div class="nav-buttons">
                <a href="/" class="nav-btn home">🏠 Home</a>
                <a href="/dashboard" class="nav-btn dashboard">📊 Dashboard</a>
            </div>
        </header>
        
        <div class="intake-form">
            <div class="column">
                <h2 class="section-title">Project Details</h2>
                <div class="form-group">
                    <label class="form-label">Organization</label>
                    <input type="text" id="organization" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Project Name</label>
                    <input type="text" id="projectName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Work Category</label>
                    <select id="workCategory" class="form-control" required>
                        <option value="">Select category</option>
                        <option value="C">Category C - Roads & Bridges</option>
                        <option value="E">Category E - Buildings & Equipment</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Estimated Cost</label>
                    <input type="number" id="estimatedCost" class="form-control" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Event Date</label>
                    <input type="date" id="eventDate" class="form-control" required>
                </div>
            </div>
            
            <div class="column">
                <h2 class="section-title">CBCS Codes</h2>
                <button type="button" class="btn warning" onclick="autoPopulate()">🔄 Auto-Populate</button>
                <div style="margin-top: 8px;">
                    <label style="display: flex; align-items: center; margin-bottom: 4px; font-size: 9px;">
                        <input type="checkbox" id="aashto_lrfd" style="margin-right: 4px;">
                        AASHTO LRFD Bridge Design
                    </label>
                    <label style="display: flex; align-items: center; margin-bottom: 4px; font-size: 9px;">
                        <input type="checkbox" id="asce_7" style="margin-right: 4px;">
                        ASCE 7-16 Design Loads
                    </label>
                    <label style="display: flex; align-items: center; margin-bottom: 4px; font-size: 9px;">
                        <input type="checkbox" id="ibc" style="margin-right: 4px;">
                        International Building Code
                    </label>
                </div>
                <button type="button" class="btn success" onclick="generateDocs()">📋 Generate Documentation</button>
            </div>
            
            <div class="column">
                <h2 class="section-title">Professional Analysis</h2>
                <div id="output" style="background: #f8f9fa; padding: 6px; border-radius: 3px; margin-bottom: 8px; min-height: 100px; font-size: 8px;">
                    Documentation will appear here
                </div>
                <button type="button" class="btn primary" onclick="submitAnalysis()">🚀 Submit for Analysis</button>
            </div>
        </div>
    </div>

    <div id="notifications"></div>

    <script>
        function showNotification(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `cm-notification ${type} show`;
            div.textContent = msg;
            div.style.cssText = `position: fixed; top: 20px; right: 20px; padding: 8px 12px; border-radius: 4px; color: white; z-index: 1000; font-size: 10px;`;
            div.style.background = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';
            document.body.appendChild(div);
            setTimeout(() => document.body.removeChild(div), 3000);
        }

        function autoPopulate() {
            const category = document.getElementById('workCategory').value;
            if (!category) { showNotification('Select work category first', 'error'); return; }
            
            showNotification('Auto-populating codes...', 'info');
            setTimeout(() => {
                if (category === 'C') {
                    document.getElementById('aashto_lrfd').checked = true;
                    document.getElementById('asce_7').checked = true;
                } else {
                    document.getElementById('asce_7').checked = true;
                    document.getElementById('ibc').checked = true;
                }
                showNotification('Codes auto-populated successfully', 'success');
            }, 1000);
        }

        function generateDocs() {
            const selected = document.querySelectorAll('input[type="checkbox"]:checked');
            if (selected.length === 0) { showNotification('Select at least one code', 'error'); return; }
            
            showNotification('Generating professional documentation...', 'info');
            setTimeout(() => {
                document.getElementById('output').innerHTML = `
                    <strong>PROFESSIONAL TECHNICAL ANALYSIS</strong><br>
                    <em>Generated for ${selected.length} selected codes</em><br><br>
                    • Load Factor Design methodology per AASHTO LRFD required<br>
                    • Service Level I loading combinations mandatory<br>
                    • Environmental loading per ASCE 7 supersedes local codes<br>
                    • Professional engineer review and stamping required<br><br>
                    <strong>Cost Impact:</strong> 5-15% increase over baseline<br>
                    <strong>Timeline:</strong> 2-4 week extension for analysis
                `;
                showNotification('Professional documentation generated', 'success');
            }, 1500);
        }

        function submitAnalysis() {
            const required = ['organization', 'projectName', 'workCategory', 'estimatedCost', 'eventDate'];
            const missing = required.filter(id => !document.getElementById(id).value);
            
            if (missing.length > 0) { showNotification('Fill in all required fields', 'error'); return; }
            
            const selected = document.querySelectorAll('input[type="checkbox"]:checked');
            if (selected.length === 0) { showNotification('Select at least one CBCS code', 'error'); return; }
            
            showNotification('Submitting for professional analysis...', 'info');
            setTimeout(() => {
                showNotification('Professional intake submitted successfully! Analysis ID: PROF-' + Date.now(), 'success');
                // Reset form
                document.querySelectorAll('input, select').forEach(el => el.value = '');
                document.querySelectorAll('input[type="checkbox"]').forEach(el => el.checked = false);
                document.getElementById('output').innerHTML = 'Documentation will appear here';
            }, 2000);
        }
    </script>
</body>
</html> 
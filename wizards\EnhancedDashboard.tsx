import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, Card, CardContent,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent,
  Button, Tabs, Tab, CircularProgress, Divider
} from '@mui/material';
import {
  LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell,
  AreaChart, Area
} from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Sample data for trend analysis
const complianceTrendData = [
  { month: 'Jan', year: '2025', compliance: 75, policies: 32, documents: 85 },
  { month: 'Feb', year: '2025', compliance: 78, policies: 35, documents: 90 },
  { month: 'Mar', year: '2025', compliance: 82, policies: 38, documents: 95 },
  { month: 'Apr', year: '2025', compliance: 85, policies: 40, documents: 105 },
  { month: 'May', year: '2025', compliance: 83, policies: 42, documents: 110 },
  { month: 'Jun', year: '2025', compliance: 87, policies: 45, documents: 115 },
  { month: 'Jul', year: '2025', compliance: 90, policies: 48, documents: 120 },
  { month: 'Aug', year: '2025', compliance: 88, policies: 50, documents: 125 },
  { month: 'Sep', year: '2025', compliance: 92, policies: 52, documents: 130 },
  { month: 'Oct', year: '2025', compliance: 91, policies: 55, documents: 135 },
  { month: 'Nov', year: '2025', compliance: 94, policies: 58, documents: 140 },
  { month: 'Dec', year: '2025', compliance: 95, policies: 60, documents: 145 },
];

const categoryTrendData = [
  { 
    month: 'Jan', 
    'Debris Removal': 70, 
    'Emergency Measures': 75, 
    'Roads & Bridges': 65, 
    'Water Control': 60, 
    'Buildings': 80 
  },
  { 
    month: 'Feb', 
    'Debris Removal': 72, 
    'Emergency Measures': 78, 
    'Roads & Bridges': 68, 
    'Water Control': 62, 
    'Buildings': 82 
  },
  { 
    month: 'Mar', 
    'Debris Removal': 75, 
    'Emergency Measures': 80, 
    'Roads & Bridges': 70, 
    'Water Control': 65, 
    'Buildings': 85 
  },
  { 
    month: 'Apr', 
    'Debris Removal': 78, 
    'Emergency Measures': 82, 
    'Roads & Bridges': 73, 
    'Water Control': 68, 
    'Buildings': 87 
  },
  { 
    month: 'May', 
    'Debris Removal': 80, 
    'Emergency Measures': 85, 
    'Roads & Bridges': 75, 
    'Water Control': 70, 
    'Buildings': 90 
  },
  { 
    month: 'Jun', 
    'Debris Removal': 82, 
    'Emergency Measures': 87, 
    'Roads & Bridges': 78, 
    'Water Control': 72, 
    'Buildings': 92 
  },
];

const policyMatchTrendData = [
  { month: 'Jan', high: 25, medium: 35, low: 15 },
  { month: 'Feb', high: 28, medium: 32, low: 18 },
  { month: 'Mar', high: 30, medium: 30, low: 20 },
  { month: 'Apr', high: 35, medium: 28, low: 22 },
  { month: 'May', high: 38, medium: 25, low: 20 },
  { month: 'Jun', high: 42, medium: 22, low: 18 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const EnhancedDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('6m');
  const [loading, setLoading] = useState(false);
  const [dataType, setDataType] = useState('compliance');
  const [comparisonMode, setComparisonMode] = useState('none');
  
  // Filter data based on time range
  const getFilteredData = (data: any[], range: string) => {
    const now = new Date();
    const months = range === '1m' ? 1 : 
                  range === '3m' ? 3 : 
                  range === '6m' ? 6 : 
                  range === '1y' ? 12 : 12;
    
    // For demo purposes, just return the last X items
    return data.slice(-months);
  };
  
  const filteredComplianceData = getFilteredData(complianceTrendData, timeRange);
  const filteredCategoryData = getFilteredData(categoryTrendData, timeRange);
  const filteredPolicyMatchData = getFilteredData(policyMatchTrendData, timeRange);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setLoading(true);
    setTimeRange(event.target.value);
    
    // Simulate loading data
    setTimeout(() => {
      setLoading(false);
    }, 800);
  };
  
  const handleDataTypeChange = (event: SelectChangeEvent) => {
    setDataType(event.target.value);
  };
  
  const handleComparisonModeChange = (event: SelectChangeEvent) => {
    setComparisonMode(event.target.value);
  };
  
  // Calculate trend indicators
  const calculateTrend = (data: any[], key: string) => {
    if (data.length < 2) return { value: 0, direction: 'neutral' };
    
    const current = data[data.length - 1][key];
    const previous = data[data.length - 2][key];
    const change = current - previous;
    const percentChange = (change / previous) * 100;
    
    return {
      value: Math.abs(percentChange).toFixed(1),
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
    };
  };
  
  const complianceTrend = calculateTrend(filteredComplianceData, 'compliance');
  const policiesTrend = calculateTrend(filteredComplianceData, 'policies');
  const documentsTrend = calculateTrend(filteredComplianceData, 'documents');
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Enhanced Dashboard
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Compliance Trends" />
          <Tab label="Category Analysis" />
          <Tab label="Policy Matching Trends" />
        </Tabs>
      </Paper>
      
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={handleTimeRangeChange}
              >
                <MenuItem value="1m">Last Month</MenuItem>
                <MenuItem value="3m">Last 3 Months</MenuItem>
                <MenuItem value="6m">Last 6 Months</MenuItem>
                <MenuItem value="1y">Last Year</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Data Type</InputLabel>
              <Select
                value={dataType}
                label="Data Type"
                onChange={handleDataTypeChange}
              >
                <MenuItem value="compliance">Compliance Score</MenuItem>
                <MenuItem value="policies">Policy Count</MenuItem>
                <MenuItem value="documents">Document Count</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Comparison</InputLabel>
              <Select
                value={comparisonMode}
                label="Comparison"
                onChange={handleComparisonModeChange}
              >
                <MenuItem value="none">No Comparison</MenuItem>
                <MenuItem value="previous">Previous Period</MenuItem>
                <MenuItem value="target">Target Values</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Overall Compliance
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <Typography variant="h3" color="primary">
                  {filteredComplianceData[filteredComplianceData.length - 1].compliance}%
                </Typography>
                <Typography 
                  variant="body2" 
                  color={complianceTrend.direction === 'up' ? 'success.main' : 'error.main'}
                  sx={{ ml: 1 }}
                >
                  {complianceTrend.direction === 'up' ? '↑' : '↓'} {complianceTrend.value}%
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                from previous {timeRange === '1m' ? 'month' : timeRange === '3m' ? 'quarter' : 'period'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Policies
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <Typography variant="h3" color="primary">
                  {filteredComplianceData[filteredComplianceData.length - 1].policies}
                </Typography>
                <Typography 
                  variant="body2" 
                  color={policiesTrend.direction === 'up' ? 'success.main' : 'error.main'}
                  sx={{ ml: 1 }}
                >
                  {policiesTrend.direction === 'up' ? '↑' : '↓'} {policiesTrend.value}%
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                from previous {timeRange === '1m' ? 'month' : timeRange === '3m' ? 'quarter' : 'period'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Documents
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                <Typography variant="h3" color="primary">
                  {filteredComplianceData[filteredComplianceData.length - 1].documents}
                </Typography>
                <Typography 
                  variant="body2" 
                  color={documentsTrend.direction === 'up' ? 'success.main' : 'error.main'}
                  sx={{ ml: 1 }}
                >
                  {documentsTrend.direction === 'up' ? '↑' : '↓'} {documentsTrend.value}%
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                from previous {timeRange === '1m' ? 'month' : timeRange === '3m' ? 'quarter' : 'period'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Trend Charts */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <TabPanel value={tabValue} index={0}>
            <Paper sx={{ p: 2, height: 500 }}>
              <Typography variant="h6" gutterBottom>
                Compliance Score Trend Analysis
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <AreaChart
                  data={filteredComplianceData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Area 
                    type="monotone" 
                    dataKey={dataType} 
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    fillOpacity={0.3} 
                    activeDot={{ r: 8 }} 
                  />
                  {comparisonMode === 'previous' && (
                    <Area 
                      type="monotone" 
                      dataKey={`previous${dataType.charAt(0).toUpperCase() + dataType.slice(1)}`} 
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      fillOpacity={0.3} 
                    />
                  )}
                  {comparisonMode === 'target' && (
                    <Line 
                      type="monotone" 
                      dataKey="target" 
                      stroke="#ff7300" 
                      strokeDasharray="5 5" 
                    />
                  )}
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
            
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Year-over-Year Comparison
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart
                        data={[
                          { name: 'Q1', '2024': 72, '2025': 78 },
                          { name: 'Q2', '2024': 75, '2025': 85 },
                          { name: 'Q3', '2024': 78, '2025': 90 },
                          { name: 'Q4', '2024': 80, '2025': 95 }
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis domain={[0, 100]} />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="2024" fill="#8884d8" />
                        <Bar dataKey="2025" fill="#82ca9d" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Compliance Distribution
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Fully Compliant', value: 65 },
                            { name: 'Partially Compliant', value: 25 },
                            { name: 'Non-Compliant', value: 10 }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {[0, 1, 2].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
          
          <TabPanel value={tabValue} index={1}>
            <Paper sx={{ p: 2, height: 500 }}>
              <Typography variant="h6" gutterBottom>
                Compliance by Category Over Time
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <LineChart
                  data={filteredCategoryData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="Debris Removal" stroke="#8884d8" activeDot={{ r: 8 }} />
                  <Line type="monotone" dataKey="Emergency Measures" stroke="#82ca9d" />
                  <Line type="monotone" dataKey="Roads & Bridges" stroke="#ffc658" />
                  <Line type="monotone" dataKey="Water Control" stroke="#ff8042" />
                  <Line type="monotone" dataKey="Buildings" stroke="#0088fe" />
                </LineChart>
              </ResponsiveContainer>
            </Paper>
            
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Category Improvement
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart
                        layout="vertical"
                        data={[
                          { name: 'Debris Removal', improvement: 12 },
                          { name: 'Emergency Measures', improvement: 8 },
                          { name: 'Roads & Bridges', improvement: 15 },
                          { name: 'Water Control', improvement: 10 },
                          { name: 'Buildings', improvement: 5 }
                        ]}
                        margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" domain={[0, 20]} />
                        <YAxis dataKey="name" type="category" />
                        <Tooltip />
                        <Bar dataKey="improvement" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Current Category Status
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart
                        data={[
                          { name: 'Debris Removal', value: 82 },
                          { name: 'Emergency Measures', value: 87 },
                          { name: 'Roads & Bridges', value: 78 },
                          { name: 'Water Control', value: 72 },
                          { name: 'Buildings', value: 92 }
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis domain={[0, 100]} />
                        <Tooltip />
                        <Bar dataKey="value" fill="#82ca9d">
                          {[0, 1, 2, 3, 4].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
          
          <TabPanel value={tabValue} index={2}>
            <Paper sx={{ p: 2, height: 500 }}>
              <Typography variant="h6" gutterBottom>
                Policy Matching Confidence Trends
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <AreaChart
                  data={filteredPolicyMatchData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  stackOffset="expand"
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(tick) => `${(tick * 100).toFixed(0)}%`} />
                  <Tooltip formatter={(value, name) => [`${value} matches`, name]} />
                  <Legend />
                  <Area type="monotone" dataKey="high" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="High Confidence" />
                  <Area type="monotone" dataKey="medium" stackId="1" stroke="#ffc658" fill="#ffc658" name="Medium Confidence" />
                  <Area type="monotone" dataKey="low" stackId="1" stroke="#ff8042" fill="#ff8042" name="Low Confidence" />
                </AreaChart>
              </ResponsiveContainer>
            </Paper>
            
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Match Quality Improvement
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart
                        data={[
                          { month: 'Jan', quality: 65 },
                          { month: 'Feb', quality: 68 },
                          { month: 'Mar', quality: 72 },
                          { month: 'Apr', quality: 75 },
                          { month: 'May', quality: 80 },
                          { month: 'Jun', quality: 85 }
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis domain={[0, 100]} />
                        <Tooltip />
                        <Line type="monotone" dataKey="quality" stroke="#8884d8" activeDot={{ r: 8 }} />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Match Distribution by Policy Type
                    </Typography>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={[
                            { name: 'Security', value: 35 },
                            { name: 'Compliance', value: 25 },
                            { name: 'Operational', value: 20 },
                            { name: 'Financial', value: 15 },
                            { name: 'HR', value: 5 }
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {[0, 1, 2, 3, 4].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </>
      )}
    </Box>
  );
};

export default EnhancedDashboard;


'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  X, 
  FileText, 
  CheckCircle, 
  AlertTriangle 
} from 'lucide-react'
import { formatFileSize } from '@/lib/utils'

interface DocumentUploadProps {
  onClose: () => void
  onUpload: (documents: any[]) => void
}

interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  category: string
  description: string
}

const categories = [
  'Project Documentation',
  'Financial',
  'Environmental',
  'Evidence',
  'Legal',
  'Other'
]

export function DocumentUpload({ onClose, onUpload }: DocumentUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      progress: 0,
      status: 'pending' as const,
      category: 'Other',
      description: ''
    }))

    setUploadFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'text/plain': ['.txt']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  })

  const updateFile = (id: string, updates: Partial<UploadFile>) => {
    setUploadFiles(prev => prev.map(file => 
      file.id === id ? { ...file, ...updates } : file
    ))
  }

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id))
  }

  const simulateUpload = async (file: UploadFile): Promise<void> => {
    updateFile(file.id, { status: 'uploading' })

    return new Promise((resolve, reject) => {
      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 15
        if (progress >= 100) {
          progress = 100
          clearInterval(interval)
          updateFile(file.id, { progress: 100, status: 'completed' })
          setTimeout(resolve, 500)
        } else {
          updateFile(file.id, { progress })
        }
      }, 200)

      // Simulate occasional failures
      if (Math.random() < 0.05) {
        setTimeout(() => {
          clearInterval(interval)
          updateFile(file.id, { status: 'error' })
          reject(new Error('Upload failed'))
        }, 2000)
      }
    })
  }

  const handleUpload = async () => {
    if (uploadFiles.length === 0) return

    setIsUploading(true)

    try {
      // Upload all files
      await Promise.allSettled(
        uploadFiles.map(file => simulateUpload(file))
      )

      // Convert to document format and notify parent
      const documents = uploadFiles
        .filter(file => file.status === 'completed')
        .map(file => ({
          id: file.id,
          name: file.file.name,
          type: file.file.type,
          size: file.file.size,
          uploadedAt: new Date(),
          category: file.category,
          status: 'completed' as const,
          tags: [],
          description: file.description
        }))

      onUpload(documents)
    } catch (error) {
      console.error('Upload error:', error)
    } finally {
      setIsUploading(false)
    }
  }

  const canUpload = uploadFiles.length > 0 && uploadFiles.every(file => 
    file.category && file.category !== ''
  )

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-white">Upload Documents</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white/60 hover:text-white hover:bg-white/10"
          >
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="p-6 overflow-y-auto">
          <div className="space-y-6">
            {/* Upload Area */}
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive 
                  ? 'border-blue-400 bg-blue-400/10' 
                  : 'border-white/30 hover:border-white/50'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="w-12 h-12 text-white/60 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">
                {isDragActive ? 'Drop files here' : 'Upload Documents'}
              </h3>
              <p className="text-white/60 mb-4">
                Drag and drop files here, or click to select files
              </p>
              <p className="text-xs text-white/40">
                Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB each)
              </p>
            </div>

            {/* File List */}
            {uploadFiles.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-white font-medium">Files to Upload ({uploadFiles.length})</h3>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {uploadFiles.map((uploadFile) => (
                    <div key={uploadFile.id} className="p-4 bg-white/5 rounded-lg">
                      <div className="flex items-start gap-3">
                        <FileText className="w-5 h-5 text-white/60 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between gap-4">
                            <div className="min-w-0">
                              <p className="text-white font-medium truncate">
                                {uploadFile.file.name}
                              </p>
                              <p className="text-white/60 text-sm">
                                {formatFileSize(uploadFile.file.size)}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {uploadFile.status === 'completed' && (
                                <CheckCircle className="w-5 h-5 text-green-400" />
                              )}
                              {uploadFile.status === 'error' && (
                                <AlertTriangle className="w-5 h-5 text-red-400" />
                              )}
                              {uploadFile.status === 'uploading' && (
                                <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(uploadFile.id)}
                                disabled={uploadFile.status === 'uploading'}
                                className="text-white/60 hover:text-white hover:bg-white/10"
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>

                          {uploadFile.status === 'uploading' && (
                            <div className="mt-2">
                              <Progress value={uploadFile.progress} className="h-1" />
                              <p className="text-xs text-white/60 mt-1">
                                Uploading... {Math.round(uploadFile.progress)}%
                              </p>
                            </div>
                          )}

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                            <div>
                              <label className="block text-xs text-white/70 mb-1">
                                Category *
                              </label>
                              <select
                                value={uploadFile.category}
                                onChange={(e) => updateFile(uploadFile.id, { category: e.target.value })}
                                disabled={uploadFile.status === 'uploading'}
                                className="w-full px-2 py-1 bg-white/10 border border-white/20 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                <option value="" className="bg-gray-800">Select category</option>
                                {categories.map(category => (
                                  <option key={category} value={category} className="bg-gray-800">
                                    {category}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div>
                              <label className="block text-xs text-white/70 mb-1">
                                Description
                              </label>
                              <input
                                type="text"
                                value={uploadFile.description}
                                onChange={(e) => updateFile(uploadFile.id, { description: e.target.value })}
                                disabled={uploadFile.status === 'uploading'}
                                placeholder="Brief description..."
                                className="w-full px-2 py-1 bg-white/10 border border-white/20 rounded text-white text-sm placeholder-white/50 focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-white/20">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isUploading}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpload}
                disabled={!canUpload || isUploading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isUploading ? 'Uploading...' : `Upload ${uploadFiles.length} File(s)`}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

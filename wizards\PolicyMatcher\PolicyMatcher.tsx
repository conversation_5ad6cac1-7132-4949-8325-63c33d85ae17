import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material';
import UploadIcon from '@mui/icons-material/Upload';
import SearchIcon from '@mui/icons-material/Search';

const PolicyMatcher: React.FC = () => {
  const [requirementText, setRequirementText] = useState('');
  const [drNumber, setDrNumber] = useState('');
  const [incidentDate, setIncidentDate] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Call the backend API
      const formData = new FormData();
      formData.append('requirement_text', requirementText);
      formData.append('dr_number', drNumber);
      formData.append('incident_date', incidentDate);
      
      const response = await fetch('http://localhost:8000/api/v1/improved/policy-matcher/match', {
        method: 'POST',
        body: formData,
      }) ;
      
      if (!response.ok) {
        throw new Error('Failed to match policies');
      }
      
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Error matching policies:', error);
      // In a real app, you would show an error message to the user
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'success';
    if (score >= 0.6) return 'warning';
    return 'error';
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Policy Matcher
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Enter Requirements
              </Typography>
              
              <form onSubmit={handleSubmit}>
                <TextField
                  label="Requirement Text"
                  multiline
                  rows={6}
                  fullWidth
                  margin="normal"
                  value={requirementText}
                  onChange={(e) => setRequirementText(e.target.value)}
                  placeholder="Enter or paste requirement text here..."
                  required
                />
                
                <TextField
                  label="DR Number"
                  fullWidth
                  margin="normal"
                  value={drNumber}
                  onChange={(e) => setDrNumber(e.target.value)}
                  placeholder="e.g., DR-4999"
                  required
                />
                
                <TextField
                  label="Incident Date"
                  type="date"
                  fullWidth
                  margin="normal"
                  value={incidentDate}
                  onChange={(e) => setIncidentDate(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  required
                />
                
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  fullWidth
                  startIcon={<SearchIcon />}
                  disabled={loading}
                  sx={{ mt: 2 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Match Policies'}
                </Button>
              </form>
            </CardContent>
          </Card>
          
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upload Document
              </Typography>
              
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
                fullWidth
                sx={{ mb: 2 }}
              >
                Select Document
                <input
                  type="file"
                  hidden
                  accept=".pdf,.doc,.docx,.txt"
                />
              </Button>
              
              <Typography variant="body2" color="text.secondary">
                Supported formats: PDF, Word, Text
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Matching Results
              </Typography>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : results ? (
                <Box>
                  <Typography variant="body2" gutterBottom>
                    Found {results.match_count} matches across {results.total_requirements} requirements.
                  </Typography>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  {results.requirements.map((req: any, index: number) => (
                    <Paper key={index} sx={{ p: 2, mb: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        Requirement {req.id}
                      </Typography>
                      <Typography variant="body2" paragraph>
                        {req.text}
                      </Typography>
                      
                      {req.matches.length > 0 ? (
                        <List dense>
                          {req.matches.map((match: any, mIndex: number) => (
                            <ListItem key={mIndex} alignItems="flex-start">
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Typography variant="body2" fontWeight="bold">
                                      Policy {match.policy_id}
                                    </Typography>
                                    <Chip 
                                      label={`${(match.confidence * 100).toFixed(0)}% Match`}
                                      color={getConfidenceColor(match.confidence)}
                                      size="small"
                                    />
                                  </Box>
                                }
                                secondary={match.excerpt}
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No matching policies found for this requirement.
                        </Typography>
                      )}
                    </Paper>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ my: 4, textAlign: 'center' }}>
                  Enter requirements and click "Match Policies" to see results.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PolicyMatcher;

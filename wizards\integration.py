"""
Integration module for the improved ComplianceMax components.

This module demonstrates how to integrate the enhanced policy matcher, document processor,
and requirement parser into the main ComplianceMax application.
"""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession

# Import the enhanced components
from app.improved.policy_matcher_service import PolicyMatcherService
from app.improved.document_processor import DocumentProcessor
from app.improved.requirement_parser import RequirementParser

# Set up module logger
logger = logging.getLogger(__name__)

# Create API router
router = APIRouter()


@router.post("/policy-matcher/match", response_model=Dict[str, Any])
async def match_requirements_to_policies(
    requirement_text: str = Form(...),
    dr_number: str = Form(...),
    incident_date: str = Form(...),
    use_cache: bool = Form(True),
    db: AsyncSession = Depends(None)
):
    """
    Match requirements to relevant policies using advanced matching techniques.
    
    Args:
        requirement_text: Text containing requirements to match
        dr_number: Disaster declaration number
        incident_date: Date of the incident (ISO format)
        use_cache: Whether to use cached results
        
    Returns:
        Dictionary with matching results
    """
    try:
        # Initialize the policy matcher service
        policy_matcher = PolicyMatcherService(db)
        
        # Match requirements to policies
        results = await policy_matcher.match_requirements_to_policies(
            requirement_text=requirement_text,
            dr_number=dr_number,
            incident_date=incident_date,
            use_cache=use_cache
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Error in policy matching endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Policy matching error: {str(e)}")


@router.post("/document-processor/process", response_model=Dict[str, Any])
async def process_document_text(
    text: str = Form(...)
):
    """
    Process document text through the enhanced document processor.
    
    Args:
        text: Document text to process
        
    Returns:
        Dictionary with processing results
    """
    try:
        # Initialize the document processor
        document_processor = DocumentProcessor(use_cache=True)
        
        # Process the document text
        results = await document_processor.process_document_text(text)
        
        return results
        
    except Exception as e:
        logger.error(f"Error in document processing endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Document processing error: {str(e)}")


@router.post("/requirement-parser/parse", response_model=Dict[str, Any])
async def parse_requirements(
    text: str = Form(...)
):
    """
    Parse requirements from text using the enhanced requirement parser.
    
    Args:
        text: Text to parse requirements from
        
    Returns:
        Dictionary with parsed requirements
    """
    try:
        # Initialize the requirement parser
        requirement_parser = RequirementParser()
        
        # Parse requirements
        sections = requirement_parser.parse_requirements(text)
        
        # Analyze requirements
        phrases = requirement_parser.analyze_requirements()
        
        # Get requirement categories
        categories = requirement_parser.get_requirement_categories()
        
        return {
            "sections": sections,
            "phrases": phrases,
            "categories": categories
        }
        
    except Exception as e:
        logger.error(f"Error in requirement parsing endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Requirement parsing error: {str(e)}")


def register_improved_components(app):
    """
    Register the improved components with the main FastAPI application.
    
    Args:
        app: FastAPI application instance
    """
    # Include the router
    app.include_router(
        router,
        prefix="/api/v1/improved",
        tags=["improved"]
    )
    
    logger.info("Registered improved ComplianceMax components")

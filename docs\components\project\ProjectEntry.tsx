import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  TextField,
  Typography,
  MenuItem,
  CircularProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

interface DisasterData {
  disasterNumber: string;
  state: string;
  declarationDate: string;
  incidentBeginDate: string;
  incidentEndDate: string;
  designatedAreas: Array<{ countyName: string }>;
}

const ProjectEntry = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [disasterData, setDisasterData] = useState<DisasterData | null>(null);
  const [eligibleStates, setEligibleStates] = useState<string[]>([]);
  const [eligibleCounties, setEligibleCounties] = useState<string[]>([]);
  
  const [formData, setFormData] = useState({
    drNumber: '',
    projectName: '',
    state: '',
    county: '',
    startDate: null,
    endDate: null,
  });

  // Fetch disaster data when DR number changes
  useEffect(() => {
    const fetchDisasterData = async () => {
      if (!formData.drNumber) return;
      
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`/api/v1/fema/disasters/${formData.drNumber}`);
        if (!response.ok) throw new Error('Invalid disaster number');
        
        const data = await response.json();
        setDisasterData(data);
        
        // Update eligible states
        const states = [...new Set(data.map((d: DisasterData) => d.state))];
        setEligibleStates(states);
        
        // Clear state and county if not in eligible list
        if (!states.includes(formData.state)) {
          setFormData(prev => ({ ...prev, state: '', county: '' }));
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch disaster data');
        setDisasterData(null);
        setEligibleStates([]);
        setEligibleCounties([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDisasterData();
  }, [formData.drNumber]);

  // Update eligible counties when state changes
  useEffect(() => {
    if (!disasterData || !formData.state) {
      setEligibleCounties([]);
      return;
    }

    const counties = disasterData.designatedAreas
      .filter(area => area.countyName)
      .map(area => area.countyName);
    
    setEligibleCounties([...new Set(counties)]);
    
    // Clear county if not in eligible list
    if (!counties.includes(formData.county)) {
      setFormData(prev => ({ ...prev, county: '' }));
    }
  }, [formData.state, disasterData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleDateChange = (field: string) => (date: Date | null) => {
    setFormData({
      ...formData,
      [field]: date,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form data
    if (!disasterData) {
      setError('Please enter a valid disaster number');
      return;
    }
    
    try {
      // Save project data to backend
      const response = await fetch('/api/v1/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) throw new Error('Failed to create project');
      
      const project = await response.json();
      
      // Navigate to document upload with project ID
      navigate(`/compliance/${project.id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create project');
    }
  };

  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ p: 4, mt: 4, background: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(10px)' }}>
        <Box component="form" onSubmit={handleSubmit}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'white' }}>
            Project Details
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="DR Number"
                name="drNumber"
                value={formData.drNumber}
                onChange={handleChange}
                placeholder="e.g., DR-4999"
                disabled={loading}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: 'white',
                    '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.23)' },
                    '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.4)' },
                  },
                  '& .MuiInputLabel-root': { color: 'rgba(255, 255, 255, 0.7)' },
                }}
              />
              {loading && (
                <CircularProgress size={20} sx={{ ml: 1 }} />
              )}
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Project Name"
                name="projectName"
                value={formData.projectName}
                onChange={handleChange}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: 'white',
                    '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.23)' },
                    '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.4)' },
                  },
                  '& .MuiInputLabel-root': { color: 'rgba(255, 255, 255, 0.7)' },
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                select
                required
                fullWidth
                label="State"
                name="state"
                value={formData.state}
                onChange={handleChange}
                disabled={!disasterData || loading}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: 'white',
                    '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.23)' },
                    '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.4)' },
                  },
                  '& .MuiInputLabel-root': { color: 'rgba(255, 255, 255, 0.7)' },
                }}
              >
                {eligibleStates.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                select
                required
                fullWidth
                label="County"
                name="county"
                value={formData.county}
                onChange={handleChange}
                disabled={!formData.state || loading}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: 'white',
                    '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.23)' },
                    '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.4)' },
                  },
                  '& .MuiInputLabel-root': { color: 'rgba(255, 255, 255, 0.7)' },
                }}
              >
                {eligibleCounties.map((county) => (
                  <MenuItem key={county} value={county}>
                    {county}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Start Date"
                value={formData.startDate}
                onChange={handleDateChange('startDate')}
                disabled={!disasterData}
                minDate={disasterData?.incidentBeginDate ? new Date(disasterData.incidentBeginDate) : undefined}
                maxDate={disasterData?.incidentEndDate ? new Date(disasterData.incidentEndDate) : undefined}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="End Date"
                value={formData.endDate}
                onChange={handleDateChange('endDate')}
                disabled={!disasterData || !formData.startDate}
                minDate={formData.startDate || undefined}
                maxDate={disasterData?.incidentEndDate ? new Date(disasterData.incidentEndDate) : undefined}
              />
            </Grid>
          </Grid>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              type="submit"
              variant="contained"
              disabled={loading || !disasterData}
              sx={{
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
              }}
            >
              Continue to Document Upload
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default ProjectEntry;

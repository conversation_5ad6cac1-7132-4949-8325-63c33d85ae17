<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax - Intelligent FEMA Compliance Onboarding</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .starlink-gradient {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #1e40af 50%, #1e293b 75%, #0f172a 100%);
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .service-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .service-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .service-card.selected {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            transform: translateY(-2px);
        }
        .pulse-dot {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="starlink-gradient min-h-screen text-white">

    <!-- Unified Navigation -->
    <nav class="relative z-50 px-6 py-4 glass-effect bg-slate-900/30 border-b border-slate-700/50">
        <div class="mx-auto max-w-7xl flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="h-10 w-10 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">CM</span>
                </div>
                <span class="text-2xl font-bold text-white">ComplianceMax</span>
                <span class="px-3 py-1 text-xs font-semibold bg-blue-600/20 text-blue-300 rounded-full border border-blue-500/30">
                    Onboarding
                </span>
            </div>
            
            <div class="hidden md:flex items-center space-x-8">
                <button onclick="goHome()" class="text-slate-300 hover:text-white transition-colors font-medium">
                    🏠 Home
                </button>
                <span class="text-blue-300 font-medium">
                    🚀 Get Started
                </span>
                <button onclick="goToWizard()" class="text-slate-300 hover:text-white transition-colors font-medium">
                    🧭 Compliance Wizard
                </button>
                <div class="flex items-center space-x-2 text-sm text-slate-300">
                    <div class="w-2 h-2 bg-green-400 rounded-full pulse-dot"></div>
                    <span>18 Active Disasters</span>
                </div>
            </div>

            <!-- Mobile Menu Button -->
            <button class="md:hidden text-white" onclick="toggleMobileMenu()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobileMenu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700/50">
            <div class="flex flex-col space-y-3 pt-4">
                <button onclick="goHome()" class="text-slate-300 hover:text-white transition-colors text-left">🏠 Home</button>
                <span class="text-blue-300 font-medium">🚀 Get Started (Current)</span>
                <button onclick="goToWizard()" class="text-slate-300 hover:text-white transition-colors text-left">🧭 Compliance Wizard</button>
            </div>
        </div>
    </nav>

    <!-- Progress Header -->
    <div class="max-w-6xl mx-auto px-6 py-8">
        <div class="glass-card rounded-2xl p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-white">Intelligent Onboarding</h1>
                <span id="step-counter" class="text-blue-300 font-medium">Step 1 of 4</span>
            </div>
            
            <!-- Progress Bar -->
            <div class="w-full bg-slate-700 rounded-full h-3 mb-6">
                <div id="progress-bar" class="bg-gradient-to-r from-blue-400 to-cyan-400 h-3 rounded-full transition-all duration-500" style="width: 25%"></div>
            </div>

            <!-- Step Indicators -->
            <div id="step-indicators" class="grid grid-cols-4 gap-4">
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 text-white flex items-center justify-center font-bold">1</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-white">FEMA Registration</p>
                        <p class="text-xs text-slate-300">Auto-populate data</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold">2</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-slate-400">Geographic Selection</p>
                        <p class="text-xs text-slate-500">State → County → DR#</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold">3</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-slate-400">Workflow Analysis</p>
                        <p class="text-xs text-slate-500">PAPPG & policy detection</p>
                    </div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold">4</div>
                    <div class="mt-3 text-center">
                        <p class="text-sm font-semibold text-slate-400">Service Selection</p>
                        <p class="text-xs text-slate-500">Choose your tier</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Content Container -->
    <div class="max-w-6xl mx-auto px-6 pb-12">
        
        <!-- Step 1: FEMA Number Entry & Auto-Population -->
        <div id="step-1" class="step-content slide-in">
            <div class="max-w-2xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-bold text-white mb-4">Enter Your FEMA Registration Number</h2>
                        <p class="text-slate-300 text-lg">
                            Our intelligent system will auto-populate your profile and analyze eligible compliance workflows.
                        </p>
                    </div>

                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-200 mb-3">
                                FEMA Registration Number
                            </label>
                            <input
                                type="text"
                                id="fema-number"
                                placeholder="e.g., 123-4567-89"
                                maxlength="11"
                                pattern="[0-9]{3}-[0-9]{4}-[0-9]{2}"
                                class="w-full px-4 py-4 bg-white/10 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all"
                            />
                            <p class="mt-2 text-xs text-slate-400">
                                A FEMA applicant number is a nine-digit number (XXX-XXXX-XX) that you receive when you apply for FEMA disaster assistance. This number is used for reference when communicating with FEMA about your application.
                            </p>
                            <p id="fema-error" class="mt-2 text-sm text-red-400 hidden"></p>
                        </div>

                        <button
                            id="lookup-btn"
                            onclick="handleFEMALookup()"
                            class="w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-4 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-cyan-600 transition-all transform hover:scale-105 shadow-lg"
                        >
                            🔍 Auto-Populate Information
                        </button>

                        <div class="text-center text-sm text-slate-400">
                            <p>Don't have a FEMA registration? <a href="https://www.fema.gov/assistance/public" target="_blank" class="text-blue-400 hover:text-blue-300 underline">Register with FEMA first</a></p>
                        </div>
                    </div>

                    <!-- Auto-Populated Profile Info -->
                    <div id="profile-info" class="mt-8 p-6 bg-green-900/30 border border-green-600 rounded-lg hidden">
                        <h3 class="text-xl font-semibold text-green-300 mb-4">✅ Information Successfully Retrieved</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-6">
                            <div class="bg-white/5 p-3 rounded-lg">
                                <span class="font-medium text-slate-300">State:</span>
                                <div id="profile-state" class="text-white font-semibold text-lg"></div>
                            </div>
                            <div class="bg-white/5 p-3 rounded-lg">
                                <span class="font-medium text-slate-300">County:</span>
                                <div id="profile-county" class="text-white font-semibold text-lg"></div>
                            </div>
                            <div class="bg-white/5 p-3 rounded-lg">
                                <span class="font-medium text-slate-300">Type:</span>
                                <div id="profile-type" class="text-white font-semibold text-lg"></div>
                            </div>
                        </div>
                        <button
                            onclick="goToStep(2)"
                            class="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white py-3 px-6 rounded-lg font-semibold hover:from-green-600 hover:to-emerald-600 transition-all"
                        >
                            ➡️ Continue to Geographic Selection
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Geographic & Disaster Selection -->
        <div id="step-2" class="step-content hidden">
            <div class="max-w-4xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-bold text-white mb-4">Select Your Disaster Declaration</h2>
                        <p class="text-slate-300 text-lg">
                            Based on your location, here are currently eligible disasters with real-time FEMA data:
                        </p>
                    </div>

                    <div id="disasters-loading" class="text-center py-12">
                        <div class="loading-spinner w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                        <p class="text-slate-300">Loading live disaster data from FEMA APIs...</p>
                    </div>

                    <div id="disasters-list" class="space-y-4 hidden">
                        <!-- Disasters populated by JavaScript -->
                    </div>

                    <div class="mt-8 text-center">
                        <button
                            id="continue-disaster-btn"
                            onclick="goToStep(3)"
                            class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-3 px-8 rounded-lg font-semibold hover:from-blue-600 hover:to-cyan-600 transition-all shadow-lg hidden"
                        >
                            ➡️ Continue to Workflow Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Intelligent Workflow Analysis -->
        <div id="step-3" class="step-content hidden">
            <div class="max-w-4xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-bold text-white mb-4">Intelligent Compliance Analysis</h2>
                        <p class="text-slate-300 text-lg">
                            Our system is analyzing your project requirements and determining applicable policies...
                        </p>
                    </div>

                    <div id="workflow-analysis" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- PAPPG Version Analysis -->
                            <div class="bg-white/5 p-6 rounded-lg border border-slate-600">
                                <h3 class="text-xl font-semibold text-blue-300 mb-4">📋 PAPPG Version Analysis</h3>
                                <div id="pappg-analysis" class="space-y-2 text-sm text-slate-300">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                                        <span>Analyzing incident date...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Policy Framework Detection -->
                            <div class="bg-white/5 p-6 rounded-lg border border-slate-600">
                                <h3 class="text-xl font-semibold text-cyan-300 mb-4">⚖️ Policy Framework</h3>
                                <div id="policy-analysis" class="space-y-2 text-sm text-slate-300">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                                        <span>Checking DRRA applicability...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Compliance Requirements -->
                            <div class="bg-white/5 p-6 rounded-lg border border-slate-600">
                                <h3 class="text-xl font-semibold text-purple-300 mb-4">📝 Compliance Requirements</h3>
                                <div id="compliance-analysis" class="space-y-2 text-sm text-slate-300">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                                        <span>Evaluating documentation needs...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Geographic Risk Assessment -->
                            <div class="bg-white/5 p-6 rounded-lg border border-slate-600">
                                <h3 class="text-xl font-semibold text-green-300 mb-4">🗺️ Geographic Risk</h3>
                                <div id="geographic-analysis" class="space-y-2 text-sm text-slate-300">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3 pulse-dot"></div>
                                        <span>Assessing county-level risk factors...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="analysis-complete" class="text-center mt-8 hidden">
                            <div class="bg-green-900/30 border border-green-600 rounded-lg p-6 mb-6">
                                <h3 class="text-2xl font-semibold text-green-300 mb-4">✅ Analysis Complete!</h3>
                                <p class="text-slate-300">Your personalized compliance workflow has been generated based on sophisticated policy integration.</p>
                            </div>
                            <button
                                onclick="goToStep(4)"
                                class="bg-gradient-to-r from-green-500 to-emerald-500 text-white py-4 px-12 rounded-lg text-lg font-semibold hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg"
                            >
                                ➡️ Choose Your Service Level
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Service Selection & Subscription -->
        <div id="step-4" class="step-content hidden">
            <div class="max-w-6xl mx-auto">
                <div class="glass-card rounded-2xl p-8">
                    <div class="text-center mb-12">
                        <h2 class="text-4xl font-bold text-white mb-4">Choose Your Service Level</h2>
                        <p class="text-slate-300 text-lg mb-6">
                            Based on your workflow analysis, we recommend the following services:
                        </p>
                        <div id="recommendation-badge" class="inline-flex items-center px-4 py-2 bg-blue-900/50 border border-blue-500 rounded-full text-blue-300 text-sm font-medium">
                            💡 Recommendation based on your compliance requirements
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Basic Plan -->
                        <div class="service-card bg-white/5 border border-slate-600 rounded-2xl p-8 cursor-pointer" onclick="selectService('basic')">
                            <div class="text-center mb-6">
                                <h3 class="text-2xl font-bold text-white mb-2">Basic Compliance</h3>
                                <p class="text-slate-300 text-sm mb-4">Essential tools for straightforward projects</p>
                                <div class="text-4xl font-bold text-white mb-2">
                                    $199
                                    <span class="text-lg font-normal text-slate-400">/project</span>
                                </div>
                            </div>

                            <ul class="space-y-3 mb-8">
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Basic PAPPG compliance checklist</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Document templates</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Email support</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Standard reporting</span>
                                </li>
                            </ul>

                            <button class="w-full py-3 px-6 rounded-lg bg-slate-700 text-slate-300 hover:bg-slate-600 transition-colors">
                                Select Basic
                            </button>
                        </div>

                        <!-- Professional Plan (Recommended) -->
                        <div class="service-card bg-white/5 border-2 border-blue-500 rounded-2xl p-8 cursor-pointer relative" onclick="selectService('professional')">
                            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                <span class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                                    🏆 RECOMMENDED
                                </span>
                            </div>

                            <div class="text-center mb-6 mt-4">
                                <h3 class="text-2xl font-bold text-white mb-2">Professional</h3>
                                <p class="text-slate-300 text-sm mb-4">Advanced automation for complex projects</p>
                                <div class="text-4xl font-bold text-white mb-2">
                                    $499
                                    <span class="text-lg font-normal text-slate-400">/project</span>
                                </div>
                            </div>

                            <ul class="space-y-3 mb-8">
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Full PAPPG + DRRA compliance</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Intelligent workflow automation</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Real-time FEMA integration</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Advanced reporting & analytics</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Priority support</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Document OCR processing</span>
                                </li>
                            </ul>

                            <button class="w-full py-3 px-6 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold hover:from-blue-600 hover:to-cyan-600 transition-all">
                                Select Professional
                            </button>
                        </div>

                        <!-- Enterprise Plan -->
                        <div class="service-card bg-white/5 border border-slate-600 rounded-2xl p-8 cursor-pointer" onclick="selectService('enterprise')">
                            <div class="text-center mb-6">
                                <h3 class="text-2xl font-bold text-white mb-2">Enterprise</h3>
                                <p class="text-slate-300 text-sm mb-4">Complete solution for large organizations</p>
                                <div class="text-4xl font-bold text-white mb-2">
                                    $999
                                    <span class="text-lg font-normal text-slate-400">/project</span>
                                </div>
                            </div>

                            <ul class="space-y-3 mb-8">
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Everything in Professional</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Multi-project management</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Custom policy integration</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Dedicated account manager</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>Training & onboarding</span>
                                </li>
                                <li class="flex items-center text-sm text-slate-300">
                                    <span class="w-5 h-5 text-green-400 mr-3">✓</span>
                                    <span>API access & white-label</span>
                                </li>
                            </ul>

                            <button class="w-full py-3 px-6 rounded-lg bg-slate-700 text-slate-300 hover:bg-slate-600 transition-colors">
                                Select Enterprise
                            </button>
                        </div>
                    </div>

                    <div id="subscribe-section" class="mt-12 text-center hidden">
                        <div class="bg-blue-900/30 border border-blue-500 rounded-lg p-6 mb-6">
                            <h3 class="text-xl font-semibold text-blue-300 mb-2">Ready to Launch Your Compliance Dashboard?</h3>
                            <p class="text-slate-300">Your personalized workflow is configured and ready to go!</p>
                        </div>
                        <button
                            onclick="handleSubscription()"
                            class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-5 px-16 rounded-lg text-xl font-bold hover:from-blue-600 hover:to-cyan-600 transition-all shadow-xl transform hover:scale-105"
                        >
                            🚀 Subscribe & Launch Dashboard
                        </button>
                        <p class="mt-4 text-sm text-slate-400">
                            30-day money-back guarantee • Cancel anytime • Enterprise security
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let userProfile = {};
        let selectedDisaster = '';
        let selectedService = '';
        let workflowAnalysis = {};

        function updateStepIndicators(step) {
            const indicators = document.querySelectorAll('#step-indicators > div');
            indicators.forEach((indicator, index) => {
                const circle = indicator.querySelector('div');
                const title = indicator.querySelector('p:first-of-type');
                const subtitle = indicator.querySelector('p:last-of-type');
                
                if (index + 1 <= step) {
                    circle.className = 'w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 text-white flex items-center justify-center font-bold';
                    title.className = 'text-sm font-semibold text-white';
                    subtitle.className = 'text-xs text-slate-300';
                } else {
                    circle.className = 'w-10 h-10 rounded-full bg-slate-600 text-slate-400 flex items-center justify-center font-bold';
                    title.className = 'text-sm font-semibold text-slate-400';
                    subtitle.className = 'text-xs text-slate-500';
                }
            });

            document.getElementById('step-counter').textContent = `Step ${step} of 4`;
            document.getElementById('progress-bar').style.width = `${(step / 4) * 100}%`;
        }

        function goToStep(step) {
            // Hide all steps
            document.querySelectorAll('.step-content').forEach(el => {
                el.classList.add('hidden');
                el.classList.remove('slide-in');
            });
            
            // Show target step with animation
            const targetStep = document.getElementById(`step-${step}`);
            targetStep.classList.remove('hidden');
            setTimeout(() => targetStep.classList.add('slide-in'), 50);
            
            currentStep = step;
            updateStepIndicators(step);

            // Step-specific initialization
            if (step === 2) {
                loadDisasters();
            } else if (step === 3) {
                performWorkflowAnalysis();
            }
        }

        async function handleFEMALookup() {
            const femaNumber = document.getElementById('fema-number').value.trim();
            const errorEl = document.getElementById('fema-error');
            const lookupBtn = document.getElementById('lookup-btn');

            if (!femaNumber) {
                showError('Please enter your FEMA registration number');
                return;
            }

            // Validate format before proceeding
            const femaPattern = /^\d{3}-\d{4}-\d{2}$/;
            if (!femaPattern.test(femaNumber)) {
                showError('Invalid FEMA applicant number format. Please use XXX-XXXX-XX format (e.g., 123-4567-89)');
                return;
            }

            errorEl.classList.add('hidden');
            lookupBtn.innerHTML = '<div class="loading-spinner w-5 h-5 border-2 border-white border-t-transparent rounded-full inline-block mr-2"></div>Connecting to FEMA systems...';
            lookupBtn.disabled = true;

            try {
                // Try to connect to our backend first
                const response = await fetch('/api/fema/lookup-registration', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ femaNumber })
                });

                let profile;
                if (response.ok) {
                    const data = await response.json();
                    profile = data.profile;
                } else {
                    // Fallback to mock data based on pattern recognition
                    profile = generateMockProfile(femaNumber);
                }

                userProfile = { femaNumber, ...profile };
                displayProfile(profile);

            } catch (err) {
                // Fallback to intelligent pattern recognition
                console.log('Backend unavailable, using intelligent pattern recognition');
                const profile = generateMockProfile(femaNumber);
                userProfile = { femaNumber, ...profile };
                displayProfile(profile);
            } finally {
                lookupBtn.innerHTML = '🔍 Auto-Populate Information';
                lookupBtn.disabled = false;
            }
        }

        function generateMockProfile(femaNumber) {
            // Validate FEMA applicant number format (XXX-XXXX-XX)
            const femaPattern = /^\d{3}-\d{4}-\d{2}$/;
            
            if (!femaPattern.test(femaNumber)) {
                return { 
                    error: 'Invalid FEMA applicant number format. Please use XXX-XXXX-XX format.',
                    state: 'Invalid', 
                    county: 'Invalid', 
                    applicantType: 'Invalid' 
                };
            }

            // Generate mock profile based on number patterns
            const firstThree = parseInt(femaNumber.substring(0, 3));
            const applicantTypes = ['Local Government', 'State Government', 'Nonprofit Organization', 'Private Nonprofit'];
            
            // Use numeric patterns to simulate different states/counties
            if (firstThree >= 100 && firstThree <= 199) {
                return { state: 'Texas', county: 'Harris', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 200 && firstThree <= 299) {
                return { state: 'Florida', county: 'Miami-Dade', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 300 && firstThree <= 399) {
                return { state: 'Arkansas', county: 'Pulaski', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 400 && firstThree <= 499) {
                return { state: 'Nebraska', county: 'Douglas', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 500 && firstThree <= 599) {
                return { state: 'Kentucky', county: 'Jefferson', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 600 && firstThree <= 699) {
                return { state: 'Missouri', county: 'St. Louis', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 700 && firstThree <= 799) {
                return { state: 'Kansas', county: 'Johnson', applicantType: applicantTypes[firstThree % 4] };
            } else if (firstThree >= 800 && firstThree <= 899) {
                return { state: 'Mississippi', county: 'Hinds', applicantType: applicantTypes[firstThree % 4] };
            } else {
                return { state: 'Iowa', county: 'Polk', applicantType: applicantTypes[firstThree % 4] };
            }
        }

        function displayProfile(profile) {
            if (profile.error) {
                showError(profile.error);
                return;
            }
            
            document.getElementById('profile-state').textContent = profile.state;
            document.getElementById('profile-county').textContent = profile.county;
            document.getElementById('profile-type').textContent = profile.applicantType;
            document.getElementById('profile-info').classList.remove('hidden');
        }

        function showError(message) {
            const errorEl = document.getElementById('fema-error');
            errorEl.textContent = message;
            errorEl.classList.remove('hidden');
        }

        async function loadDisasters() {
            const loadingEl = document.getElementById('disasters-loading');
            const listEl = document.getElementById('disasters-list');
            
            loadingEl.classList.remove('hidden');
            listEl.classList.add('hidden');

            try {
                // Try to get real disasters from our backend
                const response = await fetch(`http://localhost:5000/api/fema/disasters/enhanced?format=json`);
                let disasters;

                if (response.ok) {
                    const data = await response.json();
                    disasters = data.data || [];
                } else {
                    // Fallback to mock data
                    disasters = getMockDisasters();
                }

                displayDisasters(disasters);

            } catch (err) {
                console.log('Using mock disaster data');
                displayDisasters(getMockDisasters());
            } finally {
                loadingEl.classList.add('hidden');
                listEl.classList.remove('hidden');
            }
        }

        function getMockDisasters() {
            return [
                {
                    drNumber: 'DR-4873-AR',
                    title: 'Arkansas Severe Storms and Flooding',
                    declarationDate: '2024-06-15T00:00:00Z',
                    status: 'Active',
                    incidentType: 'Severe Weather',
                    counties: ['Clark', 'Clay', 'Craighead', 'Cross', 'Dallas'],
                    eligiblePrograms: ['Public Assistance', 'Individual Assistance']
                },
                {
                    drNumber: 'DR-4868-NE',
                    title: 'Nebraska Severe Weather',
                    declarationDate: '2024-05-20T00:00:00Z',
                    status: 'Active',
                    incidentType: 'Severe Storm',
                    counties: ['Boone', 'Douglas', 'Lancaster', 'Sarpy'],
                    eligiblePrograms: ['Public Assistance']
                },
                {
                    drNumber: 'DR-4875-KY',
                    title: 'Kentucky Storms and Flooding',
                    declarationDate: '2024-07-01T00:00:00Z',
                    status: 'Active',
                    incidentType: 'Severe Weather',
                    counties: ['Caldwell', 'Laurel', 'Pulaski', 'Russell'],
                    eligiblePrograms: ['Public Assistance', 'Hazard Mitigation']
                }
            ];
        }

        function displayDisasters(disasters) {
            const listEl = document.getElementById('disasters-list');
            
            listEl.innerHTML = disasters.map(disaster => `
                <div class="disaster-card bg-white/5 border border-slate-600 rounded-lg p-6 cursor-pointer transition-all hover:border-blue-400 hover:bg-white/10" 
                     onclick="selectDisaster('${disaster.drNumber}')">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-semibold text-white mb-1">${disaster.drNumber}</h3>
                            <p class="text-slate-300 mb-2">${disaster.title}</p>
                            <div class="flex items-center space-x-4 text-sm text-slate-400">
                                <span>📅 ${new Date(disaster.declarationDate).toLocaleDateString()}</span>
                                <span>📍 ${disaster.counties ? disaster.counties.length + ' counties' : 'Multiple counties'}</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-900/50 text-green-300 border border-green-600">
                                ${disaster.status}
                            </span>
                            <p class="text-sm text-slate-400 mt-2">
                                ${disaster.eligiblePrograms ? disaster.eligiblePrograms.join(', ') : 'Public Assistance'}
                            </p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function selectDisaster(drNumber) {
            selectedDisaster = drNumber;
            
            // Update visual selection
            document.querySelectorAll('.disaster-card').forEach(card => {
                card.classList.remove('border-blue-400', 'bg-blue-900/20');
                card.classList.add('border-slate-600');
            });
            
            event.currentTarget.classList.remove('border-slate-600');
            event.currentTarget.classList.add('border-blue-400', 'bg-blue-900/20');
            
            document.getElementById('continue-disaster-btn').classList.remove('hidden');
        }

        async function performWorkflowAnalysis() {
            const stages = [
                { id: 'pappg-analysis', text: 'PAPPG v5.0 determined (incident after 2025)', delay: 1000 },
                { id: 'policy-analysis', text: 'DRRA Sections 1206, 1235b applicable', delay: 1500 },
                { id: 'compliance-analysis', text: '2 CFR 200 procurement requirements', delay: 2000 },
                { id: 'geographic-analysis', text: 'HIGH risk - multi-county disaster', delay: 2500 }
            ];

            for (const stage of stages) {
                await new Promise(resolve => setTimeout(resolve, stage.delay));
                
                const element = document.getElementById(stage.id);
                element.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                        <span class="text-green-300">✓ ${stage.text}</span>
                    </div>
                `;
            }

            // Show completion
            setTimeout(() => {
                document.getElementById('analysis-complete').classList.remove('hidden');
            }, 3000);
        }

        function selectService(serviceId) {
            selectedService = serviceId;
            
            // Reset all service cards
            document.querySelectorAll('.service-card').forEach(card => {
                card.classList.remove('selected');
                const button = card.querySelector('button');
                
                if (serviceId === 'basic') {
                    button.textContent = 'Select Basic';
                    button.className = 'w-full py-3 px-6 rounded-lg bg-slate-700 text-slate-300 hover:bg-slate-600 transition-colors';
                } else if (serviceId === 'professional') {
                    button.textContent = 'Select Professional';
                    button.className = 'w-full py-3 px-6 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold hover:from-blue-600 hover:to-cyan-600 transition-all';
                } else {
                    button.textContent = 'Select Enterprise';
                    button.className = 'w-full py-3 px-6 rounded-lg bg-slate-700 text-slate-300 hover:bg-slate-600 transition-colors';
                }
            });
            
            // Highlight selected service
            event.currentTarget.classList.add('selected');
            const selectedButton = event.currentTarget.querySelector('button');
            selectedButton.textContent = `✓ ${serviceId.charAt(0).toUpperCase() + serviceId.slice(1)} Selected`;
            selectedButton.className = 'w-full py-3 px-6 rounded-lg bg-green-600 text-white font-semibold transition-colors';
            
            document.getElementById('subscribe-section').classList.remove('hidden');
        }

        function handleSubscription() {
            const summary = `
🎉 SUBSCRIPTION SUCCESSFUL!

👤 Profile: ${userProfile.applicantType} in ${userProfile.county}, ${userProfile.state}
🌪️ Disaster: ${selectedDisaster}
📋 Service: ${selectedService.charAt(0).toUpperCase() + selectedService.slice(1)} plan
⚖️ Compliance: PAPPG v5.0, DRRA Sections 1206/1235b, 2 CFR 200

🚀 Launching your personalized compliance dashboard...
            `;
            
            alert(summary);
            
            // Send completion message to parent window (if in iframe)
            if (window.parent !== window) {
                window.parent.postMessage({ action: 'onboarding-complete' }, '*');
            } else {
                // If not in iframe, redirect directly to wizard
                window.location.href = 'components/EnhancedComplianceWizard.html';
            }
        }

        // Initialize the page
        updateStepIndicators(1);

        // Auto-format FEMA number input
        document.getElementById('fema-number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 9) value = value.slice(0, 9); // Limit to 9 digits
            
            // Format as XXX-XXXX-XX
            if (value.length >= 8) {
                value = value.slice(0, 3) + '-' + value.slice(3, 7) + '-' + value.slice(7);
            } else if (value.length >= 4) {
                value = value.slice(0, 3) + '-' + value.slice(3);
            }
            
            e.target.value = value;
        });

        // Navigation Functions
        function goHome() {
            // If we're in an iframe, send message to parent
            if (window.parent !== window) {
                window.parent.postMessage({ action: 'navigate', page: 'home' }, '*');
            } else {
                window.location.href = 'index.html';
            }
        }

        function goToWizard() {
            // If we're in an iframe, send message to parent
            if (window.parent !== window) {
                window.parent.postMessage({ action: 'navigate', page: 'wizard' }, '*');
            } else {
                window.location.href = 'components/EnhancedComplianceWizard.html';
            }
        }

        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }
    </script>
</body>
</html> 
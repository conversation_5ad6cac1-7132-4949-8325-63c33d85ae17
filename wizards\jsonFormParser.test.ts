import { parseJsonSchemaToFormSteps } from './jsonFormParser';

describe('parseJsonSchemaToFormSteps', () => {
  it('should return empty array and not throw for invalid entries', () => {
    const invalidData: any = [
      [null, undefined, { foo: 'bar' }, {}],
      [],
    ];
    expect(() => parseJsonSchemaToFormSteps(invalidData)).not.toThrow();
    const steps = parseJsonSchemaToFormSteps(invalidData);
    expect(Array.isArray(steps)).toBe(true);
    expect(steps.length).toBe(0);
  });

  it('should parse valid entries and skip invalid ones', () => {
    const validEntry: any = {
      process_phase: 'Phase 1',
      'step/requirement': 'Requirement A',
      trigger_condition_if: null,
      action_required_then: '',
      documentation_required: '',
      responsible_party: '',
      applicable_regulations: '',
      notes: '',
      phase_tags: [],
    };
    const invalidEntry = { foo: 'bar' };
    const steps = parseJsonSchemaToFormSteps([[validEntry, invalidEntry]] as any);
    expect(steps.length).toBe(1);
    expect(steps[0].title).toBe('Phase 1');
    expect(steps[0].fields.some(f => f.label === 'Requirement A')).toBe(true);
  });
}); 
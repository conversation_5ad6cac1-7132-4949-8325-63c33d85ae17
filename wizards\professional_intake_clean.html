<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Compliance Intake - ComplianceMax V74</title>
    <style>
        /* SCOPED STYLES - NO DASHBOARD BLEED */
        .intake-container {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .intake-header {
            background: #1e293b;
            color: white;
            padding: 2rem 0;
            border-bottom: 3px solid #3b82f6;
        }
        
        .intake-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
            text-align: center;
        }
        
        .intake-header p {
            margin: 0.5rem 0 0 0;
            text-align: center;
            color: #94a3b8;
            font-size: 1.1rem;
        }
        
        .intake-form {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .form-section {
            padding: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .form-section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.75rem;
            margin-top: 0.5rem;
        }
        
        .checkbox-item {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            transition: all 0.2s;
        }
        
        .checkbox-item:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-top: 0.25rem;
        }
        
        .checkbox-label {
            flex: 1;
        }
        
        .checkbox-title {
            display: block;
            font-weight: 500;
            color: #1e293b;
            font-size: 0.9rem;
        }
        
        .checkbox-desc {
            display: block;
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-full {
            width: 100%;
        }
        
        .form-actions {
            padding: 2rem;
            background: #f8fafc;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        
        .help-text {
            font-size: 0.85rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .required {
            color: #ef4444;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        @media (max-width: 768px) {
            .intake-form {
                margin: 1rem;
                border-radius: 8px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body class="intake-container">
    <!-- Clean Header - No Dashboard Bleed -->
    <header class="intake-header">
        <h1>Professional Compliance Analysis</h1>
        <p>Comprehensive FEMA Public Assistance Project Evaluation</p>
    </header>

    <!-- Focused Intake Form -->
    <form class="intake-form" id="professionalIntakeForm">
        <!-- Project Information -->
        <section class="form-section">
            <h2 class="section-title">
                📋 Project Information
            </h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Project Name <span class="required">*</span></label>
                    <input type="text" class="form-input" name="projectName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">FEMA Category <span class="required">*</span></label>
                    <select class="form-select" name="femaCategory" required>
                        <option value="">Select Category</option>
                        <option value="C">Category C - Roads and Bridges</option>
                        <option value="D">Category D - Water Control Facilities</option>
                        <option value="E">Category E - Buildings and Equipment</option>
                        <option value="F">Category F - Public Utilities</option>
                        <option value="G">Category G - Parks and Recreation</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Estimated Cost <span class="required">*</span></label>
                    <input type="number" class="form-input" name="estimatedCost" placeholder="$0.00" required>
                    <div class="help-text">Enter total project cost estimate</div>
                </div>
                <div class="form-group">
                    <label class="form-label">Disaster Declaration Date</label>
                    <input type="date" class="form-input" name="disasterDate">
                    <div class="help-text">Determines applicable PAPPG version</div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Project Description <span class="required">*</span></label>
                <textarea class="form-textarea" name="projectDescription" placeholder="Describe the scope of work, damage, and proposed repairs..." required></textarea>
            </div>
        </section>

        <!-- Facility Information -->
        <section class="form-section">
            <h2 class="section-title">
                🏢 Facility Information
            </h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Facility Type <span class="required">*</span></label>
                    <select class="form-select" name="facilityType" required>
                        <option value="">Select Facility Type</option>
                        <option value="building">Building/Structure</option>
                        <option value="bridge">Bridge</option>
                        <option value="road">Road/Highway</option>
                        <option value="water">Water Treatment Facility</option>
                        <option value="wastewater">Wastewater Treatment</option>
                        <option value="electric">Electric Power System</option>
                        <option value="park">Park/Recreation Facility</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Year Built</label>
                    <input type="number" class="form-input" name="yearBuilt" placeholder="YYYY">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Facility Address</label>
                <input type="text" class="form-input" name="facilityAddress" placeholder="Street address, City, State, ZIP">
            </div>
        </section>

        <!-- Compliance Requirements -->
        <section class="form-section">
            <h2 class="section-title">
                ⚖️ Compliance Requirements
            </h2>
            
            <div class="form-group">
                <label class="form-label">Applicable Building Codes <span class="required">*</span></label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="ibc" name="buildingCodes" value="IBC">
                        <label class="checkbox-label" for="ibc">
                            <span class="checkbox-title">International Building Code (IBC)</span>
                            <span class="checkbox-desc">Current edition building standards</span>
                        </label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="asce7" name="buildingCodes" value="ASCE7">
                        <label class="checkbox-label" for="asce7">
                            <span class="checkbox-title">ASCE 7 - Minimum Design Loads</span>
                            <span class="checkbox-desc">Wind, seismic, and other load requirements</span>
                        </label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="aashto" name="buildingCodes" value="AASHTO">
                        <label class="checkbox-label" for="aashto">
                            <span class="checkbox-title">AASHTO Bridge Design</span>
                            <span class="checkbox-desc">For bridge and highway projects</span>
                        </label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="nfpa" name="buildingCodes" value="NFPA">
                        <label class="checkbox-label" for="nfpa">
                            <span class="checkbox-title">NFPA Fire Protection</span>
                            <span class="checkbox-desc">Fire safety and protection standards</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Environmental Considerations</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="floodplain" name="environmental" value="floodplain">
                        <label class="checkbox-label" for="floodplain">
                            <span class="checkbox-title">Located in Floodplain</span>
                            <span class="checkbox-desc">Requires flood-resistant design</span>
                        </label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="historic" name="environmental" value="historic">
                        <label class="checkbox-label" for="historic">
                            <span class="checkbox-title">Historic Property</span>
                            <span class="checkbox-desc">Section 106 review required</span>
                        </label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="wetlands" name="environmental" value="wetlands">
                        <label class="checkbox-label" for="wetlands">
                            <span class="checkbox-title">Near Wetlands</span>
                            <span class="checkbox-desc">Environmental impact assessment</span>
                        </label>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Information -->
        <section class="form-section">
            <h2 class="section-title">
                📞 Contact Information
            </h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Primary Contact <span class="required">*</span></label>
                    <input type="text" class="form-input" name="contactName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Title/Position</label>
                    <input type="text" class="form-input" name="contactTitle">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Email <span class="required">*</span></label>
                    <input type="email" class="form-input" name="contactEmail" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Phone</label>
                    <input type="tel" class="form-input" name="contactPhone">
                </div>
            </div>
        </section>

        <!-- Form Actions -->
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                Cancel
            </button>
            <button type="submit" class="btn btn-primary">
                Submit for Analysis
            </button>
        </div>
    </form>

    <script>
        document.getElementById('professionalIntakeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            alert('Professional intake submitted successfully! You will receive a detailed analysis within 2-3 business days.');
            
            // In real implementation, this would submit to the API
            console.log('Form submitted:', new FormData(this));
        });
        
        // Auto-populate codes based on facility type
        document.querySelector('select[name="facilityType"]').addEventListener('change', function() {
            const facilityType = this.value;
            const checkboxes = document.querySelectorAll('input[name="buildingCodes"]');
            
            // Reset all checkboxes
            checkboxes.forEach(cb => cb.checked = false);
            
            // Auto-select relevant codes
            if (facilityType === 'building') {
                document.getElementById('ibc').checked = true;
                document.getElementById('asce7').checked = true;
                document.getElementById('nfpa').checked = true;
            } else if (facilityType === 'bridge' || facilityType === 'road') {
                document.getElementById('aashto').checked = true;
                document.getElementById('asce7').checked = true;
            }
        });
    </script>
</body>
</html> 
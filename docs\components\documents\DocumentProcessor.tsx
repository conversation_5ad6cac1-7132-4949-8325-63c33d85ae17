import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  CircularProgress,
  Paper,
  Typography,
  Alert,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import { DocumentService } from '../../services/document';
import { websocketService, DocumentUpdate } from '../../services/websocket';
import { SUPPORTED_FILE_TYPES, MAX_FILE_SIZE } from '../../config';

interface DocumentProcessorProps {
  drNumber: string;
  onProcessingComplete?: (documentId: string) => void;
}

const DocumentProcessor: React.FC<DocumentProcessorProps> = ({
  drNumber,
  onProcessingComplete
}) => {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<{
    stage: string;
    message: string;
  } | null>(null);
  const [documentId, setDocumentId] = useState<string | null>(null);

  const documentService = new DocumentService();

  useEffect(() => {
    if (documentId) {
      // Connect to WebSocket for real-time updates
      websocketService.connect(documentId);

      const unsubscribe = websocketService.subscribeToDocumentUpdates(
        (update: DocumentUpdate) => {
          switch (update.action) {
            case 'analysis_complete':
              setProgress({
                stage: 'Analysis Complete',
                message: 'Document analysis completed successfully'
              });
              break;
            case 'compliance_complete':
              setProgress({
                stage: 'Compliance Check Complete',
                message: 'Compliance validation completed successfully'
              });
              setProcessing(false);
              if (onProcessingComplete) {
                onProcessingComplete(documentId);
              }
              break;
            case 'error':
              setError(update.details?.message || 'An error occurred during processing');
              setProcessing(false);
              break;
          }
        }
      );

      return () => {
        unsubscribe();
        websocketService.disconnect();
      };
    }
  }, [documentId, onProcessingComplete]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    
    // Validate file type and size
    if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
      setError('Unsupported file type. Please upload a PDF, Word document, or text file.');
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      setError(`File size exceeds the maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
      return;
    }

    setError(null);
    setProcessing(true);
    setProgress({
      stage: 'Upload',
      message: 'Uploading document...'
    });

    try {
      // Upload document
      const docId = await documentService.uploadDocument(file, drNumber);
      setDocumentId(docId);
      
      setProgress({
        stage: 'Analysis',
        message: 'Analyzing document content...'
      });

      // Start analysis
      await documentService.analyzeDocument(docId);
      
      // WebSocket will handle the rest of the updates
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while processing the document');
      setProcessing(false);
    }
  }, [drNumber, documentService]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: SUPPORTED_FILE_TYPES.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxSize: MAX_FILE_SIZE,
    multiple: false
  });

  return (
    <Box sx={{ p: 2 }}>
      {/* Upload Area */}
      <Paper
        {...getRootProps()}
        sx={{
          p: 3,
          textAlign: 'center',
          cursor: 'pointer',
          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'divider'
        }}
      >
        <input {...getInputProps()} />
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'Drop the document here' : 'Drag & drop a document here'}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Supported formats: PDF, Word, Text files
          <br />
          Maximum size: {MAX_FILE_SIZE / (1024 * 1024)}MB
        </Typography>
      </Paper>

      {/* Progress and Status */}
      {processing && progress && (
        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item>
                <CircularProgress size={24} />
              </Grid>
              <Grid item xs>
                <Typography variant="h6">{progress.stage}</Typography>
                <Typography variant="body2" color="textSecondary">
                  {progress.message}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};

export default DocumentProcessor;

import React, { useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Box,
  Grid,
  Paper
} from '@mui/material';
import {
  History as HistoryIcon,
  Description as DocumentIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useHistory } from '../../hooks/useHistory';

interface AuditTrailProps {
  reviewId: number;
  includeDocuments?: boolean;
}

export const AuditTrail: React.FC<AuditTrailProps> = ({
  reviewId,
  includeDocuments = true
}) => {
  const { auditTrail, loading, error, fetchAuditTrail } = useHistory();

  useEffect(() => {
    fetchAuditTrail(reviewId, includeDocuments);
  }, [reviewId, includeDocuments]);

  if (loading) {
    return <Typography>Loading audit trail...</Typography>;
  }

  if (error) {
    return <Typography color="error">Error loading audit trail: {error.message}</Typography>;
  }

  if (!auditTrail) {
    return <Typography>No audit trail available</Typography>;
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Audit Trail
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Review Information
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Created"
                    secondary={auditTrail.creation_date ? new Date(auditTrail.creation_date).toLocaleString() : 'N/A'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Last Modified"
                    secondary={auditTrail.last_modified ? new Date(auditTrail.last_modified).toLocaleString() : 'N/A'}
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {includeDocuments && auditTrail.document_changes && auditTrail.document_changes.length > 0 && (
            <Grid item xs={12} md={6}>
              <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Document Changes
                </Typography>
                <List dense>
                  {auditTrail.document_changes.map((doc, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <DocumentIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={doc.document_type}
                        secondary={`${doc.action} by User ${doc.user_id} on ${new Date(doc.timestamp).toLocaleString()}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>
          )}
        </Grid>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle1" gutterBottom>
          Change History
        </Typography>

        <List>
          {auditTrail.changes.map((change, index) => (
            <Paper key={index} elevation={2} sx={{ mb: 2 }}>
              <ListItem>
                <ListItemIcon>
                  <HistoryIcon />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="subtitle2">
                        {change.details}
                      </Typography>
                      <Chip
                        label={change.type}
                        size="small"
                        color={
                          change.type === 'create' ? 'success' :
                          change.type === 'update' ? 'info' :
                          change.type === 'delete' ? 'error' :
                          'default'
                        }
                      />
                    </Box>
                  }
                  secondary={
                    <>
                      <Typography variant="caption" display="block">
                        {new Date(change.timestamp).toLocaleString()} by User {change.user_id}
                      </Typography>
                      {change.comment && (
                        <Typography variant="body2" color="textSecondary">
                          Comment: {change.comment}
                        </Typography>
                      )}
                      {change.changes && Object.keys(change.changes).length > 0 && (
                        <Box mt={1}>
                          <Typography variant="caption" color="textSecondary">
                            Changes:
                          </Typography>
                          <List dense>
                            {Object.entries(change.changes).map(([field, values]) => (
                              <ListItem key={field}>
                                <ListItemIcon>
                                  <EditIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={field}
                                  secondary={`${values.from} → ${values.to}`}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                      {change.findings_diff && (
                        <Box mt={1}>
                          <Typography variant="caption" color="textSecondary">
                            Findings Changes:
                          </Typography>
                          <List dense>
                            {change.findings_diff.added.length > 0 && (
                              <ListItem>
                                <ListItemIcon>
                                  <AddIcon fontSize="small" color="success" />
                                </ListItemIcon>
                                <ListItemText
                                  primary="Added"
                                  secondary={change.findings_diff.added.join(', ')}
                                />
                              </ListItem>
                            )}
                            {change.findings_diff.removed.length > 0 && (
                              <ListItem>
                                <ListItemIcon>
                                  <RemoveIcon fontSize="small" color="error" />
                                </ListItemIcon>
                                <ListItemText
                                  primary="Removed"
                                  secondary={change.findings_diff.removed.join(', ')}
                                />
                              </ListItem>
                            )}
                            {change.findings_diff.modified.length > 0 && (
                              <ListItem>
                                <ListItemIcon>
                                  <EditIcon fontSize="small" color="info" />
                                </ListItemIcon>
                                <ListItemText
                                  primary="Modified"
                                  secondary={change.findings_diff.modified.join(', ')}
                                />
                              </ListItem>
                            )}
                          </List>
                        </Box>
                      )}
                    </>
                  }
                />
              </ListItem>
            </Paper>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

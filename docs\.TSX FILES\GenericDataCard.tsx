
'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  Info, 
  AlertCircle, 
  XCircle,
  Clock,
  ExternalLink
} from 'lucide-react';

// Types for generic data display
export interface DataPoint {
  id: string;
  source: string;
  category: string;
  timestamp: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, any>;
  raw_data?: Record<string, any>;
}

export interface GenericDataCardProps {
  dataPoint: DataPoint;
  className?: string;
  showMetadata?: boolean;
  onViewDetails?: (dataPoint: DataPoint) => void;
  customIcon?: React.ComponentType<any>;
  customActions?: React.ReactNode;
}

// Severity configuration
const severityConfig = {
  low: {
    icon: Info,
    color: 'text-blue-500',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    badgeVariant: 'secondary' as const
  },
  medium: {
    icon: AlertCircle,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    badgeVariant: 'outline' as const
  },
  high: {
    icon: AlertTriangle,
    color: 'text-orange-500',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    badgeVariant: 'destructive' as const
  },
  critical: {
    icon: XCircle,
    color: 'text-red-500',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    badgeVariant: 'destructive' as const
  }
};

// Category configuration
const categoryConfig = {
  environmental: {
    label: 'Environmental',
    color: 'text-green-600'
  },
  regulatory: {
    label: 'Regulatory',
    color: 'text-blue-600'
  },
  security: {
    label: 'Security',
    color: 'text-red-600'
  },
  financial: {
    label: 'Financial',
    color: 'text-purple-600'
  },
  operational: {
    label: 'Operational',
    color: 'text-gray-600'
  },
  news: {
    label: 'News',
    color: 'text-indigo-600'
  },
  disaster: {
    label: 'Disaster',
    color: 'text-red-700'
  }
};

export const GenericDataCard: React.FC<GenericDataCardProps> = ({
  dataPoint,
  className = '',
  showMetadata = false,
  onViewDetails,
  customIcon,
  customActions
}) => {
  const severityInfo = severityConfig[dataPoint.severity];
  const categoryInfo = categoryConfig[dataPoint.category as keyof typeof categoryConfig] || {
    label: dataPoint.category,
    color: 'text-gray-600'
  };
  
  const IconComponent = customIcon || severityInfo.icon;
  const timestamp = new Date(dataPoint.timestamp);
  const timeAgo = getTimeAgo(timestamp);

  return (
    <Card className={`${severityInfo.bgColor} ${severityInfo.borderColor} border-l-4 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <IconComponent className={`h-5 w-5 ${severityInfo.color}`} />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm">
                {dataPoint.title}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={severityInfo.badgeVariant} className="text-xs">
                  {dataPoint.severity.toUpperCase()}
                </Badge>
                <span className={`text-xs font-medium ${categoryInfo.color}`}>
                  {categoryInfo.label}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {customActions}
            {onViewDetails && (
              <button
                onClick={() => onViewDetails(dataPoint)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="View details"
              >
                <ExternalLink className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-gray-700 text-sm mb-3">
          {dataPoint.description}
        </p>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <Clock className="h-3 w-3" />
            <span>{timeAgo}</span>
          </div>
          <span className="font-medium">{dataPoint.source}</span>
        </div>
        
        {showMetadata && Object.keys(dataPoint.metadata).length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <h4 className="text-xs font-semibold text-gray-600 mb-2">Metadata</h4>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(dataPoint.metadata).map(([key, value]) => (
                <div key={key} className="text-xs">
                  <span className="font-medium text-gray-600">{key}:</span>
                  <span className="ml-1 text-gray-800">
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Utility function to calculate time ago
function getTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return `${diffInSeconds}s ago`;
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}m ago`;
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}h ago`;
  } else {
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  }
}

export default GenericDataCard;

# ComplianceMax Application

## Overview

ComplianceMax is a comprehensive compliance management solution designed to streamline your organization's compliance processes. The application includes policy matching, document management, reporting, data visualization, and integration with external systems.

## Features

- **Policy Matcher**: Automatically match requirements documents against your organization's policies
- **Document Management**: Upload, categorize, and manage compliance-related documents
- **Reports**: Generate and manage compliance reports
- **Data Visualization**: Interactive charts and graphs for compliance metrics
- **Export Functionality**: Export reports in multiple formats (PDF, Excel, CSV)
- **Notification System**: Stay informed about important events and configure notification preferences
- **Enhanced Dashboard**: Detailed trend analysis and compliance monitoring
- **External Systems Integration**: Connect with FEMA database, document repositories, and calendars

## Repository Structure

```
compliancemax/
├── frontend/                  # React frontend application
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/             # Page components
│   │   ├── services/          # API service integrations
│   │   ├── theme.ts           # Material UI theme configuration
│   │   └── App.tsx            # Main application component
│   ├── public/                # Static assets
│   └── package.json           # Frontend dependencies
├── backend/                   # FastAPI backend application
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   ├── core/              # Core functionality
│   │   ├── db/                # Database models and migrations
│   │   ├── services/          # Business logic services
│   │   └── main.py            # Application entry point
│   ├── requirements.txt       # Backend dependencies
│   └── alembic/               # Database migrations
├── policy_matcher/            # Policy matcher module
│   ├── document_extractor.py  # Document text extraction
│   ├── requirement_parser.py  # Requirement parsing
│   ├── policy_matcher.py      # Policy matching algorithm
│   ├── report_generator.py    # Report generation
│   └── main.py                # Module entry point
├── tests/                     # Test suite
│   ├── frontend/              # Frontend tests
│   ├── backend/               # Backend tests
│   └── end_to_end/            # End-to-end tests
├── docs/                      # Documentation
│   ├── USER_GUIDE.md          # User guide
│   └── DEPLOYMENT_GUIDE.md    # Deployment guide
├── docker-compose.yml         # Docker Compose configuration
└── README.md                  # Project overview
```

## Getting Started

### Prerequisites

- Node.js 16.x or higher
- Python 3.10 or higher
- PostgreSQL 14.x or higher
- Git

### Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/compliancemax/compliancemax.git
   cd compliancemax
   ```

2. Set up the frontend:
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. Set up the backend:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   uvicorn app.main:app --reload
   ```

4. Set up the database:
   ```bash
   # Create database and user in PostgreSQL
   # Run migrations
   cd backend
   alembic upgrade head
   ```

### Docker Setup

Alternatively, you can use Docker Compose to set up the entire application:

```bash
docker-compose up -d
```

## Documentation

- [User Guide](docs/USER_GUIDE.md): Detailed instructions for using the application
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md): Instructions for deploying the application in production

## Testing

Run the test suite to ensure everything is working correctly:

```bash
# Frontend tests
cd frontend
npm test

# Backend tests
cd backend
pytest

# End-to-end tests
cd tests/end_to_end
pytest
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, <NAME_EMAIL> or visit our website at https://www.compliancemax.com.

import { render } from '@testing-library/react';
import { measureMemory, measureRender } from 'react-performance-testing';
import ProfessionalIntakeSuperWizard from './ProfessionalIntakeSuperWizard';

describe('Performance Tests', () => {
  it('should render under 500ms for large checklists', async () => {
    const { duration } = await measureRender(
      <ProfessionalIntakeSuperWizard />
    );
    expect(duration).toBeLessThan(500);
  });

  it('should use <100MB memory', async () => {
    const { usedJSHeapSize } = await measureMemory(
      <ProfessionalIntakeSuperWizard />
    );
    expect(usedJSHeapSize).toBeLessThan(100 * 1024 * 1024);
  });
});

describe('Snapshot Tests', () => {
  it('matches wizard snapshot', () => {
    const { asFragment } = render(<ProfessionalIntakeSuperWizard />);
    expect(asFragment()).toMatchSnapshot();
  });
}); 
import React from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Avatar, Button, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useAuth } from '../hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { ChangePassword } from './ChangePassword';

const GlassContainer = styled(Paper)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(10px)',
  borderRadius: '16px',
  padding: theme.spacing(3),
  border: '1px solid rgba(255, 255, 255, 0.1)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
  },
}));

const StyledAvatar = styled(Avatar)(({ theme }) => ({
  width: theme.spacing(12),
  height: theme.spacing(12),
  marginBottom: theme.spacing(2),
  border: '4px solid rgba(255, 255, 255, 0.1)',
  background: 'linear-gradient(135deg, #6B8DD6 0%, #8E37D7 100%)',
}));

const TabContainer = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(3),
}));

const TabButtons = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

const TabButton = styled(Button)(({ theme, active }) => ({
  padding: theme.spacing(1, 2),
  border: 'none',
  background: active ? '#0066cc' : 'transparent',
  color: active ? 'white' : '#333',
  borderRadius: theme.spacing(1),
  cursor: 'pointer',
  transition: 'all 0.2s',
  '&:hover': {
    background: active ? '#0052a3' : '#f0f0f0',
  },
}));

const TabContent = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 0),
}));

const LogoutButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(2),
  padding: theme.spacing(1, 2),
  background: '#dc3545',
  color: 'white',
  border: 'none',
  borderRadius: theme.spacing(1),
  cursor: 'pointer',
  transition: 'background 0.2s',
  '&:hover': {
    background: '#c82333',
  },
}));

const ProfileSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(1.5),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  padding: theme.spacing(0.5),
  borderRadius: theme.spacing(1),
  transition: 'background-color 0.2s',
  '&:hover': {
    background: '#f8f9fa',
  },
}));

const Label = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  color: '#34495e',
  minWidth: theme.spacing(12),
}));

const Value = styled(Typography)(({ theme }) => ({
  color: '#2c3e50',
}));

const StatusBadge = styled(Typography)(({ theme, active }) => ({
  background: active ? '#27ae60' : '#e74c3c',
  color: 'white',
  padding: theme.spacing(0.25, 0.75),
  borderRadius: theme.spacing(1),
  fontSize: theme.spacing(0.875),
}));

const DateValue = styled(Value)(({ theme }) => ({
  color: '#7f8c8d',
  fontSize: theme.spacing(0.875),
}));

export function UserProfile() {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = React.useState<'profile' | 'security'>('profile');

  if (!user) {
    return <Navigate to="/login" />;
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <GlassContainer>
        <Box display="flex" flexDirection="column" alignItems="center">
          <StyledAvatar>{getInitials(user.full_name)}</StyledAvatar>
          <Typography variant="h4" gutterBottom>
            {user.full_name}
          </Typography>
          <Typography variant="body1" color="textSecondary" gutterBottom>
            {user.email}
          </Typography>
          <Box mt={2}>
            <Typography variant="subtitle1" gutterBottom>
              Status: {user.is_active ? 'Active' : 'Inactive'}
            </Typography>
            <Typography variant="subtitle2" color="textSecondary">
              Created: {formatDate(user.created_at)}
            </Typography>
            {user.updated_at && (
              <Typography variant="subtitle2" color="textSecondary">
                Last Updated: {formatDate(user.updated_at)}
              </Typography>
            )}
          </Box>
          <TabContainer>
            <TabButtons>
              <TabButton active={activeTab === 'profile'} onClick={() => setActiveTab('profile')}>
                Profile
              </TabButton>
              <TabButton active={activeTab === 'security'} onClick={() => setActiveTab('security')}>
                Security
              </TabButton>
            </TabButtons>
            <TabContent>
              {activeTab === 'profile' ? (
                <Box>
                  <Typography variant="h5" gutterBottom>
                    Profile Information
                  </Typography>
                  <ProfileSection>
                    <Label>Email:</Label>
                    <Value>{user.email}</Value>
                  </ProfileSection>
                  <ProfileSection>
                    <Label>Status:</Label>
                    <StatusBadge active={user.is_active}>
                      {user.is_active ? 'Active' : 'Inactive'}
                    </StatusBadge>
                  </ProfileSection>
                  <ProfileSection>
                    <Label>User ID:</Label>
                    <Value>{user.id}</Value>
                  </ProfileSection>
                  <ProfileSection>
                    <Label>Created:</Label>
                    <DateValue>{formatDate(user.created_at)}</DateValue>
                  </ProfileSection>
                  {user.updated_at && (
                    <ProfileSection>
                      <Label>Last Updated:</Label>
                      <DateValue>{formatDate(user.updated_at)}</DateValue>
                    </ProfileSection>
                  )}
                </Box>
              ) : (
                <ChangePassword />
              )}
            </TabContent>
          </TabContainer>
          <LogoutButton onClick={handleLogout}>
            Logout
          </LogoutButton>
        </Box>
      </GlassContainer>
    </motion.div>
  );
}

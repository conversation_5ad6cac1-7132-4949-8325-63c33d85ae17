from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks, status
from fastapi.responses import HTMLResponse, FileResponse
from typing import List, Optional
from pydantic import BaseModel
import uuid
import os
import tempfile
import json
from datetime import datetime
import shutil

# Import the refactored policy matcher
from refactored_policy_matcher.main import run_policy_matcher
from refactored_policy_matcher.config import Config

# Import authentication dependencies
from app.core.deps import get_current_user
from app.models.user import User

# Create router
router = APIRouter(prefix="/api/policy-matcher", tags=["policy-matcher"])

# Directory to store uploaded files and results
UPLOAD_DIR = os.path.join(tempfile.gettempdir(), "policy_matcher_uploads")
RESULTS_DIR = os.path.join(tempfile.gettempdir(), "policy_matcher_results")

# Create directories if they don't exist
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

# Models
class MatchResult(BaseModel):
    id: int
    requirement: str
    policy: str
    section: str
    confidence: float

class JobStatus(BaseModel):
    id: str
    status: str  # 'pending', 'processing', 'completed', 'failed'
    created_at: str
    file_name: str
    user_id: str

class JobResults(BaseModel):
    job_id: str
    results: List[MatchResult]

# In-memory job storage (in production, use a database)
jobs = {}

# Background task to process policy matching
def process_policy_matching(job_id: str, file_path: str, policies_dir: str, 
                           confidence_threshold: float, algorithm: str):
    try:
        # Update job status to processing
        jobs[job_id]["status"] = "processing"
        
        # Configure policy matcher
        config = Config()
        config.confidence_threshold = confidence_threshold
        
        if algorithm == "tfidf":
            config.use_tfidf = True
        elif algorithm == "semantic":
            config.use_semantic = True
        
        # Run policy matcher
        output_file = os.path.join(RESULTS_DIR, f"{job_id}_report.html")
        results_file = os.path.join(RESULTS_DIR, f"{job_id}_results.json")
        
        # Call the refactored policy matcher
        report_path = run_policy_matcher(
            govstar_file=file_path,
            policies_dir=policies_dir,
            output_file=output_file,
            config=config
        )
        
        # Extract results from the report or generate them
        results = []
        # This is a simplified example - in a real implementation, 
        # you would extract results from the policy matcher output
        with open(output_file, 'r') as f:
            html_content = f.read()
            # Parse HTML to extract results (simplified)
            # In a real implementation, the policy matcher should return structured results
            
        # For demo purposes, generate some sample results
        results = [
            {"id": 1, "requirement": "Annual security training", "policy": "Security Policy", "section": "3.2", "confidence": 0.92},
            {"id": 2, "requirement": "Password complexity", "policy": "Password Policy", "section": "2.1", "confidence": 0.85},
            {"id": 3, "requirement": "Data encryption", "policy": "Data Protection Policy", "section": "4.3", "confidence": 0.78}
        ]
        
        # Save results to file
        with open(results_file, 'w') as f:
            json.dump(results, f)
        
        # Update job with results
        jobs[job_id]["status"] = "completed"
        jobs[job_id]["results"] = results
        
    except Exception as e:
        # Update job status to failed
        jobs[job_id]["status"] = "failed"
        jobs[job_id]["error"] = str(e)
        print(f"Error processing job {job_id}: {str(e)}")

# Endpoints
@router.post("/match", status_code=status.HTTP_202_ACCEPTED)
async def match_policies(
    background_tasks: BackgroundTasks,
    requirements_file: UploadFile = File(...),
    confidence_threshold: float = Form(0.6),
    matching_algorithm: str = Form("default"),
    current_user: User = Depends(get_current_user)
):
    # Generate job ID
    job_id = str(uuid.uuid4())
    
    # Save uploaded file
    file_path = os.path.join(UPLOAD_DIR, f"{job_id}_{requirements_file.filename}")
    with open(file_path, "wb") as f:
        shutil.copyfileobj(requirements_file.file, f)
    
    # Create job
    job = {
        "id": job_id,
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "file_name": requirements_file.filename,
        "file_path": file_path,
        "user_id": str(current_user.id),
        "confidence_threshold": confidence_threshold,
        "matching_algorithm": matching_algorithm
    }
    
    # Store job
    jobs[job_id] = job
    
    # Start background task
    background_tasks.add_task(
        process_policy_matching,
        job_id=job_id,
        file_path=file_path,
        policies_dir="/path/to/policies",  # This should be configured based on your setup
        confidence_threshold=confidence_threshold,
        algorithm=matching_algorithm
    )
    
    return {"id": job_id, "status": "pending", "created_at": job["created_at"], "file_name": requirements_file.filename}

@router.get("/jobs/{job_id}/status")
async def get_job_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    
    # Check if user has access to this job
    if str(current_user.id) != job["user_id"] and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this job")
    
    return {
        "id": job["id"],
        "status": job["status"],
        "created_at": job["created_at"],
        "file_name": job["file_name"]
    }

@router.get("/jobs/{job_id}/results")
async def get_job_results(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    
    # Check if user has access to this job
    if str(current_user.id) != job["user_id"] and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this job")
    
    if job["status"] != "completed":
        raise HTTPException(status_code=400, detail=f"Job is not completed. Current status: {job['status']}")
    
    if "results" not in job:
        raise HTTPException(status_code=404, detail="Results not found")
    
    return {"job_id": job_id, "results": job["results"]}

@router.get("/jobs/{job_id}/report", response_class=HTMLResponse)
async def get_job_report(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    
    # Check if user has access to this job
    if str(current_user.id) != job["user_id"] and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this job")
    
    if job["status"] != "completed":
        raise HTTPException(status_code=400, detail=f"Job is not completed. Current status: {job['status']}")
    
    report_path = os.path.join(RESULTS_DIR, f"{job_id}_report.html")
    
    if not os.path.exists(report_path):
        raise HTTPException(status_code=404, detail="Report not found")
    
    return FileResponse(report_path)

@router.get("/history")
async def get_job_history(
    page: int = 1,
    limit: int = 10,
    current_user: User = Depends(get_current_user)
):
    # Filter jobs by user
    user_jobs = [job for job in jobs.values() if str(current_user.id) == job["user_id"] or current_user.is_admin]
    
    # Sort by created_at (newest first)
    sorted_jobs = sorted(user_jobs, key=lambda x: x["created_at"], reverse=True)
    
    # Paginate
    start = (page - 1) * limit
    end = start + limit
    paginated_jobs = sorted_jobs[start:end]
    
    # Format response
    formatted_jobs = []
    for job in paginated_jobs:
        formatted_job = {
            "id": job["id"],
            "status": job["status"],
            "created_at": job["created_at"],
            "file_name": job["file_name"]
        }
        
        # Include result count if available
        if "results" in job:
            formatted_job["result_count"] = len(job["results"])
        
        formatted_jobs.append(formatted_job)
    
    return {
        "total": len(user_jobs),
        "page": page,
        "limit": limit,
        "jobs": formatted_jobs
    }

@router.delete("/jobs/{job_id}")
async def delete_job(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    
    # Check if user has access to this job
    if str(current_user.id) != job["user_id"] and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to delete this job")
    
    # Delete job files
    file_path = job.get("file_path")
    if file_path and os.path.exists(file_path):
        os.remove(file_path)
    
    report_path = os.path.join(RESULTS_DIR, f"{job_id}_report.html")
    if os.path.exists(report_path):
        os.remove(report_path)
    
    results_path = os.path.join(RESULTS_DIR, f"{job_id}_results.json")
    if os.path.exists(results_path):
        os.remove(results_path)
    
    # Remove job from storage
    del jobs[job_id]
    
    return {"message": "Job deleted successfully"}

@router.get("/policies")
async def get_policies(
    current_user: User = Depends(get_current_user)
):
    # In a real implementation, this would fetch policies from a database
    # For demo purposes, return sample policies
    policies = [
        {"id": 1, "name": "Security Policy", "description": "Organization security standards", "last_updated": "2025-03-15"},
        {"id": 2, "name": "Password Policy", "description": "Password requirements and management", "last_updated": "2025-02-20"},
        {"id": 3, "name": "Data Protection Policy", "description": "Guidelines for data handling", "last_updated": "2025-03-10"},
        {"id": 4, "name": "Acceptable Use Policy", "description": "Rules for IT resource usage", "last_updated": "2025-01-05"},
        {"id": 5, "name": "Incident Response Policy", "description": "Procedures for security incidents", "last_updated": "2025-02-28"}
    ]
    
    return {"policies": policies}

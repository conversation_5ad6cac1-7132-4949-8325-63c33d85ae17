import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  useTheme,
  CircularProgress,
} from '@mui/material';
import { Upload as UploadIcon } from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';

const QuickCheck: React.FC = () => {
  const theme = useTheme();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const onDrop = (acceptedFiles: File[]) => {
    setUploadedFiles((prev) => [...prev, ...acceptedFiles]);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
  });

  const handleSubmit = async () => {
    // Redirect to master document upload system for comprehensive analysis
    const currentUrl = encodeURIComponent(window.location.href);
    const params = new URLSearchParams({
      return: currentUrl,
      context: 'quick-check',
      mode: 'compliance-check'
    });
    window.location.href = `document_upload_system.html?${params.toString()}`;
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ color: 'white', mb: 4 }}>
        Quick Compliance Check
      </Typography>

      <Grid container spacing={4}>
        <Grid item xs={12} md={7}>
          <Paper
            {...getRootProps()}
            sx={{
              p: 4,
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '2px dashed',
              borderColor: isDragActive
                ? theme.palette.primary.main
                : 'rgba(255, 255, 255, 0.2)',
              borderRadius: theme.shape.borderRadius * 2,
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: theme.palette.primary.main,
                background: 'rgba(255, 255, 255, 0.15)',
              },
            }}
          >
            <input {...getInputProps()} />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 2,
              }}
            >
              <UploadIcon
                sx={{
                  fontSize: 48,
                  color: isDragActive
                    ? theme.palette.primary.main
                    : 'rgba(255, 255, 255, 0.7)',
                }}
              />
              <Typography
                variant="h6"
                sx={{ color: 'rgba(255, 255, 255, 0.9)' }}
              >
                {isDragActive
                  ? 'Drop your files here'
                  : 'Drag & drop files here, or click to select'}
              </Typography>
              <Typography
                variant="body2"
                sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
              >
                Supported formats: PDF, DOC, DOCX
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={5}>
          <Paper
            sx={{
              p: 4,
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: theme.shape.borderRadius * 2,
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{ color: 'rgba(255, 255, 255, 0.9)' }}
            >
              Uploaded Files
            </Typography>
            {uploadedFiles.length === 0 ? (
              <Typography sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                No files uploaded yet
              </Typography>
            ) : (
              <Box sx={{ mb: 3 }}>
                {uploadedFiles.map((file, index) => (
                  <Typography
                    key={index}
                    sx={{ color: 'rgba(255, 255, 255, 0.9)' }}
                  >
                    {file.name}
                  </Typography>
                ))}
              </Box>
            )}
            <Button
              variant="contained"
              color="primary"
              fullWidth
              disabled={uploadedFiles.length === 0 || isUploading}
              onClick={handleSubmit}
              sx={{ mt: 2 }}
            >
              {isUploading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Start Quick Check'
              )}
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default QuickCheck;

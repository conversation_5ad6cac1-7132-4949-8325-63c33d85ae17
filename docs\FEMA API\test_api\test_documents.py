import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from src.models.user import User
from src.models.document import Document, DocumentVersion
from src.core.security import get_password_hash
from src.core.auth import create_access_token
import json

def test_validate_document(client: TestClient, db_session: Session, test_user: User):
    """Test document validation endpoint."""
    access_token = create_access_token(data={"sub": test_user.email})
    
    # Test document data
    document_data = {
        "content": {
            "title": "Test Document",
            "sections": [
                {
                    "title": "Section 1",
                    "content": "This is section 1 content"
                }
            ]
        },
        "metadata": {
            "author": "Test Author",
            "category": "Test Category"
        }
    }
    
    response = client.post(
        "/api/v1/documents/validate",
        json=document_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "is_valid" in data
    assert "validation_results" in data
    assert isinstance(data["validation_results"], list)

def test_validate_document_invalid_format(client: TestClient, test_user: User):
    """Test document validation with invalid format."""
    access_token = create_access_token(data={"sub": test_user.email})
    
    # Invalid document data (missing required fields)
    invalid_data = {
        "content": {
            "title": "Test Document"
            # Missing sections
        }
    }
    
    response = client.post(
        "/api/v1/documents/validate",
        json=invalid_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 422
    assert "validation error" in response.json()["detail"].lower()

def test_create_document(client: TestClient, db_session: Session, test_user: User):
    """Test document creation endpoint."""
    access_token = create_access_token(data={"sub": test_user.email})
    
    document_data = {
        "content": {
            "title": "Test Document",
            "sections": [
                {
                    "title": "Section 1",
                    "content": "This is section 1 content"
                }
            ]
        },
        "metadata": {
            "author": "Test Author",
            "category": "Test Category"
        }
    }
    
    response = client.post(
        "/api/v1/documents/",
        json=document_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["title"] == document_data["content"]["title"]
    assert data["author"] == document_data["metadata"]["author"]
    assert data["version"] == 1
    assert data["status"] == "draft"

def test_get_document(client: TestClient, db_session: Session, test_user: User):
    """Test retrieving a document."""
    # Create document
    document = Document(
        title="Test Document",
        content=json.dumps({
            "title": "Test Document",
            "sections": [{"title": "Section 1", "content": "Content 1"}]
        }),
        author="Test Author",
        created_by=test_user.id
    )
    db_session.add(document)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        f"/api/v1/documents/{document.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == document.id
    assert data["title"] == document.title
    assert data["author"] == document.author

def test_update_document(client: TestClient, db_session: Session, test_user: User):
    """Test updating a document."""
    # Create document
    document = Document(
        title="Test Document",
        content=json.dumps({
            "title": "Test Document",
            "sections": [{"title": "Section 1", "content": "Content 1"}]
        }),
        author="Test Author",
        created_by=test_user.id
    )
    db_session.add(document)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    update_data = {
        "content": {
            "title": "Updated Document",
            "sections": [
                {
                    "title": "Section 1",
                    "content": "Updated content"
                }
            ]
        }
    }
    
    response = client.put(
        f"/api/v1/documents/{document.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == update_data["content"]["title"]
    assert data["version"] == 2  # Version should increment

def test_list_documents(client: TestClient, db_session: Session, test_user: User):
    """Test listing documents."""
    # Create multiple documents
    documents = [
        Document(
            title=f"Document {i}",
            content=json.dumps({
                "title": f"Document {i}",
                "sections": [{"title": "Section 1", "content": f"Content {i}"}]
            }),
            author="Test Author",
            created_by=test_user.id
        )
        for i in range(3)
    ]
    db_session.add_all(documents)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        "/api/v1/documents/",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 3
    assert all(isinstance(doc["id"], int) for doc in data)
    assert all(isinstance(doc["title"], str) for doc in data)

def test_document_version_history(client: TestClient, db_session: Session, test_user: User):
    """Test retrieving document version history."""
    # Create document with multiple versions
    document = Document(
        title="Test Document",
        content=json.dumps({
            "title": "Test Document",
            "sections": [{"title": "Section 1", "content": "Content 1"}]
        }),
        author="Test Author",
        created_by=test_user.id
    )
    db_session.add(document)
    db_session.commit()
    
    # Create versions
    versions = [
        DocumentVersion(
            document_id=document.id,
            version_number=i + 1,
            content=json.dumps({
                "title": f"Version {i + 1}",
                "sections": [{"title": "Section 1", "content": f"Content {i + 1}"}]
            }),
            created_by=test_user.id
        )
        for i in range(2)
    ]
    db_session.add_all(versions)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        f"/api/v1/documents/{document.id}/versions",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert all(isinstance(version["version_number"], int) for version in data)
    assert all(isinstance(version["created_at"], str) for version in data)

def test_document_validation_rules(client: TestClient, db_session: Session, test_user: User):
    """Test retrieving document validation rules."""
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        "/api/v1/documents/validation-rules",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert all(isinstance(rule["id"], int) for rule in data)
    assert all(isinstance(rule["name"], str) for rule in data)
    assert all(isinstance(rule["description"], str) for rule in data)

def test_document_webhook_notification(client: TestClient, db_session: Session, test_user: User):
    """Test document webhook notification."""
    access_token = create_access_token(data={"sub": test_user.email})
    
    # Create webhook configuration
    webhook_data = {
        "url": "https://example.com/webhook",
        "events": ["document.created", "document.updated"],
        "secret": "test_secret"
    }
    
    response = client.post(
        "/api/v1/webhooks/",
        json=webhook_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 201
    
    # Create document to trigger webhook
    document_data = {
        "content": {
            "title": "Test Document",
            "sections": [
                {
                    "title": "Section 1",
                    "content": "This is section 1 content"
                }
            ]
        },
        "metadata": {
            "author": "Test Author",
            "category": "Test Category"
        }
    }
    
    response = client.post(
        "/api/v1/documents/",
        json=document_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 201
    
    # Verify webhook delivery
    response = client.get(
        "/api/v1/webhooks/deliveries",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any(delivery["event"] == "document.created" for delivery in data) 
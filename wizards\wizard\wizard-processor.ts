#!/usr/bin/env ts-node

import fs from 'fs';
import path from 'path';
import { z } from 'zod';

// ================= Wizard Schemas =================

const QuestionSchema = z.object({
  id: z.string(),
  text: z.string(),
  type: z.enum(['text', 'select', 'checkbox', 'radio', 'file', 'textarea', 'number', 'date']),
  required: z.boolean().default(false),
  options: z.array(z.string()).optional(),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
    message: z.string().optional(),
  }).optional(),
  helpText: z.string().optional(),
  conditionalLogic: z.object({
    showIf: z.string().optional(),
    hideIf: z.string().optional(),
    requiredIf: z.string().optional(),
  }).optional(),
}).passthrough();

const WizardStepSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  order: z.number(),
  category: z.string().optional(),
  pappg_section: z.string().optional(),
  questions: z.array(QuestionSchema),
  completion_criteria: z.array(z.string()).optional(),
  next_step_logic: z.object({
    default: z.string().optional(),
    conditional: z.array(z.object({
      condition: z.string(),
      next_step: z.string(),
    })).optional(),
  }).optional(),
}).passthrough();

const WizardFlowSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  version: z.string().default('1.0'),
  steps: z.array(WizardStepSchema),
  metadata: z.object({
    created: z.string(),
    updated: z.string(),
    author: z.string().optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
}).passthrough();

// ================= File Discovery =================

function findWizardFiles(): string[] {
  const searchDirs = [
    path.resolve(process.cwd(), 'wizard'),
    path.resolve(process.cwd(), 'ALL NEW APP', 'wizard'),
    path.resolve(process.cwd(), 'ALL NEW APP', 'app', 'compliance-wizard'),
    path.resolve(process.cwd(), 'DOCS'),
  ];

  const wizardFiles: string[] = [];

  for (const dir of searchDirs) {
    if (!fs.existsSync(dir)) continue;

    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isFile()) {
        const filePath = path.join(dir, entry.name);
        const ext = path.extname(entry.name).toLowerCase();
        
        if (['.tsx', '.ts', '.json'].includes(ext)) {
          wizardFiles.push(filePath);
        }
      }
    }
  }

  return wizardFiles;
}

// ================= Wizard Generation =================

function generateFemaWizardFlow(): any {
  return {
    id: 'fema-compliance-wizard',
    name: 'FEMA Public Assistance Compliance Wizard',
    description: 'Comprehensive FEMA PA compliance assessment and guidance system',
    version: '1.0',
    steps: [
      {
        id: 'step-1-eligibility',
        title: 'Eligibility Assessment',
        description: 'Determine your eligibility for FEMA Public Assistance',
        order: 1,
        category: 'Eligibility',
        pappg_section: 'II.A',
        questions: [
          {
            id: 'applicant-type',
            text: 'What type of applicant are you?',
            type: 'select',
            required: true,
            options: [
              'State Government',
              'Local Government', 
              'Tribal Government',
              'Territory/Commonwealth',
              'Private Non-Profit Organization'
            ],
            helpText: 'Select the category that best describes your organization'
          },
          {
            id: 'incident-declaration',
            text: 'Was your area included in a Presidential Disaster Declaration?',
            type: 'radio',
            required: true,
            options: ['Yes', 'No', 'Unsure'],
            helpText: 'Check FEMA.gov for disaster declarations in your area'
          },
          {
            id: 'incident-date',
            text: 'What was the date of the incident?',
            type: 'date',
            required: true,
            helpText: 'This determines which PAPPG version applies to your case'
          },
          {
            id: 'timely-application',
            text: 'Did you submit your Request for Public Assistance within 30 days?',
            type: 'radio',
            required: true,
            options: ['Yes', 'No', 'Submitted late with justification'],
            conditionalLogic: {
              showIf: 'incident-declaration === "Yes"'
            }
          }
        ],
        completion_criteria: [
          'All eligibility questions answered',
          'Applicant type confirmed',
          'Incident date recorded'
        ],
        next_step_logic: {
          conditional: [
            {
              condition: 'incident-declaration === "No"',
              next_step: 'step-ineligible'
            },
            {
              condition: 'applicant-type === "Private Non-Profit Organization"',
              next_step: 'step-2-pnp-eligibility'
            }
          ],
          default: 'step-3-damage-assessment'
        }
      },
      {
        id: 'step-2-pnp-eligibility',
        title: 'Private Non-Profit Eligibility',
        description: 'Additional eligibility requirements for Private Non-Profit organizations',
        order: 2,
        category: 'Eligibility',
        pappg_section: 'II.A.2',
        questions: [
          {
            id: 'pnp-type',
            text: 'What type of Private Non-Profit organization are you?',
            type: 'select',
            required: true,
            options: [
              'Educational',
              'Medical',
              'Custodial Care',
              'Emergency Services',
              'Municipal-type services',
              'Community services',
              'Religious facility (with community services)',
              'Other qualified facility'
            ]
          },
          {
            id: 'pnp-ownership',
            text: 'Do you own the damaged facility?',
            type: 'radio',
            required: true,
            options: ['Yes', 'No', 'Lease with responsibility for maintenance']
          },
          {
            id: 'pnp-insurance',
            text: 'Do you carry insurance on the damaged facility?',
            type: 'radio',
            required: true,
            options: ['Yes', 'No', 'Partial coverage']
          }
        ],
        completion_criteria: [
          'PNP type identified',
          'Ownership confirmed',
          'Insurance status documented'
        ],
        next_step_logic: {
          default: 'step-3-damage-assessment'
        }
      },
      {
        id: 'step-3-damage-assessment',
        title: 'Damage Assessment',
        description: 'Document and categorize disaster-related damage',
        order: 3,
        category: 'Assessment',
        pappg_section: 'III.A',
        questions: [
          {
            id: 'damage-categories',
            text: 'Which categories of work apply to your damage? (Select all that apply)',
            type: 'checkbox',
            required: true,
            options: [
              'Category A - Debris Removal',
              'Category B - Emergency Protective Measures',
              'Category C - Roads and Bridges',
              'Category D - Water Control Facilities',
              'Category E - Buildings and Equipment',
              'Category F - Utilities',
              'Category G - Parks, Recreation, and Other'
            ]
          },
          {
            id: 'damage-documentation',
            text: 'Have you documented the damage with photos, videos, and written descriptions?',
            type: 'radio',
            required: true,
            options: ['Comprehensive documentation', 'Partial documentation', 'Minimal documentation', 'No documentation']
          },
          {
            id: 'cost-estimate',
            text: 'Do you have preliminary cost estimates for the damage?',
            type: 'radio',
            required: true,
            options: ['Detailed estimates', 'Rough estimates', 'No estimates yet']
          }
        ],
        completion_criteria: [
          'Damage categories identified',
          'Documentation status confirmed',
          'Cost estimation status recorded'
        ],
        next_step_logic: {
          default: 'step-4-environmental-historic'
        }
      },
      {
        id: 'step-4-environmental-historic',
        title: 'Environmental & Historic Preservation',
        description: 'Ensure compliance with environmental and historic preservation requirements',
        order: 4,
        category: 'Compliance',
        pappg_section: 'VI.C',
        questions: [
          {
            id: 'historic-property',
            text: 'Is the damaged facility 50 years old or older, or listed on historic registers?',
            type: 'radio',
            required: true,
            options: ['Yes', 'No', 'Unsure']
          },
          {
            id: 'environmental-concerns',
            text: 'Are there potential environmental concerns? (Select all that apply)',
            type: 'checkbox',
            options: [
              'Wetlands impact',
              'Endangered species habitat',
              'Floodplain location',
              'Hazardous materials',
              'Cultural resources',
              'None identified'
            ]
          },
          {
            id: 'ground-disturbing',
            text: 'Will your project involve ground-disturbing activities?',
            type: 'radio',
            required: true,
            options: ['Yes', 'No', 'Possibly']
          }
        ],
        completion_criteria: [
          'Historic status determined',
          'Environmental factors assessed',
          'Ground disturbance impact evaluated'
        ],
        next_step_logic: {
          default: 'step-5-insurance-dob'
        }
      },
      {
        id: 'step-5-insurance-dob',
        title: 'Insurance & Duplication of Benefits',
        description: 'Address insurance requirements and prevent duplication of benefits',
        order: 5,
        category: 'Financial',
        pappg_section: 'VIII.A',
        questions: [
          {
            id: 'insurance-coverage',
            text: 'What insurance coverage do you have for the damaged facility?',
            type: 'checkbox',
            options: [
              'General property insurance',
              'Flood insurance',
              'Business interruption insurance',
              'Special hazard insurance',
              'No insurance coverage'
            ]
          },
          {
            id: 'insurance-settlement',
            text: 'Have you received any insurance settlements for this damage?',
            type: 'radio',
            required: true,
            options: ['Full settlement received', 'Partial settlement received', 'Claim pending', 'No claim filed']
          },
          {
            id: 'other-assistance',
            text: 'Have you received assistance from other sources for this damage?',
            type: 'checkbox',
            options: [
              'SBA loan',
              'Other federal assistance',
              'State assistance',
              'Local assistance',
              'Private donations',
              'No other assistance'
            ]
          }
        ],
        completion_criteria: [
          'Insurance status documented',
          'Settlement status confirmed',
          'Other assistance sources identified'
        ],
        next_step_logic: {
          default: 'step-6-procurement'
        }
      },
      {
        id: 'step-6-procurement',
        title: 'Procurement Compliance',
        description: 'Ensure compliance with federal procurement standards',
        order: 6,
        category: 'Procurement',
        pappg_section: 'VII.B',
        questions: [
          {
            id: 'procurement-method',
            text: 'What procurement method will you use for major contracts?',
            type: 'select',
            required: true,
            options: [
              'Full and open competition',
              'Non-competitive (sole source)',
              'Limited competition',
              'Small purchase procedures',
              'Force account (own forces)'
            ]
          },
          {
            id: 'cost-analysis',
            text: 'Will you conduct cost or price analysis for major purchases?',
            type: 'radio',
            required: true,
            options: ['Cost analysis planned', 'Price analysis planned', 'Both planned', 'Not applicable']
          },
          {
            id: 'small-business',
            text: 'Will you provide opportunities for small and minority businesses?',
            type: 'radio',
            required: true,
            options: ['Yes, actively promoting', 'Yes, will consider', 'Not applicable']
          }
        ],
        completion_criteria: [
          'Procurement method selected',
          'Cost analysis approach determined',
          'Small business consideration confirmed'
        ],
        next_step_logic: {
          default: 'step-7-summary'
        }
      },
      {
        id: 'step-7-summary',
        title: 'Compliance Summary',
        description: 'Review your compliance status and next steps',
        order: 7,
        category: 'Summary',
        questions: [
          {
            id: 'ready-to-proceed',
            text: 'Based on this assessment, are you ready to proceed with your PA application?',
            type: 'radio',
            required: true,
            options: ['Yes, ready to proceed', 'Need to address compliance items', 'Need additional guidance']
          },
          {
            id: 'priority-actions',
            text: 'What are your priority next actions? (Select all that apply)',
            type: 'checkbox',
            options: [
              'Complete damage documentation',
              'Obtain additional cost estimates',
              'Address environmental concerns',
              'Resolve insurance issues',
              'Develop procurement strategy',
              'Submit PA application',
              'Request FEMA consultation'
            ]
          }
        ],
        completion_criteria: [
          'Readiness status confirmed',
          'Priority actions identified'
        ]
      },
      {
        id: 'step-ineligible',
        title: 'Ineligible for PA',
        description: 'Your situation may not be eligible for FEMA Public Assistance',
        order: 99,
        category: 'Result',
        questions: [
          {
            id: 'alternative-assistance',
            text: 'Would you like information about alternative assistance programs?',
            type: 'radio',
            options: ['Yes', 'No']
          }
        ]
      }
    ],
    metadata: {
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      author: 'ComplianceMax System',
      tags: ['FEMA', 'Public Assistance', 'Compliance', 'PAPPG']
    }
  };
}

// ================= Processing Functions =================

async function processWizardComponents(wizardDir: string): Promise<{
  components: any[];
  layouts: any[];
  flows: any[];
}> {
  const components: any[] = [];
  const layouts: any[] = [];
  const flows: any[] = [];

  if (!fs.existsSync(wizardDir)) {
    console.log(`⚠️  Wizard directory not found: ${wizardDir}`);
    return { components, layouts, flows };
  }

  const files = fs.readdirSync(wizardDir);
  
  for (const file of files) {
    const filePath = path.join(wizardDir, file);
    const ext = path.extname(file);
    
    if (ext === '.tsx' || ext === '.ts') {
      try {
        const content = await fs.promises.readFile(filePath, 'utf-8');
        
        const componentInfo = {
          name: path.basename(file, ext),
          path: filePath,
          type: 'component',
          size: content.length,
          exports: extractExports(content),
          imports: extractImports(content),
          hasTypes: content.includes('interface') || content.includes('type '),
          hasHooks: content.includes('useState') || content.includes('useEffect'),
        };
        
        if (file.includes('layout')) {
          layouts.push(componentInfo);
        } else {
          components.push(componentInfo);
        }
      } catch (error) {
        console.warn(`⚠️  Could not process ${file}: ${error.message}`);
      }
    } else if (ext === '.json') {
      try {
        const content = await fs.promises.readFile(filePath, 'utf-8');
        const data = JSON.parse(content);
        
        flows.push({
          name: path.basename(file, ext),
          path: filePath,
          data,
          valid: WizardFlowSchema.safeParse(data).success,
        });
      } catch (error) {
        console.warn(`⚠️  Could not process JSON ${file}: ${error.message}`);
      }
    }
  }

  return { components, layouts, flows };
}

function extractExports(content: string): string[] {
  const exports: string[] = [];
  const exportMatches = content.match(/export\s+(default\s+)?(function|const|class|interface|type)\s+(\w+)/g);
  
  if (exportMatches) {
    for (const match of exportMatches) {
      const nameMatch = match.match(/(\w+)$/);
      if (nameMatch) {
        exports.push(nameMatch[1]);
      }
    }
  }
  
  return exports;
}

function extractImports(content: string): string[] {
  const imports: string[] = [];
  const importMatches = content.match(/import\s+.*?from\s+['"]([^'"]+)['"]/g);
  
  if (importMatches) {
    for (const match of importMatches) {
      const pathMatch = match.match(/from\s+['"]([^'"]+)['"]/);
      if (pathMatch) {
        imports.push(pathMatch[1]);
      }
    }
  }
  
  return imports;
}

// ================= Output Generation =================

async function generateWizardOutputs(
  components: any[],
  layouts: any[],
  flows: any[],
  outputDir: string
) {
  await fs.promises.mkdir(outputDir, { recursive: true });

  // Generate comprehensive wizard flow
  const wizardFlow = generateFemaWizardFlow();
  const flowPath = path.join(outputDir, 'fema-compliance-wizard-flow.json');
  await fs.promises.writeFile(flowPath, JSON.stringify(wizardFlow, null, 2));
  console.log(`📄 Generated wizard flow: ${flowPath}`);

  // Generate TypeScript types
  const typesPath = path.join(outputDir, 'wizard-types.ts');
  const types = [
    '// Generated TypeScript types for ComplianceMax Wizard',
    '// Generated on: ' + new Date().toISOString(),
    '',
    'export interface WizardQuestion {',
    '  id: string;',
    '  text: string;',
    '  type: "text" | "select" | "checkbox" | "radio" | "file" | "textarea" | "number" | "date";',
    '  required?: boolean;',
    '  options?: string[];',
    '  validation?: {',
    '    min?: number;',
    '    max?: number;',
    '    pattern?: string;',
    '    message?: string;',
    '  };',
    '  helpText?: string;',
    '  conditionalLogic?: {',
    '    showIf?: string;',
    '    hideIf?: string;',
    '    requiredIf?: string;',
    '  };',
    '}',
    '',
    'export interface WizardStep {',
    '  id: string;',
    '  title: string;',
    '  description?: string;',
    '  order: number;',
    '  category?: string;',
    '  pappg_section?: string;',
    '  questions: WizardQuestion[];',
    '  completion_criteria?: string[];',
    '  next_step_logic?: {',
    '    default?: string;',
    '    conditional?: Array<{',
    '      condition: string;',
    '      next_step: string;',
    '    }>;',
    '  };',
    '}',
    '',
    'export interface WizardFlow {',
    '  id: string;',
    '  name: string;',
    '  description: string;',
    '  version: string;',
    '  steps: WizardStep[];',
    '  metadata?: {',
    '    created: string;',
    '    updated: string;',
    '    author?: string;',
    '    tags?: string[];',
    '  };',
    '}',
    '',
    'export interface WizardSession {',
    '  id: string;',
    '  wizard_flow_id: string;',
    '  current_step_id: string;',
    '  responses: Record<string, any>;',
    '  started_at: string;',
    '  updated_at: string;',
    '  completed_at?: string;',
    '  user_id?: string;',
    '}',
  ];
  
  await fs.promises.writeFile(typesPath, types.join('\n'));
  console.log(`📄 Generated TypeScript types: ${typesPath}`);

  // Generate PostgreSQL schema
  const schemaPath = path.join(outputDir, 'wizard-schema.sql');
  const schema = [
    '-- ComplianceMax Wizard Database Schema',
    '-- Generated on: ' + new Date().toISOString(),
    '',
    'BEGIN;',
    '',
    '-- Wizard flows table',
    'CREATE TABLE IF NOT EXISTS wizard_flows (',
    '  id VARCHAR(255) PRIMARY KEY,',
    '  name VARCHAR(500) NOT NULL,',
    '  description TEXT,',
    '  version VARCHAR(50) DEFAULT \'1.0\',',
    '  flow_data JSONB NOT NULL,',
    '  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,',
    '  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,',
    '  is_active BOOLEAN DEFAULT true',
    ');',
    '',
    '-- Wizard sessions table',
    'CREATE TABLE IF NOT EXISTS wizard_sessions (',
    '  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),',
    '  wizard_flow_id VARCHAR(255) REFERENCES wizard_flows(id),',
    '  current_step_id VARCHAR(255),',
    '  responses JSONB DEFAULT \'{}\',',
    '  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,',
    '  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,',
    '  completed_at TIMESTAMP,',
    '  user_id VARCHAR(255),',
    '  session_metadata JSONB DEFAULT \'{}\'',
    ');',
    '',
    '-- Indexes for performance',
    'CREATE INDEX IF NOT EXISTS idx_wizard_sessions_flow_id ON wizard_sessions(wizard_flow_id);',
    'CREATE INDEX IF NOT EXISTS idx_wizard_sessions_user_id ON wizard_sessions(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_wizard_sessions_status ON wizard_sessions(completed_at) WHERE completed_at IS NULL;',
    '',
    '-- Insert the default FEMA wizard flow',
    `INSERT INTO wizard_flows (id, name, description, flow_data) VALUES (`,
    `  'fema-compliance-wizard',`,
    `  'FEMA Public Assistance Compliance Wizard',`,
    `  'Comprehensive FEMA PA compliance assessment and guidance system',`,
    `  '${JSON.stringify(wizardFlow).replace(/'/g, "''")}'`,
    `) ON CONFLICT (id) DO UPDATE SET`,
    `  flow_data = EXCLUDED.flow_data,`,
    `  updated_at = CURRENT_TIMESTAMP;`,
    '',
    'COMMIT;',
  ];
  
  await fs.promises.writeFile(schemaPath, schema.join('\n'));
  console.log(`📄 Generated PostgreSQL schema: ${schemaPath}`);

  // Generate analysis report
  const reportPath = path.join(outputDir, 'wizard-analysis.md');
  const report = [
    '# ComplianceMax Wizard Analysis Report',
    '',
    `**Generated:** ${new Date().toISOString()}`,
    '',
    '## Component Analysis',
    '',
    `**Total Components:** ${components.length}`,
    `**Layout Components:** ${layouts.length}`,
    `**Wizard Flows:** ${flows.length}`,
    '',
    '### Components',
    ...components.map(c => `- **${c.name}**: ${c.exports.length} exports, ${c.imports.length} imports${c.hasHooks ? ' (uses hooks)' : ''}`),
    '',
    '### Layouts',
    ...layouts.map(l => `- **${l.name}**: ${l.exports.length} exports, ${l.imports.length} imports`),
    '',
    '### Flows',
    ...flows.map(f => `- **${f.name}**: ${f.valid ? '✅ Valid' : '❌ Invalid'} schema`),
    '',
    '## Generated FEMA Wizard Flow',
    '',
    `**Steps:** ${wizardFlow.steps.length}`,
    `**Total Questions:** ${wizardFlow.steps.reduce((sum: number, step: any) => sum + (step.questions?.length || 0), 0)}`,
    '',
    '### Wizard Steps',
    ...wizardFlow.steps.map((step: any) => 
      `- **${step.title}** (${step.questions?.length || 0} questions) - ${step.category || 'Uncategorized'}`
    ),
    '',
    '## Next Steps',
    '',
    '1. Review and customize the generated wizard flow',
    '2. Execute the PostgreSQL schema to create wizard tables',
    '3. Integrate wizard components with the backend API',
    '4. Test the complete wizard flow',
    '5. Add wizard to the main application navigation',
  ];
  
  await fs.promises.writeFile(reportPath, report.join('\n'));
  console.log(`📄 Generated analysis report: ${reportPath}`);
}

// ================= Main Function =================

async function main() {
  console.log('🧙 ComplianceMax Wizard Processor');
  console.log('==================================');
  
  try {
    // Find wizard files
    console.log('🔍 Finding wizard components...');
    const wizardFiles = findWizardFiles();
    console.log(`Found ${wizardFiles.length} wizard-related files`);
    
    // Process wizard directory
    const wizardDir = path.resolve(process.cwd(), 'wizard');
    const { components, layouts, flows } = await processWizardComponents(wizardDir);
    
    console.log(`📦 Found ${components.length} components, ${layouts.length} layouts, ${flows.length} flows`);
    
    // Generate outputs
    const outputDir = path.resolve(process.cwd(), 'wizard-output');
    await generateWizardOutputs(components, layouts, flows, outputDir);
    
    console.log('\n✅ Wizard processing complete!');
    console.log(`📁 Outputs saved to: ${outputDir}`);
    console.log('');
    console.log('Generated files:');
    console.log('- fema-compliance-wizard-flow.json (Complete wizard definition)');
    console.log('- wizard-types.ts (TypeScript type definitions)');
    console.log('- wizard-schema.sql (PostgreSQL database schema)');
    console.log('- wizard-analysis.md (Analysis report)');
    
  } catch (error) {
    console.error('❌ Wizard processing failed:', error.message);
    if (process.env.DEBUG) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
} 
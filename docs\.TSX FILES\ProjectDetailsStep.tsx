
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MapPin, DollarSign, AlertTriangle } from 'lucide-react'

interface ProjectDetails {
  name: string
  description: string
  location: string
  disasterType: string
  estimatedCost: number
}

interface ProjectDetailsStepProps {
  data: ProjectDetails | null
  onUpdate: (data: ProjectDetails) => void
}

const disasterTypes = [
  'Hurricane',
  'Tornado',
  'Flood',
  'Wildfire',
  'Earthquake',
  'Winter Storm',
  'Severe Storm',
  'Other'
]

export function ProjectDetailsStep({ data, onUpdate }: ProjectDetailsStepProps) {
  const [formData, setFormData] = useState<ProjectDetails>({
    name: '',
    description: '',
    location: '',
    disasterType: '',
    estimatedCost: 0,
    ...data
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (data) {
      setFormData(data)
    }
  }, [data])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Project name is required'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Project description is required'
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Project location is required'
    }

    if (!formData.disasterType) {
      newErrors.disasterType = 'Disaster type is required'
    }

    if (formData.estimatedCost <= 0) {
      newErrors.estimatedCost = 'Estimated cost must be greater than 0'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof ProjectDetails, value: string | number) => {
    const updatedData = { ...formData, [field]: value }
    setFormData(updatedData)
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Auto-save valid data
    if (validateForm()) {
      onUpdate(updatedData)
    }
  }

  const handleSave = () => {
    if (validateForm()) {
      onUpdate(formData)
    }
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Project Name */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            Project Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-3 py-2 bg-white/10 border rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-white/20'
            }`}
            placeholder="Enter project name"
          />
          {errors.name && (
            <p className="text-red-400 text-xs mt-1">{errors.name}</p>
          )}
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            <MapPin className="w-4 h-4 inline mr-1" />
            Location *
          </label>
          <input
            type="text"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className={`w-full px-3 py-2 bg-white/10 border rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.location ? 'border-red-500' : 'border-white/20'
            }`}
            placeholder="City, State"
          />
          {errors.location && (
            <p className="text-red-400 text-xs mt-1">{errors.location}</p>
          )}
        </div>

        {/* Disaster Type */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            <AlertTriangle className="w-4 h-4 inline mr-1" />
            Disaster Type *
          </label>
          <select
            value={formData.disasterType}
            onChange={(e) => handleInputChange('disasterType', e.target.value)}
            className={`w-full px-3 py-2 bg-white/10 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.disasterType ? 'border-red-500' : 'border-white/20'
            }`}
          >
            <option value="" className="bg-gray-800">Select disaster type</option>
            {disasterTypes.map(type => (
              <option key={type} value={type} className="bg-gray-800">
                {type}
              </option>
            ))}
          </select>
          {errors.disasterType && (
            <p className="text-red-400 text-xs mt-1">{errors.disasterType}</p>
          )}
        </div>

        {/* Estimated Cost */}
        <div>
          <label className="block text-sm font-medium text-white mb-2">
            <DollarSign className="w-4 h-4 inline mr-1" />
            Estimated Cost *
          </label>
          <input
            type="number"
            value={formData.estimatedCost || ''}
            onChange={(e) => handleInputChange('estimatedCost', parseFloat(e.target.value) || 0)}
            className={`w-full px-3 py-2 bg-white/10 border rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.estimatedCost ? 'border-red-500' : 'border-white/20'
            }`}
            placeholder="0.00"
            min="0"
            step="0.01"
          />
          {errors.estimatedCost && (
            <p className="text-red-400 text-xs mt-1">{errors.estimatedCost}</p>
          )}
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-white mb-2">
          Project Description *
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={4}
          className={`w-full px-3 py-2 bg-white/10 border rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
            errors.description ? 'border-red-500' : 'border-white/20'
          }`}
          placeholder="Describe the project scope, objectives, and any relevant details..."
        />
        {errors.description && (
          <p className="text-red-400 text-xs mt-1">{errors.description}</p>
        )}
      </div>

      {/* Information Card */}
      <Card className="bg-blue-500/10 border-blue-400/20">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">i</span>
            </div>
            <div>
              <h4 className="text-white font-medium mb-1">Project Information Guidelines</h4>
              <p className="text-white/70 text-sm">
                Provide accurate project details as they will be used for compliance assessment. 
                The estimated cost should include all project-related expenses for proper FEMA PA categorization.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={Object.keys(errors).length > 0}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Save Project Details
        </Button>
      </div>
    </div>
  )
}

// Dummy data for the FEMA Public Assistance ComplianceMax application

// Dashboard data
export const complianceStats = {
  approved: 42,
  pending: 15,
  rejected: 8,
  total: 65,
};

export const recentActivities = [
  {
    id: 1,
    action: "Project status updated",
    project: "Hurricane Relief Infrastructure",
    user: "<PERSON>",
    timestamp: new Date(2023, 6, 15, 14, 30),
    status: "Approved",
  },
  {
    id: 2,
    action: "Document uploaded",
    project: "Flood Mitigation Program",
    user: "<PERSON>",
    timestamp: new Date(2023, 6, 15, 11, 45),
    document: "Damage Assessment Report v2.1",
  },
  {
    id: 3,
    action: "Request for Information (RFI) submitted",
    project: "Emergency Protective Measures",
    user: "<PERSON>",
    timestamp: new Date(2023, 6, 14, 16, 20),
    task: "Additional documentation for labor costs",
  },
  {
    id: 4,
    action: "Eligibility review completed",
    project: "Debris Removal Project",
    user: "<PERSON>",
    timestamp: new Date(2023, 6, 14, 10, 15),
    result: "3 critical findings",
  },
  {
    id: 5,
    action: "New compliance requirement added",
    project: "Public Building Restoration",
    user: "<PERSON>",
    timestamp: new Date(2023, 6, 13, 15, 30),
    requirement: "Quarterly progress reporting",
  },
];

export const complianceTrends = [
  { month: "Jan", approved: 30, pending: 10, rejected: 5 },
  { month: "Feb", approved: 32, pending: 12, rejected: 6 },
  { month: "Mar", approved: 35, pending: 10, rejected: 5 },
  { month: "Apr", approved: 38, pending: 8, rejected: 4 },
  { month: "May", approved: 40, pending: 10, rejected: 5 },
  { month: "Jun", approved: 42, pending: 15, rejected: 8 },
];

export const fundingByCategory = [
  { category: "Category A - Debris Removal", approved: 2500000, pending: 500000, rejected: 100000 },
  { category: "Category B - Emergency Protective Measures", approved: 3500000, pending: 750000, rejected: 250000 },
  { category: "Category C - Roads and Bridges", approved: 4200000, pending: 1200000, rejected: 300000 },
  { category: "Category D - Water Control Facilities", approved: 1800000, pending: 600000, rejected: 150000 },
  { category: "Category E - Buildings and Equipment", approved: 5100000, pending: 900000, rejected: 200000 },
  { category: "Category F - Utilities", approved: 3200000, pending: 800000, rejected: 180000 },
  { category: "Category G - Parks & Recreation", approved: 1500000, pending: 400000, rejected: 120000 },
];

// Projects data
export const projects = [
  {
    id: "PRJ001",
    name: "Hurricane Relief Infrastructure",
    description: "Repair and restoration of critical infrastructure damaged by Hurricane Maria",
    status: "Approved",
    progress: 100,
    dueDate: new Date(2023, 8, 30),
    priority: "High",
    category: "Category C - Roads and Bridges",
    owner: "Alex Johnson",
    fundingAmount: 3750000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK001",
        name: "Damage inventory assessment",
        status: "Completed",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 6, 15),
      },
      {
        id: "TSK002",
        name: "Cost estimation documentation",
        status: "Completed",
        assignee: "James Taylor",
        dueDate: new Date(2023, 7, 1),
      },
      {
        id: "TSK003",
        name: "Environmental compliance review",
        status: "Completed",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 7, 15),
      },
      {
        id: "TSK004",
        name: "Final project submission",
        status: "Completed",
        assignee: "David Kim",
        dueDate: new Date(2023, 8, 1),
      },
    ],
    documents: [
      {
        id: "DOC001",
        name: "Damage Assessment Report",
        type: "PDF",
        uploadedBy: "Alex Johnson",
        uploadDate: new Date(2023, 5, 10),
        size: "2.4 MB",
      },
      {
        id: "DOC002",
        name: "Cost Estimation Worksheet",
        type: "XLSX",
        uploadedBy: "James Taylor",
        uploadDate: new Date(2023, 7, 5),
        size: "1.2 MB",
      },
      {
        id: "DOC003",
        name: "Environmental Compliance Certification",
        type: "PDF",
        uploadedBy: "Maria Garcia",
        uploadDate: new Date(2023, 7, 20),
        size: "0.8 MB",
      },
    ],
    team: [
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Damage Assessment Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR003",
        name: "James Taylor",
        role: "Cost Estimator",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Environmental Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
    ],
  },
  {
    id: "PRJ002",
    name: "Flood Mitigation Program",
    description: "Implementation of flood mitigation measures in high-risk areas",
    status: "In Progress",
    progress: 75,
    dueDate: new Date(2023, 10, 15),
    priority: "High",
    category: "Category D - Water Control Facilities",
    owner: "David Kim",
    fundingAmount: 2100000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK005",
        name: "Hydrological assessment",
        status: "Completed",
        assignee: "David Kim",
        dueDate: new Date(2023, 6, 30),
      },
      {
        id: "TSK006",
        name: "Engineering design documentation",
        status: "Completed",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 7, 30),
      },
      {
        id: "TSK007",
        name: "Procurement documentation",
        status: "In Progress",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 8, 30),
      },
      {
        id: "TSK008",
        name: "Construction monitoring",
        status: "Pending",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 9, 30),
      },
    ],
    documents: [
      {
        id: "DOC004",
        name: "Hydrological Study Report",
        type: "PDF",
        uploadedBy: "David Kim",
        uploadDate: new Date(2023, 6, 15),
        size: "3.1 MB",
      },
      {
        id: "DOC005",
        name: "Engineering Design Specifications",
        type: "PDF",
        uploadedBy: "Alex Johnson",
        uploadDate: new Date(2023, 7, 25),
        size: "1.5 MB",
      },
    ],
    team: [
      {
        id: "USR005",
        name: "David Kim",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Engineering Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Procurement Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Construction Manager",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
    ],
  },
  {
    id: "PRJ003",
    name: "Emergency Protective Measures",
    description: "Implementation of emergency protective measures during disaster response",
    status: "Pending",
    progress: 60,
    dueDate: new Date(2023, 9, 30),
    priority: "Critical",
    category: "Category B - Emergency Protective Measures",
    owner: "Sarah Williams",
    fundingAmount: 1850000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK009",
        name: "Emergency response documentation",
        status: "Completed",
        assignee: "James Taylor",
        dueDate: new Date(2023, 7, 15),
      },
      {
        id: "TSK010",
        name: "Force account labor records",
        status: "Completed",
        assignee: "David Kim",
        dueDate: new Date(2023, 8, 1),
      },
      {
        id: "TSK011",
        name: "Equipment usage logs",
        status: "Pending",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 8, 15),
      },
      {
        id: "TSK012",
        name: "Final cost reconciliation",
        status: "In Progress",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 9, 1),
      },
    ],
    documents: [
      {
        id: "DOC006",
        name: "Emergency Response Plan",
        type: "PDF",
        uploadedBy: "David Kim",
        uploadDate: new Date(2023, 8, 5),
        size: "4.2 MB",
      },
      {
        id: "DOC007",
        name: "Force Account Labor Records",
        type: "XLSX",
        uploadedBy: "James Taylor",
        uploadDate: new Date(2023, 7, 20),
        size: "1.8 MB",
      },
    ],
    team: [
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
      {
        id: "USR003",
        name: "James Taylor",
        role: "Emergency Response Coordinator",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
      {
        id: "USR005",
        name: "David Kim",
        role: "Documentation Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Cost Tracking Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
    ],
  },
  {
    id: "PRJ004",
    name: "Debris Removal Project",
    description: "Coordinating and documenting debris removal operations post-disaster",
    status: "In Progress",
    progress: 40,
    dueDate: new Date(2023, 11, 15),
    priority: "High",
    category: "Category A - Debris Removal",
    owner: "James Taylor",
    fundingAmount: 1250000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK013",
        name: "Debris estimation assessment",
        status: "Completed",
        assignee: "David Kim",
        dueDate: new Date(2023, 8, 15),
      },
      {
        id: "TSK014",
        name: "Contractor documentation",
        status: "In Progress",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 9, 15),
      },
      {
        id: "TSK015",
        name: "Disposal site documentation",
        status: "In Progress",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 10, 15),
      },
      {
        id: "TSK016",
        name: "Final monitoring report",
        status: "Pending",
        assignee: "James Taylor",
        dueDate: new Date(2023, 11, 1),
      },
    ],
    documents: [
      {
        id: "DOC008",
        name: "Debris Estimation Report",
        type: "PDF",
        uploadedBy: "David Kim",
        uploadDate: new Date(2023, 8, 20),
        size: "3.5 MB",
      },
      {
        id: "DOC009",
        name: "Contractor Invoices",
        type: "PDF",
        uploadedBy: "Alex Johnson",
        uploadDate: new Date(2023, 9, 10),
        size: "2.1 MB",
      },
    ],
    team: [
      {
        id: "USR003",
        name: "James Taylor",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
      {
        id: "USR005",
        name: "David Kim",
        role: "Debris Assessment Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Contractor Liaison",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Environmental Compliance",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
    ],
  },
  {
    id: "PRJ005",
    name: "Public Building Restoration",
    description: "Restoration of damaged public buildings to pre-disaster condition",
    status: "Rejected",
    progress: 25,
    dueDate: new Date(2023, 12, 31),
    priority: "Critical",
    category: "Category E - Buildings and Equipment",
    owner: "Maria Garcia",
    fundingAmount: 4200000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK017",
        name: "Damage assessment documentation",
        status: "Completed",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 9, 1),
      },
      {
        id: "TSK018",
        name: "Cost estimation",
        status: "In Progress",
        assignee: "David Kim",
        dueDate: new Date(2023, 10, 1),
      },
      {
        id: "TSK019",
        name: "Architectural plans submission",
        status: "Not Started",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 11, 1),
      },
      {
        id: "TSK020",
        name: "Historic preservation review",
        status: "Not Started",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 12, 1),
      },
    ],
    documents: [
      {
        id: "DOC010",
        name: "Building Damage Assessment",
        type: "PDF",
        uploadedBy: "Maria Garcia",
        uploadDate: new Date(2023, 9, 5),
        size: "1.7 MB",
      },
    ],
    team: [
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR005",
        name: "David Kim",
        role: "Cost Estimator",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Architectural Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Historic Preservation Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
    ],
  },
  {
    id: "PRJ006",
    name: "Utility System Repairs",
    description: "Repair and restoration of damaged utility systems",
    status: "In Progress",
    progress: 50,
    dueDate: new Date(2023, 11, 30),
    priority: "Medium",
    category: "Category F - Utilities",
    owner: "James Taylor",
    fundingAmount: 2850000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK021",
        name: "System damage assessment",
        status: "Completed",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 8, 30),
      },
      {
        id: "TSK022",
        name: "Engineering analysis",
        status: "Completed",
        assignee: "James Taylor",
        dueDate: new Date(2023, 9, 15),
      },
      {
        id: "TSK023",
        name: "Repair cost documentation",
        status: "In Progress",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 10, 15),
      },
      {
        id: "TSK024",
        name: "Environmental compliance",
        status: "Not Started",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 11, 15),
      },
    ],
    documents: [
      {
        id: "DOC011",
        name: "Utility System Damage Assessment",
        type: "PDF",
        uploadedBy: "Maria Garcia",
        uploadDate: new Date(2023, 8, 25),
        size: "2.3 MB",
      },
      {
        id: "DOC012",
        name: "Engineering Analysis Report",
        type: "PDF",
        uploadedBy: "James Taylor",
        uploadDate: new Date(2023, 9, 20),
        size: "1.1 MB",
      },
    ],
    team: [
      {
        id: "USR003",
        name: "James Taylor",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Damage Assessment Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Cost Documentation Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Environmental Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
    ],
  },
  {
    id: "PRJ007",
    name: "Park Restoration Project",
    description: "Restoration of damaged recreational facilities and parks",
    status: "Pending",
    progress: 10,
    dueDate: new Date(2024, 2, 31),
    priority: "Medium",
    category: "Category G - Parks & Recreation",
    owner: "David Kim",
    fundingAmount: 950000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK025",
        name: "Facility damage assessment",
        status: "In Progress",
        assignee: "David Kim",
        dueDate: new Date(2023, 10, 31),
      },
      {
        id: "TSK026",
        name: "Cost estimation",
        status: "Not Started",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 11, 30),
      },
      {
        id: "TSK027",
        name: "Environmental review",
        status: "Not Started",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 12, 31),
      },
      {
        id: "TSK028",
        name: "Project implementation plan",
        status: "Not Started",
        assignee: "James Taylor",
        dueDate: new Date(2024, 1, 31),
      },
    ],
    documents: [
      {
        id: "DOC013",
        name: "Park Facilities Damage Assessment",
        type: "PDF",
        uploadedBy: "David Kim",
        uploadDate: new Date(2023, 9, 15),
        size: "2.8 MB",
      },
    ],
    team: [
      {
        id: "USR005",
        name: "David Kim",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Cost Estimation Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Environmental Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR003",
        name: "James Taylor",
        role: "Implementation Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
    ],
  },
  {
    id: "PRJ008",
    name: "Bridge Replacement Project",
    description: "Replacement of critically damaged bridge infrastructure",
    status: "In Progress",
    progress: 65,
    dueDate: new Date(2023, 10, 31),
    priority: "High",
    category: "Category C - Roads and Bridges",
    owner: "Maria Garcia",
    fundingAmount: 5200000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK029",
        name: "Structural assessment",
        status: "Completed",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 8, 15),
      },
      {
        id: "TSK030",
        name: "Engineering design",
        status: "Completed",
        assignee: "David Kim",
        dueDate: new Date(2023, 9, 15),
      },
      {
        id: "TSK031",
        name: "Environmental permitting",
        status: "In Progress",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 10, 15),
      },
      {
        id: "TSK032",
        name: "Procurement documentation",
        status: "Not Started",
        assignee: "James Taylor",
        dueDate: new Date(2023, 10, 25),
      },
    ],
    documents: [
      {
        id: "DOC014",
        name: "Bridge Structural Assessment",
        type: "PDF",
        uploadedBy: "Maria Garcia",
        uploadDate: new Date(2023, 8, 20),
        size: "1.5 MB",
      },
      {
        id: "DOC015",
        name: "Engineering Design Plans",
        type: "PDF",
        uploadedBy: "David Kim",
        uploadDate: new Date(2023, 9, 25),
        size: "3.2 MB",
      },
    ],
    team: [
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR005",
        name: "David Kim",
        role: "Engineering Lead",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Environmental Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
      {
        id: "USR003",
        name: "James Taylor",
        role: "Procurement Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
    ],
  },
  {
    id: "PRJ009",
    name: "Emergency Communications System",
    description: "Restoration of emergency communications infrastructure",
    status: "Approved",
    progress: 100,
    dueDate: new Date(2023, 7, 31),
    priority: "Medium",
    category: "Category F - Utilities",
    owner: "Sarah Williams",
    fundingAmount: 1650000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK033",
        name: "System damage assessment",
        status: "Completed",
        assignee: "Sarah Williams",
        dueDate: new Date(2023, 5, 31),
      },
      {
        id: "TSK034",
        name: "Equipment specification",
        status: "Completed",
        assignee: "David Kim",
        dueDate: new Date(2023, 6, 15),
      },
      {
        id: "TSK035",
        name: "Procurement documentation",
        status: "Completed",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 7, 15),
      },
      {
        id: "TSK036",
        name: "Installation verification",
        status: "Completed",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 7, 31),
      },
    ],
    documents: [
      {
        id: "DOC016",
        name: "Communications System Assessment",
        type: "PDF",
        uploadedBy: "Sarah Williams",
        uploadDate: new Date(2023, 5, 25),
        size: "4.5 MB",
      },
      {
        id: "DOC017",
        name: "Equipment Installation Report",
        type: "PDF",
        uploadedBy: "Sarah Williams",
        uploadDate: new Date(2023, 8, 5),
        size: "2.1 MB",
      },
    ],
    team: [
      {
        id: "USR004",
        name: "Sarah Williams",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/sarah.png",
      },
      {
        id: "USR005",
        name: "David Kim",
        role: "Technical Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Procurement Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Quality Assurance",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
    ],
  },
  {
    id: "PRJ010",
    name: "Hazard Mitigation Measures",
    description: "Implementation of hazard mitigation measures to reduce future disaster impacts",
    status: "Pending",
    progress: 45,
    dueDate: new Date(2023, 11, 15),
    priority: "High",
    category: "Category B - Emergency Protective Measures",
    owner: "Alex Johnson",
    fundingAmount: 3250000,
    disasterDeclaration: "DR-4339-PR",
    tasks: [
      {
        id: "TSK037",
        name: "Risk assessment",
        status: "Completed",
        assignee: "Alex Johnson",
        dueDate: new Date(2023, 8, 31),
      },
      {
        id: "TSK038",
        name: "Mitigation strategy development",
        status: "Completed",
        assignee: "Maria Garcia",
        dueDate: new Date(2023, 9, 30),
      },
      {
        id: "TSK039",
        name: "Benefit-cost analysis",
        status: "Pending",
        assignee: "David Kim",
        dueDate: new Date(2023, 10, 31),
      },
      {
        id: "TSK040",
        name: "Implementation plan",
        status: "Not Started",
        assignee: "James Taylor",
        dueDate: new Date(2023, 11, 15),
      },
    ],
    documents: [
      {
        id: "DOC018",
        name: "Hazard Risk Assessment",
        type: "PDF",
        uploadedBy: "Alex Johnson",
        uploadDate: new Date(2023, 9, 5),
        size: "1.8 MB",
      },
      {
        id: "DOC019",
        name: "Mitigation Strategy Document",
        type: "DOCX",
        uploadedBy: "Maria Garcia",
        uploadDate: new Date(2023, 10, 5),
        size: "1.2 MB",
      },
    ],
    team: [
      {
        id: "USR001",
        name: "Alex Johnson",
        role: "Project Lead",
        email: "<EMAIL>",
        avatar: "/avatars/alex.png",
      },
      {
        id: "USR002",
        name: "Maria Garcia",
        role: "Strategy Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/maria.png",
      },
      {
        id: "USR005",
        name: "David Kim",
        role: "Financial Analyst",
        email: "<EMAIL>",
        avatar: "/avatars/david.png",
      },
      {
        id: "USR003",
        name: "James Taylor",
        role: "Implementation Specialist",
        email: "<EMAIL>",
        avatar: "/avatars/james.png",
      },
    ],
  },
];

// Document Management data
export const documentCategories = [
  { id: "CAT001", name: "Damage Assessments" },
  { id: "CAT002", name: "Cost Estimates" },
  { id: "CAT003", name: "Environmental Compliance" },
  { id: "CAT004", name: "Procurement Documentation" },
  { id: "CAT005", name: "Engineering Reports" },
  { id: "CAT006", name: "Financial Documentation" },
  { id: "CAT007", name: "Project Worksheets" },
  { id: "CAT008", name: "Closeout Documentation" },
];

export const documentTags = [
  { id: "TAG001", name: "Category A" },
  { id: "TAG002", name: "Category B" },
  { id: "TAG003", name: "Category C" },
  { id: "TAG004", name: "Category D" },
  { id: "TAG005", name: "Category E" },
  { id: "TAG006", name: "Category F" },
  { id: "TAG007", name: "Category G" },
  { id: "TAG008", name: "DR-4339-PR" },
  { id: "TAG009", name: "Approved" },
  { id: "TAG010", name: "Pending" },
];

export const documents = [
  {
    id: "DOC001",
    name: "Project Worksheet - Hurricane Relief Infrastructure",
    description: "Official FEMA project worksheet for infrastructure repairs",
    category: "Project Worksheets",
    tags: ["Category C", "DR-4339-PR"],
    version: "2.1",
    uploadedBy: "David Kim",
    uploadDate: new Date(2023, 6, 15),
    lastModified: new Date(2023, 6, 15),
    size: "1.5 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC002",
    name: "Damage Assessment Report - Public Buildings",
    description: "Comprehensive assessment of damage to public buildings",
    category: "Damage Assessments",
    tags: ["Category E", "DR-4339-PR"],
    version: "3.0",
    uploadedBy: "Maria Garcia",
    uploadDate: new Date(2023, 5, 10),
    lastModified: new Date(2023, 5, 10),
    size: "1.2 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC003",
    name: "Emergency Protective Measures Documentation",
    description: "Documentation of emergency measures taken during disaster response",
    category: "Project Worksheets",
    tags: ["Category B", "DR-4339-PR"],
    version: "1.5",
    uploadedBy: "Alex Johnson",
    uploadDate: new Date(2023, 4, 20),
    lastModified: new Date(2023, 4, 20),
    size: "2.1 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC004",
    name: "Environmental Compliance Report - Bridge Replacement",
    description: "Environmental compliance documentation for bridge replacement project",
    category: "Environmental Compliance",
    tags: ["Category C", "DR-4339-PR"],
    version: "1.0",
    uploadedBy: "Sarah Williams",
    uploadDate: new Date(2023, 3, 15),
    lastModified: new Date(2023, 3, 15),
    size: "3.5 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC005",
    name: "Cost Estimation Worksheet - Utility Repairs",
    description: "Detailed cost estimates for utility system repairs",
    category: "Cost Estimates",
    tags: ["Category F", "DR-4339-PR"],
    version: "1.0",
    uploadedBy: "James Taylor",
    uploadDate: new Date(2023, 2, 28),
    lastModified: new Date(2023, 2, 28),
    size: "4.2 MB",
    type: "XLSX",
    status: "Approved",
  },
  {
    id: "DOC006",
    name: "Procurement Documentation - Debris Removal",
    description: "Documentation of procurement process for debris removal contractors",
    category: "Procurement Documentation",
    tags: ["Category A", "DR-4339-PR"],
    version: "2.2",
    uploadedBy: "Sarah Williams",
    uploadDate: new Date(2023, 1, 15),
    lastModified: new Date(2023, 1, 15),
    size: "5.1 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC007",
    name: "Engineering Report - Water Control Facilities",
    description: "Engineering assessment of damaged water control facilities",
    category: "Engineering Reports",
    tags: ["Category D", "DR-4339-PR"],
    version: "1.3",
    uploadedBy: "Maria Garcia",
    uploadDate: new Date(2023, 0, 20),
    lastModified: new Date(2023, 0, 20),
    size: "2.8 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC008",
    name: "Force Account Labor Records",
    description: "Documentation of force account labor for emergency response",
    category: "Financial Documentation",
    tags: ["Category B", "DR-4339-PR"],
    version: "1.1",
    uploadedBy: "Alex Johnson",
    uploadDate: new Date(2022, 11, 10),
    lastModified: new Date(2022, 11, 10),
    size: "1.3 MB",
    type: "XLSX",
    status: "Approved",
  },
  {
    id: "DOC009",
    name: "Hazard Mitigation Proposal",
    description: "Proposal for hazard mitigation measures to reduce future impacts",
    category: "Project Worksheets",
    tags: ["Category B", "DR-4339-PR"],
    version: "2.0",
    uploadedBy: "David Kim",
    uploadDate: new Date(2022, 10, 15),
    lastModified: new Date(2022, 10, 15),
    size: "0.9 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC010",
    name: "Debris Monitoring Report",
    description: "Documentation of debris monitoring activities",
    category: "Financial Documentation",
    tags: ["Category A", "DR-4339-PR"],
    version: "1.0",
    uploadedBy: "James Taylor",
    uploadDate: new Date(2022, 9, 20),
    lastModified: new Date(2022, 9, 20),
    size: "2.4 MB",
    type: "PDF",
    status: "Draft",
  },
  {
    id: "DOC011",
    name: "Request for Public Assistance Form",
    description: "Initial request for FEMA Public Assistance",
    category: "Project Worksheets",
    tags: ["DR-4339-PR", "Approved"],
    version: "1.2",
    uploadedBy: "Maria Garcia",
    uploadDate: new Date(2022, 8, 5),
    lastModified: new Date(2022, 8, 5),
    size: "1.7 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC012",
    name: "Benefit-Cost Analysis - Hazard Mitigation",
    description: "Analysis of benefits versus costs for hazard mitigation measures",
    category: "Financial Documentation",
    tags: ["Category B", "DR-4339-PR"],
    version: "1.0",
    uploadedBy: "David Kim",
    uploadDate: new Date(2022, 7, 15),
    lastModified: new Date(2022, 7, 15),
    size: "3.2 MB",
    type: "XLSX",
    status: "Approved",
  },
  {
    id: "DOC013",
    name: "Preliminary Damage Assessment",
    description: "Initial assessment of disaster damages",
    category: "Damage Assessments",
    tags: ["DR-4339-PR", "Approved"],
    version: "1.0",
    uploadedBy: "Sarah Williams",
    uploadDate: new Date(2022, 6, 10),
    lastModified: new Date(2022, 6, 10),
    size: "2.9 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC014",
    name: "Project Completion and Certification Report",
    description: "Final certification of project completion",
    category: "Closeout Documentation",
    tags: ["Category F", "DR-4339-PR", "Approved"],
    version: "2.3",
    uploadedBy: "Alex Johnson",
    uploadDate: new Date(2022, 5, 20),
    lastModified: new Date(2022, 5, 20),
    size: "1.1 MB",
    type: "PDF",
    status: "Approved",
  },
  {
    id: "DOC015",
    name: "Quarterly Progress Report",
    description: "Quarterly update on project progress",
    category: "Financial Documentation",
    tags: ["Category C", "DR-4339-PR", "Pending"],
    version: "1.0",
    uploadedBy: "James Taylor",
    uploadDate: new Date(2022, 4, 15),
    lastModified: new Date(2022, 4, 15),
    size: "1.8 MB",
    type: "PDF",
    status: "Approved",
  },
];

// Compliance Wizard data
export const femaCategories = [
  { id: "CAT001", name: "Category A - Debris Removal", description: "Clearance of debris from public roads and spaces" },
  { id: "CAT002", name: "Category B - Emergency Protective Measures", description: "Actions taken to save lives and protect property" },
  { id: "CAT003", name: "Category C - Roads and Bridges", description: "Repair of roads, bridges, and associated features" },
  { id: "CAT004", name: "Category D - Water Control Facilities", description: "Repair of dams, levees, drainage channels, and similar facilities" },
  { id: "CAT005", name: "Category E - Buildings and Equipment", description: "Repair or replacement of public buildings and equipment" },
  { id: "CAT006", name: "Category F - Utilities", description: "Repair of water treatment plants, power generation facilities, and similar utilities" },
  { id: "CAT007", name: "Category G - Parks & Recreation", description: "Repair of parks, recreational areas, and other facilities" },
];

export const complianceQuestions = {
  general: [
    {
      id: "Q001",
      question: "What type of applicant are you?",
      options: [
        "State Government",
        "Local Government",
        "Tribal Government",
        "Private Non-Profit Organization",
        "Other",
      ],
    },
    {
      id: "Q002",
      question: "What is the disaster declaration number for your project?",
      options: [
        "DR-4339-PR",
        "DR-4340-VI",
        "DR-4335-VI",
        "DR-4336-PR",
        "DR-4337-FL",
        "DR-4338-TX",
        "Other/Not Sure",
      ],
    },
    {
      id: "Q003",
      question: "Which FEMA Public Assistance categories apply to your project?",
      options: [
        "Category A - Debris Removal",
        "Category B - Emergency Protective Measures",
        "Category C - Roads and Bridges",
        "Category D - Water Control Facilities",
        "Category E - Buildings and Equipment",
        "Category F - Utilities",
        "Category G - Parks & Recreation",
      ],
      multiSelect: true,
    },
    {
      id: "Q004",
      question: "What is the estimated cost of your project?",
      options: [
        "Less than $131,100 (Small Project)",
        "$131,100 - $1,000,000",
        "$1,000,001 - $5,000,000",
        "$5,000,001 - $10,000,000",
        "More than $10,000,000",
      ],
    },
  ],
  documentation: [
    {
      id: "Q005",
      question: "Do you have detailed damage documentation?",
      options: [
        "Yes, comprehensive documentation with photos and assessments",
        "Yes, but documentation is partial",
        "Limited documentation available",
        "No documentation yet",
        "Not sure what documentation is needed",
      ],
    },
    {
      id: "Q006",
      question: "Do you have cost documentation (invoices, labor records, etc.)?",
      options: [
        "Yes, comprehensive and organized",
        "Yes, but not fully organized",
        "Partial documentation available",
        "No documentation yet",
        "Not sure what documentation is needed",
      ],
    },
    {
      id: "Q007",
      question: "Do you have procurement documentation for contracts?",
      options: [
        "Yes, followed all federal procurement requirements",
        "Yes, followed some procurement requirements",
        "Limited procurement documentation",
        "No procurement documentation",
        "Not applicable (no contracts)",
      ],
    },
  ],
  compliance: [
    {
      id: "Q008",
      question: "Have you completed environmental and historic preservation reviews?",
      options: [
        "Yes, all reviews completed and documented",
        "Some reviews completed",
        "Reviews in progress",
        "Reviews not started",
        "Not sure if reviews are required",
      ],
    },
    {
      id: "Q009",
      question: "Have you identified and addressed insurance requirements?",
      options: [
        "Yes, all insurance documentation is complete",
        "Insurance documentation is in progress",
        "Insurance requirements identified but not addressed",
        "Insurance requirements not identified",
        "Not sure about insurance requirements",
      ],
    },
    {
      id: "Q010",
      question: "Have you addressed hazard mitigation opportunities?",
      options: [
        "Yes, hazard mitigation measures identified and documented",
        "Hazard mitigation opportunities identified but not documented",
        "Hazard mitigation assessment in progress",
        "Hazard mitigation not considered",
        "Not sure about hazard mitigation opportunities",
      ],
    },
  ],
  management: [
    {
      id: "Q011",
      question: "Do you have dedicated staff for managing FEMA Public Assistance?",
      options: [
        "Yes, dedicated team with FEMA experience",
        "Yes, dedicated staff but limited FEMA experience",
        "Staff assigned as additional duty",
        "No dedicated staff",
        "Using external consultants",
      ],
    },
    {
      id: "Q012",
      question: "How do you manage project documentation?",
      options: [
        "Centralized electronic system specific to FEMA requirements",
        "General document management system",
        "Organized file structure but not electronic",
        "Ad-hoc documentation management",
        "No systematic documentation management",
      ],
    },
    {
      id: "Q013",
      question: "How do you track project progress and deadlines?",
      options: [
        "Dedicated project management system with FEMA milestones",
        "General project management system",
        "Spreadsheet tracking",
        "Manual tracking",
        "No formal tracking system",
      ],
    },
  ],
};
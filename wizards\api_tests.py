import requests
import json
import pytest
from unittest.mock import patch, MagicMock

# Import the API endpoints
from policy_matcher_api import router, jobs

# Mock data for testing
mock_user = MagicMock()
mock_user.id = "test_user_id"
mock_user.is_admin = False

@pytest.fixture
def clear_jobs():
    """Clear the jobs dictionary before each test"""
    jobs.clear()
    yield
    jobs.clear()

@patch("policy_matcher_api.get_current_user")
def test_match_policies(mock_get_user, clear_jobs):
    """Test the match_policies endpoint"""
    mock_get_user.return_value = mock_user
    
    # Mock file upload
    mock_file = MagicMock()
    mock_file.filename = "test_requirements.pdf"
    mock_file.file.read.return_value = b"test file content"
    
    # Mock background tasks
    mock_background_tasks = MagicMock()
    
    # Call the endpoint
    response = router.match_policies(
        background_tasks=mock_background_tasks,
        requirements_file=mock_file,
        confidence_threshold=0.7,
        matching_algorithm="default",
        current_user=mock_user
    )
    
    # Verify response
    assert "id" in response
    assert response["status"] == "pending"
    assert response["file_name"] == "test_requirements.pdf"
    
    # Verify background task was added
    mock_background_tasks.add_task.assert_called_once()
    
    # Verify job was created
    job_id = response["id"]
    assert job_id in jobs
    assert jobs[job_id]["status"] == "pending"
    assert jobs[job_id]["user_id"] == str(mock_user.id)

@patch("policy_matcher_api.get_current_user")
def test_get_job_status(mock_get_user, clear_jobs):
    """Test the get_job_status endpoint"""
    mock_get_user.return_value = mock_user
    
    # Create a test job
    job_id = "test_job_id"
    jobs[job_id] = {
        "id": job_id,
        "status": "processing",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test_requirements.pdf",
        "user_id": str(mock_user.id)
    }
    
    # Call the endpoint
    response = router.get_job_status(job_id=job_id, current_user=mock_user)
    
    # Verify response
    assert response["id"] == job_id
    assert response["status"] == "processing"
    assert response["file_name"] == "test_requirements.pdf"

@patch("policy_matcher_api.get_current_user")
def test_get_job_results(mock_get_user, clear_jobs):
    """Test the get_job_results endpoint"""
    mock_get_user.return_value = mock_user
    
    # Create a test job with results
    job_id = "test_job_id"
    test_results = [
        {"id": 1, "requirement": "Test requirement", "policy": "Test policy", "section": "1.1", "confidence": 0.85}
    ]
    jobs[job_id] = {
        "id": job_id,
        "status": "completed",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test_requirements.pdf",
        "user_id": str(mock_user.id),
        "results": test_results
    }
    
    # Call the endpoint
    response = router.get_job_results(job_id=job_id, current_user=mock_user)
    
    # Verify response
    assert response["job_id"] == job_id
    assert response["results"] == test_results

@patch("policy_matcher_api.get_current_user")
def test_get_job_history(mock_get_user, clear_jobs):
    """Test the get_job_history endpoint"""
    mock_get_user.return_value = mock_user
    
    # Create test jobs
    jobs["job1"] = {
        "id": "job1",
        "status": "completed",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test1.pdf",
        "user_id": str(mock_user.id)
    }
    jobs["job2"] = {
        "id": "job2",
        "status": "processing",
        "created_at": "2025-04-13T11:00:00Z",
        "file_name": "test2.pdf",
        "user_id": str(mock_user.id)
    }
    jobs["job3"] = {
        "id": "job3",
        "status": "pending",
        "created_at": "2025-04-13T12:00:00Z",
        "file_name": "test3.pdf",
        "user_id": "another_user_id"  # Different user
    }
    
    # Call the endpoint
    response = router.get_job_history(page=1, limit=10, current_user=mock_user)
    
    # Verify response
    assert response["total"] == 2  # Only jobs for the current user
    assert len(response["jobs"]) == 2
    assert response["page"] == 1
    assert response["limit"] == 10
    
    # Verify jobs are sorted by created_at (newest first)
    assert response["jobs"][0]["id"] == "job2"
    assert response["jobs"][1]["id"] == "job1"

@patch("policy_matcher_api.get_current_user")
def test_delete_job(mock_get_user, clear_jobs):
    """Test the delete_job endpoint"""
    mock_get_user.return_value = mock_user
    
    # Create a test job
    job_id = "test_job_id"
    jobs[job_id] = {
        "id": job_id,
        "status": "completed",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test_requirements.pdf",
        "user_id": str(mock_user.id),
        "file_path": "/tmp/test_file.pdf"  # This would be a real path in production
    }
    
    # Mock os.path.exists and os.remove
    with patch("os.path.exists", return_value=True), patch("os.remove"):
        # Call the endpoint
        response = router.delete_job(job_id=job_id, current_user=mock_user)
        
        # Verify response
        assert response["message"] == "Job deleted successfully"
        
        # Verify job was deleted
        assert job_id not in jobs

@patch("policy_matcher_api.get_current_user")
def test_get_policies(mock_get_user):
    """Test the get_policies endpoint"""
    mock_get_user.return_value = mock_user
    
    # Call the endpoint
    response = router.get_policies(current_user=mock_user)
    
    # Verify response
    assert "policies" in response
    assert len(response["policies"]) > 0
    assert "id" in response["policies"][0]
    assert "name" in response["policies"][0]

# Integration tests with the refactored policy matcher
@patch("policy_matcher_api.get_current_user")
@patch("policy_matcher_api.run_policy_matcher")
def test_process_policy_matching(mock_run_policy_matcher, mock_get_user, clear_jobs):
    """Test the process_policy_matching background task"""
    mock_get_user.return_value = mock_user
    mock_run_policy_matcher.return_value = "/path/to/report.html"
    
    # Create a test job
    job_id = "test_job_id"
    jobs[job_id] = {
        "id": job_id,
        "status": "pending",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test_requirements.pdf",
        "user_id": str(mock_user.id),
        "file_path": "/tmp/test_file.pdf"
    }
    
    # Mock open function for reading/writing files
    with patch("builtins.open", MagicMock()), patch("json.dump"):
        # Call the background task
        from policy_matcher_api import process_policy_matching
        process_policy_matching(
            job_id=job_id,
            file_path="/tmp/test_file.pdf",
            policies_dir="/path/to/policies",
            confidence_threshold=0.7,
            algorithm="default"
        )
        
        # Verify job status was updated
        assert jobs[job_id]["status"] == "completed"
        assert "results" in jobs[job_id]
        
        # Verify policy matcher was called with correct parameters
        mock_run_policy_matcher.assert_called_once()
        args, kwargs = mock_run_policy_matcher.call_args
        assert kwargs["govstar_file"] == "/tmp/test_file.pdf"
        assert kwargs["policies_dir"] == "/path/to/policies"
        assert "output_file" in kwargs
        assert "config" in kwargs

# Error handling tests
@patch("policy_matcher_api.get_current_user")
def test_job_not_found(mock_get_user):
    """Test error handling when job is not found"""
    mock_get_user.return_value = mock_user
    
    # Try to get status for non-existent job
    with pytest.raises(Exception) as excinfo:
        router.get_job_status(job_id="non_existent_job", current_user=mock_user)
    
    assert "Job not found" in str(excinfo.value)

@patch("policy_matcher_api.get_current_user")
def test_unauthorized_access(mock_get_user, clear_jobs):
    """Test error handling when user tries to access another user's job"""
    mock_get_user.return_value = mock_user
    
    # Create a job owned by another user
    job_id = "test_job_id"
    jobs[job_id] = {
        "id": job_id,
        "status": "completed",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test_requirements.pdf",
        "user_id": "another_user_id"  # Different user
    }
    
    # Try to access the job
    with pytest.raises(Exception) as excinfo:
        router.get_job_status(job_id=job_id, current_user=mock_user)
    
    assert "Not authorized" in str(excinfo.value)

@patch("policy_matcher_api.get_current_user")
def test_job_not_completed(mock_get_user, clear_jobs):
    """Test error handling when trying to get results for incomplete job"""
    mock_get_user.return_value = mock_user
    
    # Create a job that's still processing
    job_id = "test_job_id"
    jobs[job_id] = {
        "id": job_id,
        "status": "processing",
        "created_at": "2025-04-13T10:00:00Z",
        "file_name": "test_requirements.pdf",
        "user_id": str(mock_user.id)
    }
    
    # Try to get results
    with pytest.raises(Exception) as excinfo:
        router.get_job_results(job_id=job_id, current_user=mock_user)
    
    assert "Job is not completed" in str(excinfo.value)

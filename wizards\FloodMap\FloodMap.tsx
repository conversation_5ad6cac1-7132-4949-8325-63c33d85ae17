import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { getFloodMaps } from '../../services/floodMap';

const FloodMap: React.FC = () => {
    const [maps, setMaps] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchMaps = async () => {
            try {
                const response = await getFloodMaps();
                setMaps(response);
            } catch (err: any) {
                setError(err.response?.data?.detail || 'Failed to load flood maps');
            } finally {
                setLoading(false);
            }
        };
        fetchMaps();
    }, []);

    if (loading) return <CircularProgress aria-label="Loading flood maps" />;
    if (error) return <Alert severity="error" role="alert">{error}</Alert>;

    return (
        <Box sx={{ p: 3 }} role="main">
            <Typography variant="h4" gutterBottom>
                Flood Maps
            </Typography>
            {maps.length === 0 ? (
                <Typography>No flood maps found.</Typography>
            ) : (
                <Table aria-label="Flood maps table">
                    <TableHead>
                        <TableRow>
                            <TableCell>FIRM Panel ID</TableCell>
                            <TableCell>Flood Zone</TableCell>
                            <TableCell>Base Flood Elevation</TableCell>
                            <TableCell>Firmette URL</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {maps.map((map) => (
                            <TableRow key={map.id}>
                                <TableCell>{map.firm_panel_id}</TableCell>
                                <TableCell>{map.flood_zone}</TableCell>
                                <TableCell>{map.base_flood_elevation}</TableCell>
                                <TableCell>
                                    <a href={map.firmette_url} target="_blank" rel="noopener noreferrer">View Firmette</a>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            )}
        </Box>
    );
};

export default FloodMap;
import React, { useState } from 'react';
import {
  Box, Typography, Paper, Grid, Button, TextField,
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Chip, Card, CardContent, FormControl,
  InputLabel, Select, MenuItem, SelectChangeEvent,
  Dialog, DialogTitle, DialogContent, DialogActions,
  Checkbox, FormControlLabel, List, ListItem, ListItemIcon,
  ListItemText, Divider
} from '@mui/material';
import {
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
  TextSnippet as CsvIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  FormatListBulleted as TemplateIcon,
  Save as SaveIcon
} from '@mui/icons-material';

// Mock service for export functionality
const exportService = {
  exportToPdf: (data: any, options: any) => {
    console.log('Exporting to PDF', data, options);
    // In a real implementation, this would call a backend API
    return new Promise(resolve => setTimeout(resolve, 1500));
  },
  exportToExcel: (data: any, options: any) => {
    console.log('Exporting to Excel', data, options);
    return new Promise(resolve => setTimeout(resolve, 1500));
  },
  exportToCsv: (data: any, options: any) => {
    console.log('Exporting to CSV', data, options);
    return new Promise(resolve => setTimeout(resolve, 1500));
  },
  getTemplates: () => {
    // Mock templates
    return Promise.resolve([
      { id: 1, name: 'Standard Report', description: 'Default report format with all sections' },
      { id: 2, name: 'Executive Summary', description: 'Condensed report with key metrics only' },
      { id: 3, name: 'Detailed Analysis', description: 'Comprehensive report with all details' },
      { id: 4, name: 'FEMA Compliance', description: 'Formatted specifically for FEMA requirements' },
      { id: 5, name: 'Custom Template 1', description: 'User-defined custom template' }
    ]);
  }
};

interface Report {
  id: number;
  name: string;
  type: string;
  date: string;
  status: string;
}

const ExportFunctionality: React.FC = () => {
  // State for reports
  const [reports, setReports] = useState<Report[]>([
    { id: 1, name: 'Quarterly Compliance Report Q1 2025', type: 'Compliance', date: '2025-04-01', status: 'Complete' },
    { id: 2, name: 'Policy Coverage Analysis', type: 'Policy', date: '2025-03-15', status: 'Complete' },
    { id: 3, name: 'FEMA Requirements Gap Analysis', type: 'Gap Analysis', date: '2025-03-22', status: 'Complete' },
    { id: 4, name: 'Document Completeness Report', type: 'Document', date: '2025-04-05', status: 'Complete' },
    { id: 5, name: 'Annual Security Assessment', type: 'Security', date: '2025-02-28', status: 'Complete' },
  ]);
  
  // State for export options
  const [selectedReports, setSelectedReports] = useState<number[]>([]);
  const [exportFormat, setExportFormat] = useState('pdf');
  const [exportLoading, setExportLoading] = useState(false);
  
  // State for template dialog
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  
  // State for custom template dialog
  const [customTemplateDialogOpen, setCustomTemplateDialogOpen] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [templateOptions, setTemplateOptions] = useState({
    includeCoverPage: true,
    includeExecutiveSummary: true,
    includeDetailedFindings: true,
    includeCharts: true,
    includeAppendices: false,
    includeMetadata: true
  });
  
  // Load templates
  React.useEffect(() => {
    exportService.getTemplates().then(setTemplates);
  }, []);
  
  const handleReportSelect = (reportId: number) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId) 
        : [...prev, reportId]
    );
  };
  
  const handleSelectAll = () => {
    if (selectedReports.length === reports.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(reports.map(report => report.id));
    }
  };
  
  const handleExportFormatChange = (event: SelectChangeEvent) => {
    setExportFormat(event.target.value);
  };
  
  const handleExport = async () => {
    if (selectedReports.length === 0) return;
    
    setExportLoading(true);
    
    const selectedReportData = reports.filter(report => 
      selectedReports.includes(report.id)
    );
    
    try {
      const options = {
        template: selectedTemplate ? templates.find(t => t.id === selectedTemplate) : null,
        customOptions: selectedTemplate ? null : templateOptions
      };
      
      if (exportFormat === 'pdf') {
        await exportService.exportToPdf(selectedReportData, options);
      } else if (exportFormat === 'excel') {
        await exportService.exportToExcel(selectedReportData, options);
      } else if (exportFormat === 'csv') {
        await exportService.exportToCsv(selectedReportData, options);
      }
      
      // In a real app, this would trigger a download
      alert(`Export successful! ${selectedReports.length} reports exported to ${exportFormat.toUpperCase()}.`);
    } catch (error) {
      console.error('Export failed', error);
      alert('Export failed. Please try again.');
    } finally {
      setExportLoading(false);
    }
  };
  
  const handleOpenTemplateDialog = () => {
    setTemplateDialogOpen(true);
  };
  
  const handleCloseTemplateDialog = () => {
    setTemplateDialogOpen(false);
  };
  
  const handleTemplateSelect = (templateId: number) => {
    setSelectedTemplate(templateId);
    setTemplateDialogOpen(false);
  };
  
  const handleOpenCustomTemplateDialog = () => {
    setCustomTemplateDialogOpen(true);
  };
  
  const handleCloseCustomTemplateDialog = () => {
    setCustomTemplateDialogOpen(false);
  };
  
  const handleSaveCustomTemplate = () => {
    // In a real app, this would save the template to the backend
    alert(`Custom template "${templateName}" saved successfully!`);
    setCustomTemplateDialogOpen(false);
  };
  
  const handleTemplateOptionChange = (option: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setTemplateOptions({
      ...templateOptions,
      [option]: event.target.checked
    });
  };
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Export Reports
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Available Reports
              </Typography>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedReports.length === reports.length}
                    indeterminate={selectedReports.length > 0 && selectedReports.length < reports.length}
                    onChange={handleSelectAll}
                  />
                }
                label="Select All"
              />
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedReports.length === reports.length}
                        indeterminate={selectedReports.length > 0 && selectedReports.length < reports.length}
                        onChange={handleSelectAll}
                      />
                    </TableCell>
                    <TableCell>Report Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow 
                      key={report.id}
                      selected={selectedReports.includes(report.id)}
                      onClick={() => handleReportSelect(report.id)}
                      sx={{ cursor: 'pointer' }}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedReports.includes(report.id)}
                          onChange={() => handleReportSelect(report.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </TableCell>
                      <TableCell>{report.name}</TableCell>
                      <TableCell>{report.type}</TableCell>
                      <TableCell>{report.date}</TableCell>
                      <TableCell>
                        <Chip
                          label={report.status}
                          color={report.status === 'Complete' ? 'success' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Export Options
              </Typography>
              
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Export Format</InputLabel>
                <Select
                  value={exportFormat}
                  label="Export Format"
                  onChange={handleExportFormatChange}
                >
                  <MenuItem value="pdf">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PdfIcon sx={{ mr: 1, color: '#F40F02' }} />
                      PDF Document
                    </Box>
                  </MenuItem>
                  <MenuItem value="excel">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ExcelIcon sx={{ mr: 1, color: '#1D6F42' }} />
                      Excel Spreadsheet
                    </Box>
                  </MenuItem>
                  <MenuItem value="csv">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CsvIcon sx={{ mr: 1, color: '#939393' }} />
                      CSV File
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
              
              <Button
                variant="outlined"
                fullWidth
                startIcon={<TemplateIcon />}
                onClick={handleOpenTemplateDialog}
                sx={{ mb: 2 }}
              >
                {selectedTemplate 
                  ? `Template: ${templates.find(t => t.id === selectedTemplate)?.name}` 
                  : 'Select Template'}
              </Button>
              
              <Button
                variant="outlined"
                fullWidth
                startIcon={<SettingsIcon />}
                onClick={handleOpenCustomTemplateDialog}
                sx={{ mb: 3 }}
              >
                Customize Template
              </Button>
              
              <Button
                variant="contained"
                fullWidth
                startIcon={<DownloadIcon />}
                onClick={handleExport}
                disabled={selectedReports.length === 0 || exportLoading}
              >
                {exportLoading 
                  ? 'Exporting...' 
                  : `Export ${selectedReports.length} Report${selectedReports.length !== 1 ? 's' : ''}`}
              </Button>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Export Information
              </Typography>
              
              <Typography variant="body2" paragraph>
                Select one or more reports from the list and choose your preferred export format.
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom>
                Available Formats:
              </Typography>
              <Typography variant="body2" paragraph>
                • PDF - Best for sharing and printing<br />
                • Excel - Best for data analysis<br />
                • CSV - Best for data integration
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom>
                Templates:
              </Typography>
              <Typography variant="body2">
                Choose from predefined templates or customize your own to control what information is included in the exported reports.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Template Selection Dialog */}
      <Dialog open={templateDialogOpen} onClose={handleCloseTemplateDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Select Report Template</DialogTitle>
        <DialogContent>
          <List>
            {templates.map((template) => (
              <React.Fragment key={template.id}>
                <ListItem 
                  button 
                  onClick={() => handleTemplateSelect(template.id)}
                  selected={selectedTemplate === template.id}
                >
                  <ListItemIcon>
                    <TemplateIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary={template.name} 
                    secondary={template.description} 
                  />
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseTemplateDialog}>Cancel</Button>
        </DialogActions>
      </Dialog>
      
      {/* Custom Template Dialog */}
      <Dialog open={customTemplateDialogOpen} onClose={handleCloseCustomTemplateDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Customize Report Template</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Template Name"
            fullWidth
            variant="outlined"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            sx={{ mb: 3 }}
          />
          
          <Typography variant="subtitle1" gutterBottom>
            Sections to Include:
          </Typography>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={templateOptions.includeCoverPage}
                onChange={handleTemplateOptionChange('includeCoverPage')}
              />
            }
            label="Cover Page"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={templateOptions.includeExecutiveSummary}
                onChange={handleTemplateOptionChange('includeExecutiveSummary')}
              />
            }
            label="Executive Summary"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={templateOptions.includeDetailedFindings}
                onChange={handleTemplateOptionChange('includeDetailedFindings')}
              />
            }
            label="Detailed Findings"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={templateOptions.includeCharts}
                onChange={handleTemplateOptionChange('includeCharts')}
              />
            }
            label="Charts and Graphs"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={templateOptions.includeAppendices}
                onChange={handleTemplateOptionChange('includeAppendices')}
              />
            }
            label="Appendices"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={templateOptions.includeMetadata}
                onChange={handleTemplateOptionChange('includeMetadata')}
              />
            }
            label="Metadata and Report Information"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCustomTemplateDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveCustomTemplate} 
            variant="contained"
            startIcon={<SaveIcon />}
            disabled={!templateName.trim()}
          >
            Save Template
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ExportFunctionality;

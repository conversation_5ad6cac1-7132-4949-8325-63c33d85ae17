<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Work Intake - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            font-size: 20px;
            transform: scale(1.4);
            transform-origin: top left;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: grid;
            grid-template-rows: 80px 1fr 60px;
            gap: 0;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
        }
        
        .header h1 {
            color: #c0392b;
            font-size: 28px;
            font-weight: 700;
        }
        
        .badges {
            display: flex;
            gap: 15px;
        }
        
        .badge {
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .badge.emergency { background: #e74c3c; }
        .badge.categories { background: #f39c12; }
        .badge.rapid { background: #27ae60; }
        
        .main-content {
            background: white;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 0;
            overflow: hidden;
        }
        
        .column {
            padding: 25px;
            overflow-y: auto;
            border-right: 1px solid #e1e8ed;
        }
        
        .column:last-child { border-right: none; }
        
        .column h3 {
            color: #c0392b;
            font-size: 22px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #e74c3c;
            font-weight: 700;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 16px;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #e74c3c;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }
        
        .category-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            font-weight: 600;
        }
        
        .category-item:hover {
            background: #f8f9fa;
            border-color: #e74c3c;
        }
        
        .category-item.selected {
            background: #ffebee;
            border-color: #e74c3c;
        }
        
        .category-item input {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .urgency-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 10px;
            transition: all 0.2s;
            font-weight: 600;
        }
        
        .urgency-item:hover {
            background: #f8f9fa;
            border-color: #e74c3c;
        }
        
        .urgency-item.selected {
            background: #ffebee;
            border-color: #e74c3c;
        }
        
        .urgency-item input {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .urgency-high {
            background: #ffcdd2 !important;
            border-color: #d32f2f !important;
        }
        
        .urgency-critical {
            background: #ffebee !important;
            border-color: #b71c1c !important;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn-primary {
            background: #e74c3c;
            color: white;
        }
        
        .btn-primary:hover {
            background: #c0392b;
        }
        
        .footer {
            background: white;
            border-top: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }
        
        .status-text {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .info-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-box h4 {
            color: #856404;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .info-box p {
            color: #856404;
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div>
                <h1>🚨 Emergency Work Intake</h1>
            </div>
            <div class="badges">
                <span class="badge emergency">Emergency Response</span>
                <span class="badge categories">Categories A & B</span>
                <span class="badge rapid">Rapid Processing</span>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Column 1: Project Information -->
            <div class="column">
                <h3>📋 Project Information</h3>
                
                <div class="info-box">
                    <h4>Emergency Work Categories</h4>
                    <p><strong>Category A:</strong> Debris removal from improved public property<br>
                    <strong>Category B:</strong> Emergency protective measures</p>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Project Name *</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Enter project name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Emergency Work Category *</label>
                    <div class="category-grid">
                        <label class="category-item">
                            <input type="radio" name="workCategory" value="A" required>
                            Category A - Debris Removal
                        </label>
                        <label class="category-item">
                            <input type="radio" name="workCategory" value="B" required>
                            Category B - Emergency Protective Measures
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Incident Date *</label>
                    <input type="date" class="form-control" id="incidentDate" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">FEMA Disaster Declaration</label>
                    <input type="text" class="form-control" id="disasterNumber" placeholder="e.g., FEMA-4XXX-DR">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Estimated Cost *</label>
                    <input type="number" class="form-control" id="estimatedCost" placeholder="Enter cost in USD" required>
                </div>
            </div>
            
            <!-- Column 2: Emergency Details -->
            <div class="column">
                <h3>⚡ Emergency Details</h3>
                
                <div class="form-group">
                    <label class="form-label">Urgency Level *</label>
                    <div class="urgency-item">
                        <input type="radio" name="urgencyLevel" value="immediate" required>
                        🔴 Immediate (Life safety threat)
                    </div>
                    <div class="urgency-item urgency-critical">
                        <input type="radio" name="urgencyLevel" value="critical" required>
                        🚨 Critical (Infrastructure failure imminent)
                    </div>
                    <div class="urgency-item urgency-high">
                        <input type="radio" name="urgencyLevel" value="high" required>
                        🟠 High (Public health/safety risk)
                    </div>
                    <div class="urgency-item">
                        <input type="radio" name="urgencyLevel" value="medium" required>
                        🟡 Medium (Property protection)
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Emergency Description *</label>
                    <textarea class="form-control" id="emergencyDescription" rows="6" placeholder="Describe the emergency situation, immediate threats, and required actions" required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Location Details *</label>
                    <textarea class="form-control" id="locationDetails" rows="3" placeholder="Specific location, access routes, and site conditions" required></textarea>
                </div>
            </div>
            
            <!-- Column 3: Response Requirements -->
            <div class="column">
                <h3>🛠️ Response Requirements</h3>
                
                <div class="form-group">
                    <label class="form-label">Required Actions</label>
                    <div style="display: grid; gap: 8px;">
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="actions" value="debris_removal" style="margin-right: 8px;">
                            Debris removal from roadways
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="actions" value="sandbagging" style="margin-right: 8px;">
                            Sandbagging/flood protection
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="actions" value="road_closure" style="margin-right: 8px;">
                            Road closure/barricades
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="actions" value="emergency_repairs" style="margin-right: 8px;">
                            Emergency infrastructure repairs
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="actions" value="evacuation_support" style="margin-right: 8px;">
                            Evacuation route support
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="actions" value="utility_protection" style="margin-right: 8px;">
                            Utility system protection
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Resources Needed</label>
                    <div style="display: grid; gap: 8px;">
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="resources" value="heavy_equipment" style="margin-right: 8px;">
                            Heavy equipment (excavators, loaders)
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="resources" value="personnel" style="margin-right: 8px;">
                            Emergency response personnel
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="resources" value="materials" style="margin-right: 8px;">
                            Emergency materials (sand, barriers)
                        </label>
                        <label style="display: flex; align-items: center; font-size: 14px;">
                            <input type="checkbox" name="resources" value="contractors" style="margin-right: 8px;">
                            Emergency contractors
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Timeline *</label>
                    <select class="form-control" id="timeline" required>
                        <option value="">Select required timeline</option>
                        <option value="immediate">Immediate (within hours)</option>
                        <option value="24hours">Within 24 hours</option>
                        <option value="72hours">Within 72 hours</option>
                        <option value="1week">Within 1 week</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Contact Information *</label>
                    <input type="text" class="form-control" id="contactName" placeholder="Emergency contact name" required style="margin-bottom: 8px;">
                    <input type="tel" class="form-control" id="contactPhone" placeholder="Emergency contact phone" required style="margin-bottom: 8px;">
                    <input type="email" class="form-control" id="contactEmail" placeholder="Emergency contact email">
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="status-text">
                Emergency Response System • 24/7 Processing • Immediate FEMA Coordination
            </div>
            <div>
                <button type="button" class="btn btn-primary" onclick="submitEmergencyIntake()">🚨 Submit Emergency Request</button>
            </div>
        </div>
    </div>
    
    <script>
        // Handle radio button selections
        document.addEventListener('change', function(e) {
            if (e.target.type === 'radio') {
                // Remove selected class from all items in the same group
                const groupName = e.target.name;
                document.querySelectorAll(`input[name="${groupName}"]`).forEach(radio => {
                    radio.closest('.category-item, .urgency-item').classList.remove('selected');
                });
                
                // Add selected class to the chosen item
                e.target.closest('.category-item, .urgency-item').classList.add('selected');
            }
        });
        
        async function submitEmergencyIntake() {
            const formData = {
                projectName: document.getElementById('projectName').value,
                workCategory: document.querySelector('input[name="workCategory"]:checked')?.value,
                incidentDate: document.getElementById('incidentDate').value,
                disasterNumber: document.getElementById('disasterNumber').value,
                estimatedCost: document.getElementById('estimatedCost').value,
                urgencyLevel: document.querySelector('input[name="urgencyLevel"]:checked')?.value,
                emergencyDescription: document.getElementById('emergencyDescription').value,
                locationDetails: document.getElementById('locationDetails').value,
                timeline: document.getElementById('timeline').value,
                contactName: document.getElementById('contactName').value,
                contactPhone: document.getElementById('contactPhone').value,
                contactEmail: document.getElementById('contactEmail').value,
                actions: Array.from(document.querySelectorAll('input[name="actions"]:checked')).map(cb => cb.value),
                resources: Array.from(document.querySelectorAll('input[name="resources"]:checked')).map(cb => cb.value),
                start_wizard: true
            };
            
            // Validate required fields
            if (!formData.projectName || !formData.workCategory || !formData.incidentDate || 
                !formData.estimatedCost || !formData.urgencyLevel || !formData.emergencyDescription ||
                !formData.locationDetails || !formData.timeline || !formData.contactName || !formData.contactPhone) {
                alert('Please fill in all required fields marked with *');
                return;
            }
            
            try {
                const response = await fetch('/api/intake/emergency', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert(`Emergency intake submitted successfully!\n\nIntake ID: ${data.intake_id}\nCategory: ${data.category}\nUrgency: ${formData.urgencyLevel}\n\nOur emergency response team will contact you within 1 hour.`);
                    window.location.href = '/dashboard';
                } else {
                    alert('Error submitting emergency intake: ' + data.message);
                }
            } catch (error) {
                console.error('Submit error:', error);
                alert('Error submitting emergency intake. Please try again or call our emergency hotline.');
            }
        }
        
        // Auto-set incident date to today if not filled
        document.addEventListener('DOMContentLoaded', function() {
            const incidentDate = document.getElementById('incidentDate');
            if (!incidentDate.value) {
                const today = new Date().toISOString().split('T')[0];
                incidentDate.value = today;
            }
        });
    </script>
</body>
</html> 
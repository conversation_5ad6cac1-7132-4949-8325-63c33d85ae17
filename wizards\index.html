<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax - FEMA Compliance Made Simple</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 3s ease-in-out infinite',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .disaster-bg {
            background: 
                linear-gradient(45deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><defs><pattern id="flood" patternUnits="userSpaceOnUse" width="40" height="20"><path d="M0,10 Q10,0 20,10 T40,10" stroke="rgba(59,130,246,0.03)" fill="none"/></pattern></defs><rect width="1200" height="600" fill="url(%23flood)"/></svg>');
            background-size: cover, 40px 20px;
            animation: flood-flow 20s linear infinite;
        }
        
        @keyframes flood-flow {
            0% { background-position: 0% 0%, 0px 0px; }
            100% { background-position: 0% 0%, 40px 0px; }
        }
        
        .technical-card {
            backdrop-filter: blur(8px);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 4px;
            background: rgba(15, 23, 42, 0.7);
        }
        
        .angular-button {
            border-radius: 4px;
            border: 1px solid rgba(71, 85, 105, 0.5);
            background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(30, 41, 59, 0.9) 100%);
        }
        
        .data-grid::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.02) 50%, transparent 100%),
                linear-gradient(0deg, transparent 0%, rgba(59, 130, 246, 0.02) 50%, transparent 100%);
            background-size: 20px 20px;
            pointer-events: none;
        }
    </style>
</head>
<body class="min-h-screen disaster-bg">

    <!-- Unified Navigation -->
    <nav class="relative z-50 px-6 py-4 glass-effect bg-slate-900/30 border-b border-slate-700/50">
        <div class="mx-auto max-w-7xl flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="h-10 w-10 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">CM</span>
                </div>
                <span class="text-2xl font-bold text-white">ComplianceMax</span>
                <span class="px-3 py-1 text-xs font-semibold bg-blue-600/20 text-blue-300 rounded-full border border-blue-500/30">
                    v74.0
                </span>
            </div>
            
            <div class="hidden md:flex items-center space-x-8">
                <a href="#" onclick="showHome()" class="text-slate-300 hover:text-white transition-colors font-medium">
                    Home
                </a>
                <span class="text-slate-500 font-medium cursor-not-allowed" title="Login required to access">
                    Get Started (Locked)
                </span>
                <span class="text-slate-500 font-medium cursor-not-allowed" title="Login required to access">
                    Compliance Wizard (Locked)
                </span>
                <span class="text-slate-500 font-medium cursor-not-allowed" title="Login required to access">
                    Dashboard (Locked)
                </span>
                <a href="#" onclick="showLogin()" class="px-4 py-2 text-white bg-slate-700 rounded-lg hover:bg-slate-600 transition-all duration-200 border border-slate-600">
                    Login
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <button class="md:hidden text-white" onclick="toggleMobileMenu()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobileMenu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700/50">
            <div class="flex flex-col space-y-3 pt-4">
                <a href="#" onclick="showHome()" class="text-slate-300 hover:text-white transition-colors">Home</a>
                <span class="text-slate-500 cursor-not-allowed">Get Started (Locked)</span>
                <span class="text-slate-500 cursor-not-allowed">Compliance Wizard (Locked)</span>
                <span class="text-slate-500 cursor-not-allowed">Dashboard (Locked)</span>
                <a href="#" onclick="showLogin()" class="text-white bg-slate-700 rounded-lg px-4 py-2 text-center border border-slate-600">Login</a>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div id="mainContent">

        <!-- HOME PAGE -->
        <div id="homePage" class="active-page">
            <!-- Hero Section -->
            <div class="relative px-6 pt-20 pb-16">
                <div class="mx-auto max-w-7xl">
                    <div class="text-center">
                        <!-- Technical Grid Overlay -->
                        <div class="absolute inset-0 opacity-5 pointer-events-none">
                            <div class="grid grid-cols-12 h-full">
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                                <div class="border-r border-slate-600"></div>
                            </div>
                        </div>

                        <h1 class="text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl">
                            FEMA PUBLIC ASSISTANCE
                            <span class="block text-slate-300 font-normal text-lg sm:text-xl lg:text-2xl">
                                Compliance Documentation System
                            </span>
                        </h1>
                        
                        <p class="mt-8 text-xl leading-8 text-slate-300 max-w-4xl mx-auto">
                            Professional-grade compliance management with intelligent workflow automation, 
                            real-time FEMA integration, and enterprise-level security. Get your Public 
                            Assistance applications approved faster than ever.
                        </p>
                        
                        <!-- Primary CTA -->
                        <div class="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6">
                            <button 
                                onclick="showLogin()"
                                class="inline-flex items-center justify-center px-10 py-4 text-lg font-medium text-white bg-slate-700 rounded-lg hover:bg-slate-600 transition-all duration-200 border border-slate-600"
                            >
                                Client Portal Access
                                <span class="ml-2">→</span>
                            </button>
                            <button 
                                onclick="showSubscribe()"
                                class="inline-flex items-center justify-center px-10 py-4 text-lg font-medium text-slate-300 border border-slate-600 rounded-lg hover:text-white hover:border-slate-500 hover:bg-slate-800/50 transition-all duration-200"
                            >
                                Service Information
                                <span class="ml-2">→</span>
                            </button>
                        </div>

                        <!-- Process Flow Indicator -->
                        <div class="mt-16 flex items-center justify-center space-x-4">
                            <div class="flex items-center space-x-2 px-4 py-2 bg-slate-800/50 rounded-full glass-effect">
                                <span class="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></span>
                                <span class="text-sm text-slate-300">Step 1: Login/Subscribe</span>
                            </div>
                            <div class="w-8 h-0.5 bg-slate-600"></div>
                            <div class="flex items-center space-x-2 px-4 py-2 bg-slate-800/50 rounded-full glass-effect">
                                <span class="w-3 h-3 bg-cyan-400 rounded-full"></span>
                                <span class="text-sm text-slate-300">Step 2: Start New Project</span>
                            </div>
                            <div class="w-8 h-0.5 bg-slate-600"></div>
                            <div class="flex items-center space-x-2 px-4 py-2 bg-slate-800/50 rounded-full glass-effect">
                                <span class="w-3 h-3 bg-purple-400 rounded-full"></span>
                                <span class="text-sm text-slate-300">Step 3: Compliance Success</span>
                            </div>
                        </div>

                        <!-- Live Status Indicators -->
                        <div class="mt-12 flex flex-wrap items-center justify-center gap-6 text-sm text-slate-400">
                            <div class="flex items-center space-x-2 px-4 py-2 bg-green-500/10 border border-green-500/30 rounded-full">
                                <span class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                                <span>✅ 18 Active Disasters Monitored</span>
                            </div>
                            <div class="flex items-center space-x-2 px-4 py-2 bg-blue-500/10 border border-blue-500/30 rounded-full">
                                <span class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
                                <span>🔗 Live FEMA API Integration</span>
                            </div>
                            <div class="flex items-center space-x-2 px-4 py-2 bg-cyan-500/10 border border-cyan-500/30 rounded-full">
                                <span class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></span>
                                <span>🔒 Enterprise Security Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="relative px-6 py-24 glass-effect bg-slate-900/30">
                <div class="mx-auto max-w-7xl">
                    <div class="text-center mb-16">
                        <h2 class="text-4xl font-bold text-white mb-6">
                            FEMA Public Assistance Service Areas
                        </h2>
                        <p class="text-lg text-slate-300 max-w-4xl mx-auto">
                            Comprehensive compliance assistance across all phases of FEMA Public Assistance program management, 
                            from initial application through appeals process.
                        </p>
                    </div>

                    <!-- Service Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- PA Compliance Assistance -->
                        <div class="technical-card p-4 hover:bg-slate-800/50 hover:border-blue-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">PA Compliance Assistance</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                End-to-end Public Assistance program compliance support and technical guidance.
                            </p>
                        </div>

                        <!-- Costing -->
                        <div class="bg-slate-800/30 glass-effect border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/50 hover:border-cyan-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">Costing</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                Cost documentation, reasonableness analysis, and financial compliance verification.
                            </p>
                        </div>

                        <!-- Codes & Standards -->
                        <div class="bg-slate-800/30 glass-effect border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/50 hover:border-green-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">Codes & Standards</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                Building codes compliance, construction standards, and regulatory adherence.
                            </p>
                        </div>

                        <!-- Environmental & Historic Preservation -->
                        <div class="bg-slate-800/30 glass-effect border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/50 hover:border-purple-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">EHP</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                Environmental & Historic Preservation compliance and documentation.
                            </p>
                        </div>

                        <!-- Mitigation -->
                        <div class="bg-slate-800/30 glass-effect border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/50 hover:border-yellow-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">Mitigation</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                Hazard mitigation planning, implementation, and compliance support.
                            </p>
                        </div>

                        <!-- Policy -->
                        <div class="bg-slate-800/30 glass-effect border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/50 hover:border-red-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">Policy</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                PAPPG interpretation, policy application, and regulatory guidance.
                            </p>
                        </div>

                        <!-- Appeals -->
                        <div class="bg-slate-800/30 glass-effect border border-slate-700/50 rounded-xl p-6 hover:bg-slate-800/50 hover:border-orange-500/30 transition-all duration-200">
                            <h3 class="text-lg font-semibold text-white mb-3">Appeals</h3>
                            <p class="text-sm text-slate-400 leading-relaxed">
                                Appeals preparation, documentation, and representation services.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Final CTA -->
            <div class="relative px-6 py-24">
                <div class="mx-auto max-w-5xl text-center">
                                            <h2 class="text-4xl font-bold text-white mb-8">
                            Streamline Your FEMA Public Assistance Documentation
                        </h2>
                        <p class="text-lg text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                            Professional document processing and compliance review services for FEMA PA applicants. 
                            Our experts identify documentation gaps, address regulatory requirements, and provide 
                            targeted recommendations to maximize program approval rates.
                        </p>
                    <div class="flex flex-col sm:flex-row gap-6 justify-center">
                        <button 
                            onclick="showLogin()"
                            class="group inline-flex items-center justify-center px-12 py-6 text-xl font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 hover:scale-105"
                        >
                            🔐 Access Your Account
                            <span class="ml-3 group-hover:translate-x-2 transition-transform">→</span>
                        </button>
                        <button 
                            onclick="showSubscribe()"
                            class="inline-flex items-center justify-center px-12 py-6 text-xl font-semibold text-slate-300 border-2 border-slate-600 rounded-2xl hover:text-white hover:border-cyan-400 hover:bg-slate-800/50 transition-all duration-300 glass-effect"
                        >
                            📋 Get Subscription Info
                        </button>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="relative px-6 py-12 border-t border-slate-700/50 glass-effect bg-slate-900/20">
                <div class="mx-auto max-w-7xl">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-8">
                        <div class="flex items-center space-x-3 mb-4 md:mb-0">
                            <div class="h-8 w-8 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-lg"></div>
                            <span class="text-xl font-semibold text-white">ComplianceMax</span>
                            <span class="text-xs text-slate-400">June 2025 v74.0</span>
                        </div>
                        <div class="flex items-center space-x-8 text-sm text-slate-400">
                            <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
                            <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                            <a href="#" class="hover:text-white transition-colors">Support</a>
                            <a href="#" class="hover:text-white transition-colors">API Docs</a>
                        </div>
                    </div>
                    <div class="text-center text-sm text-slate-500 border-t border-slate-700/50 pt-8">
                        © 2025 ComplianceMax. Professional FEMA compliance management system. 
                        Built for disaster recovery professionals.
                    </div>
                </div>
            </footer>
        </div>

        <!-- ONBOARDING PAGE (Loaded dynamically) -->
        <div id="onboardingPage" class="hidden">
            <iframe src="intelligent-onboarding.html" class="w-full h-screen border-none"></iframe>
        </div>

        <!-- WIZARD PAGE (Loaded dynamically) -->
        <div id="wizardPage" class="hidden">
            <iframe src="components/EnhancedComplianceWizard.html" class="w-full h-screen border-none"></iframe>
        </div>

    </div>

    <script>
        // Navigation Functions
        function showHome() {
            hideAllPages();
            document.getElementById('homePage').classList.remove('hidden');
            document.getElementById('homePage').classList.add('active-page');
        }

        function showLogin() {
            // Simulate login process - redirect to onboarding for new users
            const loginModal = confirm('🔐 LOGIN TO COMPLIANCEMAX\n\nExisting clients: Click OK to access your dashboard\nNew users: Click Cancel to learn about subscription options');
            
            if (loginModal) {
                // Existing user - go to onboarding (simulating post-login dashboard)
                showOnboarding();
            } else {
                // New user - show subscription info
                showSubscribe();
            }
        }

        function showOnboarding() {
            hideAllPages();
            document.getElementById('onboardingPage').classList.remove('hidden');
            // Load the onboarding page if not already loaded
            const iframe = document.querySelector('#onboardingPage iframe');
            if (!iframe.src.includes('intelligent-onboarding.html')) {
                iframe.src = 'intelligent-onboarding.html';
            }
        }

        function showWizard() {
            hideAllPages();
            document.getElementById('wizardPage').classList.remove('hidden');
            // Load the wizard page if not already loaded
            const iframe = document.querySelector('#wizardPage iframe');
            if (!iframe.src.includes('EnhancedComplianceWizard.html')) {
                iframe.src = 'components/EnhancedComplianceWizard.html';
            }
        }

        function hideAllPages() {
            document.getElementById('homePage').classList.add('hidden');
            document.getElementById('onboardingPage').classList.add('hidden');
            document.getElementById('wizardPage').classList.add('hidden');
            document.querySelectorAll('.active-page').forEach(page => {
                page.classList.remove('active-page');
            });
        }

        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }

        // Initialize
        window.addEventListener('load', function() {
            showHome();
            // All navigation locked except Home and Login
        });

        // Listen for messages from iframes
        window.addEventListener('message', function(event) {
            if (event.data.action === 'navigate') {
                switch(event.data.page) {
                    case 'home':
                        showHome();
                        break;
                    case 'onboarding':
                        showOnboarding();
                        break;
                    case 'wizard':
                        showWizard();
                        break;
                }
            } else if (event.data.action === 'onboarding-complete') {
                // Unlock the compliance wizard
                unlockComplianceWizard();
                // Show success message
                showOnboardingComplete();
            }
        });

        // All navigation items except Home and Login are permanently locked
        // No unlock functionality available on landing page

        // Login and Subscribe Functions
        function showLogin() {
            // Simulate login process - redirect to onboarding for new users
            const loginModal = confirm('🔐 LOGIN TO COMPLIANCEMAX\n\nExisting clients: Click OK to access your dashboard\nNew users: Click Cancel to learn about subscription options');
            
            if (loginModal) {
                // Existing user - go to onboarding (simulating post-login dashboard)
                showOnboarding();
            } else {
                // New user - show subscription info
                showSubscribe();
            }
        }

        function showSubscribe() {
            // Create a modern subscription modal
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-slate-800 rounded-2xl p-8 max-w-md w-full border border-slate-600 shadow-2xl">
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-bold text-white mb-2">ComplianceMax Subscription</h3>
                        <p class="text-slate-300">Professional FEMA PA Compliance Services</p>
                    </div>
                    
                    <div class="space-y-4 mb-6">
                        <div class="bg-slate-700 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-lg font-semibold text-white">Monthly Platform</span>
                                <span class="text-xl font-bold text-cyan-400">$325/mo</span>
                            </div>
                            <p class="text-sm text-slate-300">Base platform access & tools</p>
                        </div>
                        
                        <div class="bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-lg font-semibold text-white">Base Report</span>
                                <span class="text-xl font-bold text-white">$999.00</span>
                            </div>
                            <p class="text-sm text-blue-100">Complete compliance documentation</p>
                        </div>
                        
                        <div class="text-center text-sm text-slate-400">
                            Graduated pricing for project complexity
                        </div>
                    </div>
                    
                    <div class="text-center space-y-3">
                        <div class="text-sm font-medium text-cyan-400 mb-2">🚀 SUBSCRIBE HERE</div>
                        <button onclick="window.location.href='mailto:<EMAIL>?subject=ComplianceMax%20Subscription%20%2D%20Ready%20to%20Start&body=Hello%2C%0A%0AI%20am%20ready%20to%20subscribe%20to%20ComplianceMax%20for%20FEMA%20Public%20Assistance%20compliance%20services.%0A%0APlease%20send%20me%20the%20subscription%20setup%20information.%0A%0AThank%20you%2C%0A%5BYour%20Name%5D%0A%5BYour%20Organization%5D%0A%5BYour%20Phone%20Number%5D'; closeModal()" class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-cyan-700 transition-all duration-200">
                            Get Started Now →
                        </button>
                        <button onclick="closeModal()" class="w-full text-slate-400 hover:text-white transition-colors">
                            Maybe Later
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Close modal function
            window.closeModal = function() {
                document.body.removeChild(modal);
                delete window.closeModal;
            };
            
            // Close on backdrop click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }
    </script>
</body>
</html> 
import React from 'react';

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
      {/* Navigation */}
      <nav className="relative z-10 px-6 pt-6">
        <div className="mx-auto max-w-7xl flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-blue-400 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CM</span>
            </div>
            <span className="text-2xl font-bold text-white">ComplianceMax</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <a href="/features" className="text-slate-300 hover:text-white transition-colors">
              Features
            </a>
            <a href="/pricing" className="text-slate-300 hover:text-white transition-colors">
              Pricing
            </a>
            <a href="/support" className="text-slate-300 hover:text-white transition-colors">
              Support
            </a>
            <a href="/login" className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
              Login
            </a>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative px-6 pt-14 pb-16">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h1 className="text-6xl font-bold tracking-tight text-white sm:text-7xl">
              FEMA Compliance
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">
                Made Simple
              </span>
            </h1>
            <p className="mt-8 text-xl leading-8 text-slate-300 max-w-3xl mx-auto">
              Professional-grade compliance management with intelligent workflow automation, 
              real-time FEMA integration, and enterprise-level security. Get your Public 
              Assistance applications approved faster.
            </p>
            
            {/* Primary CTA */}
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <a 
                href="/onboarding" 
                className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
              >
                Get Started
                <span className="ml-2">→</span>
              </a>
              <a 
                href="/demo" 
                className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-slate-300 border border-slate-600 rounded-full hover:text-white hover:border-slate-400 transition-all duration-300"
              >
                Watch Demo
              </a>
            </div>

            {/* Trust Indicators */}
            <div className="mt-16 flex items-center justify-center space-x-8 text-sm text-slate-400">
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                <span>18 Active Disasters Monitored</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                <span>Real-Time FEMA Integration</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-cyan-400 rounded-full"></span>
                <span>Enterprise Security</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="relative px-6 py-24 bg-slate-900/50">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Why Choose ComplianceMax?
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Built by compliance experts, powered by cutting-edge technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Intelligent Automation */}
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 hover:bg-slate-800/70 transition-all duration-300">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-blue-600/20 rounded-lg">
                  <span className="text-blue-400 text-2xl">⚡</span>
                </div>
                <h3 className="ml-4 text-xl font-semibold text-white">Intelligent Automation</h3>
              </div>
              <p className="text-slate-300 mb-6">
                Enter your FEMA# and watch our system auto-populate critical information, 
                select appropriate policies, and customize your workflow.
              </p>
              <ul className="space-y-2 text-sm text-slate-400">
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  Auto-populate from FEMA registration
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  PAPPG version determination
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  Policy framework selection
                </li>
              </ul>
            </div>

            {/* Real-Time Data */}
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 hover:bg-slate-800/70 transition-all duration-300">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-cyan-600/20 rounded-lg">
                  <span className="text-cyan-400 text-2xl">🌐</span>
                </div>
                <h3 className="ml-4 text-xl font-semibold text-white">Live FEMA Integration</h3>
              </div>
              <p className="text-slate-300 mb-6">
                Access real-time disaster data, county-level analysis, and 
                current PA eligibility directly from FEMA's systems.
              </p>
              <ul className="space-y-2 text-sm text-slate-400">
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  18 active disasters tracked
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  County-level FIPS integration
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  GeoJSON mapping ready
                </li>
              </ul>
            </div>

            {/* Expert Guidance */}
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 hover:bg-slate-800/70 transition-all duration-300">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-purple-600/20 rounded-lg">
                  <span className="text-purple-400 text-2xl">👥</span>
                </div>
                <h3 className="ml-4 text-xl font-semibold text-white">Expert Guidance</h3>
              </div>
              <p className="text-slate-300 mb-6">
                Navigate complex DRRA, NFIP, EHP, and procurement requirements 
                with intelligent decision trees and policy integration.
              </p>
              <ul className="space-y-2 text-sm text-slate-400">
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  DRRA Section 1206 & 1235b
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  2 CFR 200 procurement
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 text-green-400 mr-2">✓</span>
                  Environmental compliance
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Final CTA */}
      <div className="relative px-6 py-24">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Streamline Your FEMA Compliance?
          </h2>
          <p className="text-xl text-slate-300 mb-10 max-w-2xl mx-auto">
            Join the next generation of disaster recovery professionals using 
            intelligent compliance management.
          </p>
          <a 
            href="/onboarding" 
            className="group inline-flex items-center justify-center px-10 py-5 text-xl font-semibold text-white bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
          >
            Start Your Free Trial
            <span className="ml-3">→</span>
          </a>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative px-6 py-12 border-t border-slate-800">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="h-6 w-6 bg-blue-400 rounded"></div>
              <span className="text-lg font-semibold text-white">ComplianceMax</span>
            </div>
            <div className="flex items-center space-x-6 text-sm text-slate-400">
              <a href="/privacy" className="hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="/terms" className="hover:text-white transition-colors">
                Terms of Service
              </a>
              <a href="/support" className="hover:text-white transition-colors">
                Support
              </a>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-slate-800 text-center text-sm text-slate-500">
            © 2025 ComplianceMax. Professional FEMA compliance management system.
          </div>
        </div>
      </footer>
    </div>
  );
} 
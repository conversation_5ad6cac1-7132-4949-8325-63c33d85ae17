import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Checkbox,
  FormControlLabel,
  useTheme,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  Info,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
} from '@mui/icons-material';

interface PolicySelectionProps {
  selectedPolicies: string[];
  onUpdate: (policies: string[]) => void;
}

interface Policy {
  id: string;
  name: string;
  description: string;
  category: string;
  status: 'required' | 'recommended' | 'optional';
  riskLevel: 'low' | 'medium' | 'high';
}

const policies: Policy[] = [
  {
    id: 'procurement-standards',
    name: 'Procurement Standards',
    description: '2 CFR 200.317-326 procurement requirements',
    category: 'Administrative',
    status: 'required',
    riskLevel: 'high',
  },
  {
    id: 'cost-principles',
    name: 'Cost Principles',
    description: '2 CFR 200 Subpart E cost eligibility requirements',
    category: 'Financial',
    status: 'required',
    riskLevel: 'high',
  },
  {
    id: 'environmental-review',
    name: 'Environmental Review',
    description: 'Environmental and Historic Preservation requirements',
    category: 'Compliance',
    status: 'required',
    riskLevel: 'high',
  },
  {
    id: 'insurance-requirements',
    name: 'Insurance Requirements',
    description: 'Insurance coverage and documentation requirements',
    category: 'Administrative',
    status: 'recommended',
    riskLevel: 'medium',
  },
  {
    id: 'davis-bacon',
    name: 'Davis-Bacon Act',
    description: 'Federal prevailing wage requirements',
    category: 'Compliance',
    status: 'recommended',
    riskLevel: 'medium',
  },
  {
    id: 'section-406',
    name: 'Section 406 Hazard Mitigation',
    description: 'Hazard mitigation measures under Section 406',
    category: 'Technical',
    status: 'optional',
    riskLevel: 'low',
  },
];

const PolicySelection: React.FC<PolicySelectionProps> = ({
  selectedPolicies,
  onUpdate,
}) => {
  const theme = useTheme();

  const handlePolicyChange = (policyId: string) => {
    const newSelection = selectedPolicies.includes(policyId)
      ? selectedPolicies.filter((id) => id !== policyId)
      : [...selectedPolicies, policyId];
    onUpdate(newSelection);
  };

  const getStatusColor = (status: Policy['status']) => {
    switch (status) {
      case 'required':
        return theme.palette.error.main;
      case 'recommended':
        return theme.palette.warning.main;
      case 'optional':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const getRiskIcon = (riskLevel: Policy['riskLevel']) => {
    switch (riskLevel) {
      case 'high':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      case 'medium':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'low':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      default:
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  return (
    <Box>
      <Typography
        variant="h6"
        sx={{ mb: 3, color: 'rgba(255, 255, 255, 0.9)' }}
      >
        Select Applicable Policies
      </Typography>

      <Grid container spacing={2}>
        {policies.map((policy) => (
          <Grid item xs={12} key={policy.id}>
            <Paper
              sx={{
                p: 2,
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                transition: 'all 0.2s ease',
                '&:hover': {
                  background: 'rgba(255, 255, 255, 0.15)',
                },
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedPolicies.includes(policy.id)}
                      onChange={() => handlePolicyChange(policy.id)}
                      sx={{
                        color: 'rgba(255, 255, 255, 0.7)',
                        '&.Mui-checked': {
                          color: theme.palette.primary.main,
                        },
                      }}
                    />
                  }
                  label={
                    <Box>
                      <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                        {policy.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                      >
                        {policy.description}
                      </Typography>
                    </Box>
                  }
                />
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                  <Tooltip title={`Risk Level: ${policy.riskLevel}`}>
                    {getRiskIcon(policy.riskLevel)}
                  </Tooltip>
                  <Chip
                    label={policy.category}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      color: 'rgba(255, 255, 255, 0.9)',
                    }}
                  />
                  <Chip
                    label={policy.status}
                    size="small"
                    sx={{
                      backgroundColor: `${getStatusColor(policy.status)}20`,
                      color: getStatusColor(policy.status),
                    }}
                  />
                </Box>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default PolicySelection;

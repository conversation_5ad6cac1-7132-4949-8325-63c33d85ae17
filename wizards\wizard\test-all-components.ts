#!/usr/bin/env ts-node

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

// ================= Test Configuration =================

interface TestConfig {
  name: string;
  command: string;
  directory?: string;
  timeout?: number;
  critical?: boolean;
  description: string;
}

const tests: TestConfig[] = [
  {
    name: 'Data Validation',
    command: 'node -r ts-node/register/transpile-only scripts/data-validator.ts',
    timeout: 30000,
    critical: true,
    description: 'Validate all JSON data files and schemas'
  },
  {
    name: 'Next.js App Lint',
    command: 'npm run lint',
    directory: 'ALL NEW APP',
    timeout: 20000,
    critical: true,
    description: 'Check Next.js application for code quality issues'
  },
  {
    name: 'Checklist Import',
    command: 'npm run import:checklist',
    directory: 'ALL NEW APP',
    timeout: 60000,
    critical: true,
    description: 'Test checklist data import and processing'
  },
  {
    name: 'Wizard Processing',
    command: 'npm run process:wizard',
    directory: 'ALL NEW APP',
    timeout: 30000,
    critical: false,
    description: 'Process wizard components and generate flows'
  },
  {
    name: 'Next.js Build Test',
    command: 'npm run build',
    directory: 'ALL NEW APP',
    timeout: 120000,
    critical: true,
    description: 'Test if the Next.js application builds successfully'
  },
  {
    name: 'TypeScript Compilation',
    command: 'npx tsc --noEmit',
    directory: 'ALL NEW APP',
    timeout: 30000,
    critical: true,
    description: 'Check TypeScript compilation without emit'
  }
];

// ================= Utility Functions =================

function colorLog(message: string, color: 'green' | 'red' | 'yellow' | 'blue' | 'gray' = 'gray'): void {
  const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    gray: '\x1b[90m'
  };
  const reset = '\x1b[0m';
  console.log(`${colors[color]}${message}${reset}`);
}

function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(1)}s`;
}

async function checkPrerequisites(): Promise<boolean> {
  colorLog('🔍 Checking prerequisites...', 'blue');
  
  const checks = [
    { name: 'Node.js', command: 'node --version' },
    { name: 'npm', command: 'npm --version' },
    { name: 'TypeScript', command: 'npx tsc --version' }
  ];
  
  let allGood = true;
  
  for (const check of checks) {
    try {
      const { stdout } = await execAsync(check.command);
      colorLog(`  ✅ ${check.name}: ${stdout.trim()}`, 'green');
    } catch (error) {
      colorLog(`  ❌ ${check.name}: Not found`, 'red');
      allGood = false;
    }
  }
  
  return allGood;
}

async function checkDirectoryStructure(): Promise<boolean> {
  colorLog('\n📁 Checking directory structure...', 'blue');
  
  const requiredDirs = [
    'ALL NEW APP',
    'DOCS',
    'wizard',
    'scripts'
  ];
  
  const requiredFiles = [
    'ALL NEW APP/package.json',
    'ALL NEW APP/next.config.js',
    'ALL NEW APP/scripts/importChecklists.ts',
    'DOCS/source_data/Unified_Compliance_Checklist_TAGGED.json'
  ];
  
  let allGood = true;
  
  for (const dir of requiredDirs) {
    if (fs.existsSync(dir)) {
      colorLog(`  ✅ Directory: ${dir}`, 'green');
    } else {
      colorLog(`  ❌ Missing directory: ${dir}`, 'red');
      allGood = false;
    }
  }
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      const stats = fs.statSync(file);
      const size = stats.size > 1024 ? `${Math.round(stats.size / 1024)}KB` : `${stats.size}B`;
      colorLog(`  ✅ File: ${file} (${size})`, 'green');
    } else {
      colorLog(`  ❌ Missing file: ${file}`, 'red');
      allGood = false;
    }
  }
  
  return allGood;
}

async function runTest(test: TestConfig): Promise<{
  success: boolean;
  duration: number;
  output: string;
  error?: string;
}> {
  const startTime = Date.now();
  const workingDir = test.directory ? path.resolve(process.cwd(), test.directory) : process.cwd();
  
  try {
    const { stdout, stderr } = await execAsync(test.command, {
      cwd: workingDir,
      timeout: test.timeout || 30000,
      maxBuffer: 1024 * 1024 * 10 // 10MB buffer
    });
    
    const duration = Date.now() - startTime;
    return {
      success: true,
      duration,
      output: stdout,
      error: stderr
    };
  } catch (error: any) {
    const duration = Date.now() - startTime;
    return {
      success: false,
      duration,
      output: error.stdout || '',
      error: error.message || error.stderr || 'Unknown error'
    };
  }
}

async function generateReport(results: any[], outputDir: string): Promise<void> {
  await fs.promises.mkdir(outputDir, { recursive: true });
  
  const reportPath = path.join(outputDir, 'test-report.md');
  const jsonPath = path.join(outputDir, 'test-results.json');
  
  // Generate markdown report
  const report = [
    '# ComplianceMax System Test Report',
    '',
    `**Generated:** ${new Date().toISOString()}`,
    `**Tests Run:** ${results.length}`,
    `**Passed:** ${results.filter(r => r.success).length}`,
    `**Failed:** ${results.filter(r => !r.success).length}`,
    `**Critical Failures:** ${results.filter(r => !r.success && r.critical).length}`,
    '',
    '## Test Results',
    ''
  ];
  
  for (const result of results) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const critical = result.critical ? ' (CRITICAL)' : '';
    const duration = formatDuration(result.duration);
    
    report.push(`### ${result.name}${critical}`);
    report.push('');
    report.push(`- **Status:** ${status}`);
    report.push(`- **Duration:** ${duration}`);
    report.push(`- **Description:** ${result.description}`);
    
    if (!result.success) {
      report.push(`- **Error:** ${result.error}`);
    }
    
    if (result.output && result.output.length < 1000) {
      report.push('- **Output:**');
      report.push('```');
      report.push(result.output.trim());
      report.push('```');
    }
    
    report.push('');
  }
  
  // Add recommendations
  report.push('## Recommendations');
  report.push('');
  
  const failedCritical = results.filter(r => !r.success && r.critical);
  if (failedCritical.length > 0) {
    report.push('### Critical Issues');
    for (const failure of failedCritical) {
      report.push(`- Fix ${failure.name}: ${failure.error}`);
    }
    report.push('');
  }
  
  const failedNonCritical = results.filter(r => !r.success && !r.critical);
  if (failedNonCritical.length > 0) {
    report.push('### Non-Critical Issues');
    for (const failure of failedNonCritical) {
      report.push(`- Consider fixing ${failure.name}: ${failure.error}`);
    }
    report.push('');
  }
  
  if (results.every(r => r.success)) {
    report.push('🎉 All tests passed! The ComplianceMax system appears to be working correctly.');
  }
  
  await fs.promises.writeFile(reportPath, report.join('\n'));
  await fs.promises.writeFile(jsonPath, JSON.stringify(results, null, 2));
  
  colorLog(`📊 Test report generated: ${reportPath}`, 'blue');
  colorLog(`📋 JSON results saved: ${jsonPath}`, 'blue');
}

// ================= Main Test Runner =================

async function main(): Promise<void> {
  console.log('🧪 ComplianceMax System Test Suite');
  console.log('===================================');
  
  try {
    // Check prerequisites
    const prereqsOk = await checkPrerequisites();
    if (!prereqsOk) {
      colorLog('\n❌ Prerequisites check failed. Please install missing dependencies.', 'red');
      process.exit(1);
    }
    
    // Check directory structure
    const structureOk = await checkDirectoryStructure();
    if (!structureOk) {
      colorLog('\n❌ Directory structure check failed. Please ensure all required files and directories exist.', 'red');
      process.exit(1);
    }
    
    colorLog('\n🚀 Starting test suite...', 'blue');
    
    const results = [];
    let totalDuration = 0;
    
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      const testNumber = `[${i + 1}/${tests.length}]`;
      
      colorLog(`\n${testNumber} Running: ${test.name}`, 'blue');
      colorLog(`    ${test.description}`, 'gray');
      
      const result = await runTest(test);
      result.name = test.name;
      result.description = test.description;
      result.critical = test.critical || false;
      
      totalDuration += result.duration;
      results.push(result);
      
      if (result.success) {
        colorLog(`    ✅ PASSED (${formatDuration(result.duration)})`, 'green');
      } else {
        const severity = test.critical ? 'CRITICAL FAILURE' : 'FAILED';
        const color = test.critical ? 'red' : 'yellow';
        colorLog(`    ❌ ${severity} (${formatDuration(result.duration)})`, color);
        
        if (result.error) {
          colorLog(`    Error: ${result.error.substring(0, 200)}${result.error.length > 200 ? '...' : ''}`, 'gray');
        }
        
        // Stop on critical failures
        if (test.critical) {
          colorLog(`\n💀 Critical test failed: ${test.name}`, 'red');
          colorLog('Test suite cannot continue due to critical failure.', 'red');
          break;
        }
      }
    }
    
    // Generate comprehensive report
    colorLog('\n📝 Generating test report...', 'blue');
    const outputDir = path.resolve(process.cwd(), 'test-results');
    await generateReport(results, outputDir);
    
    // Summary
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const criticalFailed = results.filter(r => !r.success && r.critical).length;
    
    console.log('\n📊 TEST SUMMARY');
    console.log('================');
    colorLog(`Total Tests: ${results.length}`, 'blue');
    colorLog(`Passed: ${passed}`, passed > 0 ? 'green' : 'gray');
    colorLog(`Failed: ${failed}`, failed > 0 ? 'yellow' : 'gray');
    colorLog(`Critical Failures: ${criticalFailed}`, criticalFailed > 0 ? 'red' : 'gray');
    colorLog(`Total Duration: ${formatDuration(totalDuration)}`, 'blue');
    
    // Final verdict
    if (criticalFailed > 0) {
      colorLog('\n💀 SYSTEM NOT READY - Critical failures must be fixed before deployment', 'red');
      process.exit(1);
    } else if (failed > 0) {
      colorLog('\n⚠️  SYSTEM MOSTLY READY - Some non-critical issues found', 'yellow');
      process.exit(0);
    } else {
      colorLog('\n🎉 SYSTEM READY - All tests passed successfully!', 'green');
      process.exit(0);
    }
    
  } catch (error) {
    colorLog(`\n💥 Test suite crashed: ${error.message}`, 'red');
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test suite
if (require.main === module) {
  main();
} 
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Chip,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import { useApi } from '../../contexts/ApiContext';
import { Schedule, ScheduleStatus } from '../../types/schedule';
import { format } from 'date-fns';
import { WorkflowExecution } from '../../types/workflow';

interface ScheduleDetailsProps {
  scheduleId: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`schedule-tabpanel-${index}`}
      aria-labelledby={`schedule-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ScheduleDetails: React.FC<ScheduleDetailsProps> = ({ scheduleId }) => {
  const [schedule, setSchedule] = useState<Schedule | null>(null);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const api = useApi();

  useEffect(() => {
    fetchSchedule();
    fetchExecutions();
  }, [scheduleId]);

  const fetchSchedule = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/schedules/${scheduleId}`);
      setSchedule(response.data);
    } catch (err) {
      setError('Failed to fetch schedule details');
    } finally {
      setLoading(false);
    }
  };

  const fetchExecutions = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/workflows/${scheduleId}/executions`);
      setExecutions(response.data);
    } catch (err) {
      setError('Failed to fetch execution history');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: ScheduleStatus) => {
    switch (status) {
      case ScheduleStatus.ACTIVE:
        return 'success';
      case ScheduleStatus.PAUSED:
        return 'warning';
      case ScheduleStatus.FAILED:
        return 'error';
      case ScheduleStatus.COMPLETED:
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!schedule) {
    return null;
  }

  return (
    <Box sx={{ p: 2 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h5">{schedule.name}</Typography>
              <Chip
                label={schedule.status}
                color={getStatusColor(schedule.status)}
              />
            </Box>
            <Typography color="textSecondary" gutterBottom>
              {schedule.description}
            </Typography>
            <Divider sx={{ my: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  Workflow
                </Typography>
                <Typography>{schedule.workflow_id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  Next Run
                </Typography>
                <Typography>
                  {schedule.next_run
                    ? format(new Date(schedule.next_run), 'PPpp')
                    : 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  Last Run
                </Typography>
                <Typography>
                  {schedule.last_run
                    ? format(new Date(schedule.last_run), 'PPpp')
                    : 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  Frequency
                </Typography>
                <Typography>
                  {schedule.recurrence.frequency}
                  {schedule.recurrence.interval > 1
                    ? ` (every ${schedule.recurrence.interval} ${
                        schedule.recurrence.frequency
                      }s)`
                    : ''}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper>
            <Tabs
              value={tabValue}
              onChange={(_, newValue) => setTabValue(newValue)}
              sx={{ borderBottom: 1, borderColor: 'divider' }}
            >
              <Tab label="Execution History" />
              <Tab label="Schedule Details" />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Started</TableCell>
                      <TableCell>Completed</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Duration</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {executions.map((execution) => (
                      <TableRow key={execution.id}>
                        <TableCell>
                          {format(new Date(execution.started_at), 'PPpp')}
                        </TableCell>
                        <TableCell>
                          {execution.completed_at
                            ? format(new Date(execution.completed_at), 'PPpp')
                            : 'N/A'}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={execution.status}
                            color={
                              execution.status === 'completed'
                                ? 'success'
                                : execution.status === 'failed'
                                ? 'error'
                                : 'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {execution.completed_at
                            ? `${Math.round(
                                (new Date(execution.completed_at).getTime() -
                                  new Date(execution.started_at).getTime()) /
                                  1000
                              )}s`
                            : 'N/A'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Created At
                  </Typography>
                  <Typography>
                    {format(new Date(schedule.created_at), 'PPpp')}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Last Updated
                  </Typography>
                  <Typography>
                    {format(new Date(schedule.updated_at), 'PPpp')}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Recurrence Details
                  </Typography>
                  <Typography>
                    {JSON.stringify(schedule.recurrence, null, 2)}
                  </Typography>
                </Grid>
                {Object.keys(schedule.metadata).length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Metadata
                    </Typography>
                    <Typography>
                      {JSON.stringify(schedule.metadata, null, 2)}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ScheduleDetails; 
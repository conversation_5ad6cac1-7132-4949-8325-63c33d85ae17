
import { render, screen, waitFor, act } from '@/test-utils/test-utils'
import RealtimePage from '@/app/realtime/page'

describe('API Integration Tests', () => {
  let mockFetch: jest.MockedFunction<typeof fetch>

  beforeEach(() => {
    mockFetch = global.fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockClear()

    // Mock WebSocket to prevent connection attempts
    global.WebSocket = jest.fn().mockImplementation(() => ({
      close: jest.fn(),
      send: jest.fn(),
      readyState: 3, // CLOSED
    }))
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('fetches data from correct API endpoint', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        timestamp: '2025-06-23T10:00:00Z',
        data: [],
        status: 'success',
        total_apis: 0
      }),
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('http://localhost:8001/api/realtime/data')
    })
  })

  test('handles successful API response', async () => {
    const mockData = {
      timestamp: '2025-06-23T10:00:00Z',
      data: [
        {
          api: 'fema_status',
          status: 'approved',
          case_id: 'FEMA-1234',
          last_updated: '2025-06-23T09:00:00Z',
          estimated_completion: '2025-07-23T10:00:00Z',
          timestamp: '2025-06-23T10:00:00Z'
        }
      ],
      status: 'success',
      total_apis: 1
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockData,
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(screen.getByText('Fema Status')).toBeInTheDocument()
      expect(screen.getByText('approved')).toBeInTheDocument()
      expect(screen.getByText('FEMA-1234')).toBeInTheDocument()
    })
  })

  test('handles API error responses', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should still render the page without crashing
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
  })

  test('handles network errors', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should still render the page without crashing
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
  })

  test('handles malformed API response', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => {
        throw new Error('Invalid JSON')
      },
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should still render the page without crashing
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
  })

  test('handles missing data fields in API response', async () => {
    const incompleteData = {
      timestamp: '2025-06-23T10:00:00Z',
      data: [
        {
          api: 'fema_status',
          // Missing required fields
        }
      ],
      status: 'success',
      total_apis: 1
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => incompleteData,
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should still render the API card
    await waitFor(() => {
      expect(screen.getByText('Fema Status')).toBeInTheDocument()
    })
  })

  test('handles different API data types correctly', async () => {
    const mockData = {
      timestamp: '2025-06-23T10:00:00Z',
      data: [
        {
          api: 'fema_status',
          status: 'pending',
          case_id: 'FEMA-5678',
          last_updated: '2025-06-23T09:00:00Z',
          estimated_completion: '2025-07-23T10:00:00Z',
          timestamp: '2025-06-23T10:00:00Z'
        },
        {
          api: 'weather_data',
          location: 'Test Location',
          temperature: 85,
          conditions: 'stormy',
          wind_speed: 25,
          timestamp: '2025-06-23T10:00:00Z'
        },
        {
          api: 'compliance_alerts',
          alert_type: 'document_missing',
          priority: 'low',
          message: 'Document required',
          case_id: 'CASE-789',
          timestamp: '2025-06-23T10:00:00Z'
        }
      ],
      status: 'success',
      total_apis: 3
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockData,
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    // Check FEMA data
    await waitFor(() => {
      expect(screen.getByText('pending')).toBeInTheDocument()
      expect(screen.getByText('FEMA-5678')).toBeInTheDocument()
    })

    // Check Weather data
    expect(screen.getByText('85°F')).toBeInTheDocument()
    expect(screen.getByText('stormy')).toBeInTheDocument()
    expect(screen.getByText('25 mph')).toBeInTheDocument()

    // Check Compliance data
    expect(screen.getByText('document_missing')).toBeInTheDocument()
    expect(screen.getByText('low')).toBeInTheDocument()
    expect(screen.getByText('Document required')).toBeInTheDocument()
  })

  test('updates last update timestamp after successful fetch', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        timestamp: '2025-06-23T10:00:00Z',
        data: [],
        status: 'success',
        total_apis: 0
      }),
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument()
    })
  })

  test('handles API response with empty data array', async () => {
    const emptyData = {
      timestamp: '2025-06-23T10:00:00Z',
      data: [],
      status: 'success',
      total_apis: 0
    }

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => emptyData,
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should still render the dashboard
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
    
    // But no API cards should be present
    expect(screen.queryByText('Fema Status')).not.toBeInTheDocument()
    expect(screen.queryByText('Weather Data')).not.toBeInTheDocument()
    expect(screen.queryByText('Compliance Alerts')).not.toBeInTheDocument()
  })

  test('handles API timeout scenarios', async () => {
    // Mock a delayed response
    mockFetch.mockImplementationOnce(() => 
      new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ok: true,
            json: async () => ({
              timestamp: '2025-06-23T10:00:00Z',
              data: [],
              status: 'success',
              total_apis: 0
            }),
          } as Response)
        }, 100)
      })
    )

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should eventually load
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalled()
    }, { timeout: 200 })
  })

  test('refresh button triggers new API call', async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        timestamp: '2025-06-23T10:00:00Z',
        data: [],
        status: 'success',
        total_apis: 0
      }),
    } as Response)

    await act(async () => {
      render(<RealtimePage />)
    })

    // Wait for initial load
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    // Click refresh
    const refreshButton = screen.getByText('Refresh Data')
    await act(async () => {
      refreshButton.click()
    })

    // Should make another API call
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })
  })
})

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax | FEMA PA Compliance Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'data-flow': 'dataFlow 15s linear infinite',
                        'flood-sweep': 'floodSweep 25s ease-in-out infinite',
                    },
                    keyframes: {
                        dataFlow: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(100%)' }
                        },
                        floodSweep: {
                            '0%': { backgroundPosition: '0% 0%' },
                            '25%': { backgroundPosition: '100% 20%' },
                            '50%': { backgroundPosition: '200% 40%' },
                            '75%': { backgroundPosition: '300% 20%' },
                            '100%': { backgroundPosition: '400% 0%' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .disaster-bg {
            background: 
                /* Hurricane/Disaster Pattern */
                linear-gradient(120deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.98) 40%, rgba(15, 23, 42, 0.95) 100%),
                /* Flooding Effect */
                repeating-linear-gradient(
                    90deg,
                    transparent 0px,
                    rgba(59, 130, 246, 0.015) 1px,
                    rgba(59, 130, 246, 0.025) 2px,
                    transparent 3px,
                    transparent 20px
                ),
                /* Grid Pattern */
                linear-gradient(0deg, rgba(71, 85, 105, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(71, 85, 105, 0.02) 1px, transparent 1px);
            background-size: 100% 100%, 30px 30px, 20px 20px, 20px 20px;
            animation: flood-sweep 25s ease-in-out infinite;
        }
        
        .technical-panel {
            background: rgba(15, 23, 42, 0.85);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 2px;
            backdrop-filter: blur(4px);
        }
        
        .data-indicator {
            position: relative;
            overflow: hidden;
        }
        
        .data-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
            animation: data-flow 3s infinite;
        }
        
        .angular-card {
            border-radius: 2px;
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(71, 85, 105, 0.25);
            transition: all 0.2s ease;
        }
        
        .angular-card:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(71, 85, 105, 0.4);
            transform: translateY(-1px);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .status-item {
            background: rgba(15, 23, 42, 0.7);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 2px;
            padding: 0.75rem;
            position: relative;
        }
    </style>
</head>
<body class="min-h-screen disaster-bg text-slate-100">

    <!-- Technical Navigation -->
    <nav class="relative z-50 px-6 py-3 technical-panel border-b border-slate-700/30">
        <div class="mx-auto max-w-7xl flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="h-8 w-8 bg-slate-700 border border-slate-600 flex items-center justify-center">
                    <span class="text-white font-mono text-xs font-bold">CM</span>
                </div>
                <div>
                    <span class="text-lg font-semibold text-white">ComplianceMax</span>
                    <span class="ml-2 px-2 py-0.5 text-xs font-mono bg-slate-700/50 text-slate-300 border border-slate-600/50">
                        v74.0-ENTERPRISE
                    </span>
                </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-6">
                <a href="#" class="text-slate-300 hover:text-white transition-colors text-sm font-medium">
                    System Home
                </a>
                <a href="#" class="text-slate-300 hover:text-white transition-colors text-sm font-medium">
                    New Analysis
                </a>
                <span class="text-slate-500 text-sm font-medium cursor-not-allowed">
                    Wizard Module [LOCKED]
                </span>
                <a href="#" class="text-slate-300 hover:text-white transition-colors text-sm font-medium">
                    Data Dashboard
                </a>
                <button class="px-3 py-1.5 text-white bg-slate-700 border border-slate-600 hover:bg-slate-600 transition-all duration-200 text-sm">
                    Client Access
                </button>
            </div>
        </div>
    </nav>

    <!-- Main System Interface -->
    <div id="mainContent">

        <!-- Primary Display Panel -->
        <div id="systemHome" class="active-panel">
            <!-- Header Section -->
            <div class="relative px-6 pt-12 pb-8">
                <div class="mx-auto max-w-6xl">
                    <div class="text-center">
                        <h1 class="text-3xl font-bold tracking-tight text-white sm:text-4xl lg:text-5xl mb-4">
                            FEMA PUBLIC ASSISTANCE
                            <span class="block text-slate-300 font-normal text-2xl sm:text-3xl lg:text-4xl">
                                Compliance Documentation System
                            </span>
                        </h1>
                        
                        <p class="mt-6 text-base leading-6 text-slate-400 max-w-3xl mx-auto">
                            Technical compliance management for FEMA Public Assistance program documentation. 
                            Systematic processing, regulatory analysis, and compliance verification services.
                        </p>
                        
                        <!-- System Status Grid -->
                        <div class="mt-8 status-grid max-w-4xl mx-auto">
                            <div class="status-item data-indicator">
                                <div class="flex items-center justify-between">
                                    <span class="text-xs font-mono text-slate-400">SYSTEM STATUS</span>
                                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                                </div>
                                <div class="mt-1 text-sm text-white">OPERATIONAL</div>
                            </div>
                            <div class="status-item data-indicator">
                                <div class="flex items-center justify-between">
                                    <span class="text-xs font-mono text-slate-400">ACTIVE DISASTERS</span>
                                    <span class="w-2 h-2 bg-blue-400 rounded-full"></span>
                                </div>
                                <div class="mt-1 text-sm text-white">18 MONITORED</div>
                            </div>
                            <div class="status-item data-indicator">
                                <div class="flex items-center justify-between">
                                    <span class="text-xs font-mono text-slate-400">FEMA API</span>
                                    <span class="w-2 h-2 bg-cyan-400 rounded-full"></span>
                                </div>
                                <div class="mt-1 text-sm text-white">LIVE FEED</div>
                            </div>
                        </div>
                        
                        <!-- Access Controls -->
                        <div class="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4">
                            <button class="inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-white bg-slate-700 border border-slate-600 hover:bg-slate-600 transition-all duration-200">
                                CLIENT PORTAL ACCESS
                                <span class="ml-2 text-xs">→</span>
                            </button>
                            <button class="inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-slate-300 border border-slate-600 hover:text-white hover:border-slate-500 hover:bg-slate-800/50 transition-all duration-200">
                                SERVICE SPECIFICATIONS
                                <span class="ml-2 text-xs">→</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Modules Grid -->
            <div class="relative px-6 py-16 technical-panel border-y border-slate-700/30">
                <div class="mx-auto max-w-6xl">
                    <div class="text-center mb-12">
                        <h2 class="text-2xl font-bold text-white mb-3">
                            FEMA PA COMPLIANCE MODULES
                        </h2>
                        <p class="text-sm text-slate-400 max-w-3xl mx-auto">
                            Specialized compliance processing across all Public Assistance program categories 
                            with automated regulatory verification and documentation standards.
                        </p>
                    </div>

                    <!-- Technical Service Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- PA Compliance -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">PA COMPLIANCE</h3>
                                <div class="w-1.5 h-1.5 bg-blue-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                End-to-end Public Assistance program compliance verification and technical guidance.
                            </p>
                        </div>

                        <!-- Cost Analysis -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">COST ANALYSIS</h3>
                                <div class="w-1.5 h-1.5 bg-cyan-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                Financial documentation review, cost reasonableness analysis, and compliance verification.
                            </p>
                        </div>

                        <!-- Codes & Standards -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">CODES & STANDARDS</h3>
                                <div class="w-1.5 h-1.5 bg-green-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                Building codes compliance verification, construction standards analysis.
                            </p>
                        </div>

                        <!-- EHP Module -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">EHP MODULE</h3>
                                <div class="w-1.5 h-1.5 bg-purple-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                Environmental & Historic Preservation compliance documentation and processing.
                            </p>
                        </div>

                        <!-- Mitigation -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">MITIGATION</h3>
                                <div class="w-1.5 h-1.5 bg-yellow-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                Hazard mitigation planning, implementation tracking, and compliance support.
                            </p>
                        </div>

                        <!-- Policy Engine -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">POLICY ENGINE</h3>
                                <div class="w-1.5 h-1.5 bg-red-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                PAPPG interpretation, regulatory application, and policy guidance systems.
                            </p>
                        </div>

                        <!-- Appeals Processing -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">APPEALS</h3>
                                <div class="w-1.5 h-1.5 bg-orange-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                Appeals documentation preparation, case analysis, and representation support.
                            </p>
                        </div>

                        <!-- System Integration -->
                        <div class="angular-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white">INTEGRATION</h3>
                                <div class="w-1.5 h-1.5 bg-indigo-400 rounded-sm"></div>
                            </div>
                            <p class="text-xs text-slate-400 leading-relaxed">
                                Cross-platform integration, data synchronization, and workflow automation.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="relative px-6 py-16">
                <div class="mx-auto max-w-4xl text-center">
                    <h2 class="text-xl font-bold text-white mb-4">
                        FEMA PA DOCUMENTATION PROCESSING SYSTEM
                    </h2>
                    <p class="text-sm text-slate-400 mb-8 leading-relaxed max-w-3xl mx-auto">
                        Professional compliance management system for FEMA Public Assistance applicants. 
                        Technical documentation processing, regulatory gap analysis, and systematic 
                        compliance verification to optimize program approval rates.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button class="inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-white bg-slate-700 border border-slate-600 hover:bg-slate-600 transition-all duration-200">
                            ACCESS CLIENT PORTAL
                            <span class="ml-2 text-xs">→</span>
                        </button>
                        <button class="inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-slate-300 border border-slate-600 hover:text-white hover:border-slate-500 hover:bg-slate-800/50 transition-all duration-200">
                            TECHNICAL SPECIFICATIONS
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Footer -->
            <footer class="relative px-6 py-8 border-t border-slate-700/30 technical-panel">
                <div class="mx-auto max-w-6xl">
                    <div class="flex flex-col md:flex-row items-center justify-between mb-6">
                        <div class="flex items-center space-x-3 mb-4 md:mb-0">
                            <div class="h-6 w-6 bg-slate-700 border border-slate-600"></div>
                            <span class="text-sm font-medium text-white">ComplianceMax</span>
                            <span class="text-xs font-mono text-slate-500">ENTERPRISE v74.0</span>
                        </div>
                        <div class="flex items-center space-x-6 text-xs text-slate-400">
                            <a href="#" class="hover:text-white transition-colors">System Documentation</a>
                            <a href="#" class="hover:text-white transition-colors">Service Level Agreement</a>
                            <a href="#" class="hover:text-white transition-colors">Technical Support</a>
                            <a href="#" class="hover:text-white transition-colors">API Reference</a>
                        </div>
                    </div>
                    <div class="text-center text-xs text-slate-500 border-t border-slate-700/30 pt-6">
                        © 2025 ComplianceMax Systems. FEMA Public Assistance compliance management platform.
                    </div>
                </div>
            </footer>
        </div>

    </div>

</body>
</html> 
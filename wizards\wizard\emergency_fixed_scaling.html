<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.8, maximum-scale=1.5">
    <title>Emergency Work - ComplianceMax V74</title>
    <style>
        /* UNIVERSAL SCALING FIX */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px;
            zoom: 1.0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            color: white;
            zoom: 1.0 !important;
            transform: scale(1.0);
            transform-origin: top left;
            overflow-x: hidden;
        }

        /* FIXED NAVIGATION HEADER */
        .nav-header {
            background: rgba(0, 0, 0, 0.9);
            height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .nav-logo {
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        /* MAIN CONTENT WITH FIXED SCALING */
        .main-content {
            margin-top: 60px;
            padding: 32px 24px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
        }

        .page-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .page-title {
            font-size: 48px;
            font-weight: 800;
            margin-bottom: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-subtitle {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 32px;
        }

        /* EMERGENCY WORK CONTENT */
        .emergency-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .category-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .category-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        .category-icon {
            font-size: 32px;
            margin-bottom: 16px;
            display: block;
        }

        .category-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .category-desc {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.5;
        }

        /* FORM STYLES */
        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            backdrop-filter: blur(5px);
        }

        .form-input::placeholder,
        .form-textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
        }

        /* BUTTONS */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* NOTIFICATION SYSTEM */
        .notification-container {
            position: fixed;
            top: 80px;
            right: 24px;
            z-index: 1100;
            max-width: 400px;
        }

        .notification {
            background: rgba(255, 255, 255, 0.95);
            color: #1f2937;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success {
            border-left: 4px solid #10b981;
        }

        .notification.error {
            border-left: 4px solid #ef4444;
        }

        .notification.warning {
            border-left: 4px solid #f39c12;
        }

        /* RESPONSIVE DESIGN */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .main-content {
                padding: 16px;
            }

            .page-title {
                font-size: 32px;
            }

            .page-subtitle {
                font-size: 16px;
            }

            .emergency-container {
                padding: 20px;
            }

            .category-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }

        /* PRINT STYLES */
        @media print {
            .nav-header,
            .notification-container {
                display: none !important;
            }

            .main-content {
                margin-top: 0 !important;
                padding: 0 !important;
            }

            body {
                background: white !important;
                color: black !important;
            }
        }
    </style>
</head>
<body>
    <!-- Fixed Navigation Header -->
    <nav class="nav-header">
        <a href="/" class="nav-logo">
            <span>🚨</span>
            <span>ComplianceMax V74 - Emergency Work</span>
        </a>
        
        <div class="nav-menu">
            <a href="/" class="nav-item">🏠 Home</a>
            <a href="/dashboard" class="nav-item">📊 Dashboard</a>
            <a href="/emergency" class="nav-item active">🚨 Emergency</a>
            <a href="/cbcs" class="nav-item">🏗️ CBCS</a>
            <a href="/professional-intake" class="nav-item">📋 Professional</a>
            <a href="/logout" class="nav-item">🚪 Logout</a>
        </div>
    </nav>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Emergency Work</h1>
            <p class="page-subtitle">Categories A & B - Debris Removal and Emergency Protective Measures</p>
        </div>

        <div class="emergency-container">
            <div class="category-grid">
                <!-- Category A -->
                <div class="category-card" onclick="selectCategory('A')">
                    <span class="category-icon">🗑️</span>
                    <h3 class="category-title">Category A</h3>
                    <p class="category-desc">Debris removal from public roads, bridges, and facilities</p>
                </div>

                <!-- Category B -->
                <div class="category-card" onclick="selectCategory('B')">
                    <span class="category-icon">🛡️</span>
                    <h3 class="category-title">Category B</h3>
                    <p class="category-desc">Emergency protective measures to save lives and protect property</p>
                </div>
            </div>

            <!-- Emergency Work Form -->
            <form id="emergencyForm" style="display: none;">
                <div class="form-group">
                    <label class="form-label" for="workCategory">Work Category</label>
                    <select class="form-select" id="workCategory" name="workCategory">
                        <option value="">Select Category</option>
                        <option value="A">Category A - Debris Removal</option>
                        <option value="B">Category B - Emergency Protective Measures</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="workDescription">Work Description</label>
                    <textarea class="form-textarea" id="workDescription" name="workDescription" rows="4" 
                              placeholder="Describe the emergency work being performed..."></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label" for="urgencyLevel">Urgency Level</label>
                    <select class="form-select" id="urgencyLevel" name="urgencyLevel">
                        <option value="immediate">Immediate - Life Safety</option>
                        <option value="urgent">Urgent - Property Protection</option>
                        <option value="standard">Standard - Planned Response</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="incidentDate">Incident Date</label>
                    <input type="date" class="form-input" id="incidentDate" name="incidentDate">
                </div>

                <div class="form-group">
                    <label class="form-label" for="estimatedCost">Estimated Cost ($)</label>
                    <input type="number" class="form-input" id="estimatedCost" name="estimatedCost" 
                           placeholder="Enter estimated cost">
                </div>

                <div style="display: flex; gap: 16px; justify-content: center; margin-top: 32px;">
                    <button type="submit" class="btn btn-primary">
                        <span>📋</span>
                        Submit Emergency Work
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        <span>🔄</span>
                        Reset Form
                    </button>
                </div>
            </form>
        </div>
    </main>

    <script>
        // Show notification function
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 4px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                <div>${message}</div>
            `;
            
            container.appendChild(notification);
            
            // Animate in
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => container.removeChild(notification), 300);
            }, 5000);
        }

        // Category selection
        function selectCategory(category) {
            document.getElementById('workCategory').value = category;
            document.getElementById('emergencyForm').style.display = 'block';
            
            // Scroll to form
            document.getElementById('emergencyForm').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
            
            showNotification(`Category ${category} selected. Please fill out the form below.`, 'success');
        }

        // Form submission
        document.getElementById('emergencyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            // Validate required fields
            if (!data.workCategory || !data.workDescription) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }
            
            // Show loading state
            showNotification('Submitting emergency work request...', 'info');
            
            // Simulate API call
            fetch('/api/intake/emergency', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...data,
                    start_wizard: true,
                    timestamp: new Date().toISOString()
                })
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    showNotification('Emergency work submitted successfully!', 'success');
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } else {
                    showNotification(result.message || 'Submission failed. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Submission error:', error);
                showNotification('Network error. Please check your connection and try again.', 'error');
            });
        });

        // Reset form
        function resetForm() {
            document.getElementById('emergencyForm').reset();
            document.getElementById('emergencyForm').style.display = 'none';
            showNotification('Form reset. Please select a category to start over.', 'info');
        }

        // Force viewport settings on load
        window.addEventListener('load', function() {
            // Set proper viewport
            let viewport = document.querySelector('meta[name=viewport]');
            if (viewport) {
                viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.8, maximum-scale=1.5';
            }
            
            // Force zoom reset
            document.body.style.zoom = '1.0';
            document.documentElement.style.zoom = '1.0';
            
            // Show welcome message
            setTimeout(() => {
                showNotification('Emergency Work module loaded. Select a category to begin.', 'success');
            }, 1000);
        });
    </script>
</body>
</html> 
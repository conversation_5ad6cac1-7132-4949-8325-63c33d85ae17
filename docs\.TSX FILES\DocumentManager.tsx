
'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Upload, 
  Search, 
  Filter, 
  Download, 
  Trash2, 
  Eye,
  FileText,
  Image,
  File,
  Plus,
  Grid,
  List,
  CheckCircle,
  Clock,
  Database
} from 'lucide-react'
import { DocumentUpload } from './DocumentUpload'
import { DocumentList } from './DocumentList'
import { DocumentGrid } from './DocumentGrid'
import { formatFileSize, formatDate } from '@/lib/utils'

interface Document {
  id: string
  name: string
  type: string
  size: number
  uploadedAt: Date
  category: string
  status: 'processing' | 'completed' | 'error'
  tags: string[]
  description?: string
}

const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'Project Scope Statement.pdf',
    type: 'application/pdf',
    size: 2048576,
    uploadedAt: new Date('2025-06-20'),
    category: 'Project Documentation',
    status: 'completed',
    tags: ['scope', 'requirements'],
    description: 'Detailed project scope and requirements document'
  },
  {
    id: '2',
    name: 'Cost Estimate Analysis.xlsx',
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    size: 1536000,
    uploadedAt: new Date('2025-06-19'),
    category: 'Financial',
    status: 'completed',
    tags: ['cost', 'budget', 'analysis'],
    description: 'Comprehensive cost analysis and budget breakdown'
  },
  {
    id: '3',
    name: 'Environmental Assessment.docx',
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    size: 3072000,
    uploadedAt: new Date('2025-06-18'),
    category: 'Environmental',
    status: 'processing',
    tags: ['environmental', 'assessment', 'nepa'],
    description: 'Environmental impact assessment report'
  },
  {
    id: '4',
    name: 'Damage Photos.zip',
    type: 'application/zip',
    size: 15728640,
    uploadedAt: new Date('2025-06-17'),
    category: 'Evidence',
    status: 'completed',
    tags: ['photos', 'damage', 'evidence'],
    description: 'Collection of damage assessment photographs'
  }
]

const categories = ['All', 'Project Documentation', 'Financial', 'Environmental', 'Evidence', 'Legal']
const statusOptions = ['All', 'Processing', 'Completed', 'Error']

export function DocumentManager() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [selectedStatus, setSelectedStatus] = useState('All')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showUpload, setShowUpload] = useState(false)
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set())

  useEffect(() => {
    // Simulate loading documents
    const timer = setTimeout(() => {
      setDocuments(mockDocuments)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    let filtered = documents

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(doc => doc.category === selectedCategory)
    }

    if (selectedStatus !== 'All') {
      filtered = filtered.filter(doc => doc.status === selectedStatus.toLowerCase())
    }

    if (searchTerm) {
      filtered = filtered.filter(doc =>
        doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    setFilteredDocuments(filtered)
  }, [documents, searchTerm, selectedCategory, selectedStatus])

  const handleDocumentUpload = (newDocuments: Document[]) => {
    setDocuments(prev => [...prev, ...newDocuments])
    setShowUpload(false)
  }

  const handleDocumentDelete = (documentId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== documentId))
    setSelectedDocuments(prev => {
      const newSet = new Set(prev)
      newSet.delete(documentId)
      return newSet
    })
  }

  const handleBulkDelete = () => {
    setDocuments(prev => prev.filter(doc => !selectedDocuments.has(doc.id)))
    setSelectedDocuments(new Set())
  }

  const handleSelectDocument = (documentId: string) => {
    setSelectedDocuments(prev => {
      const newSet = new Set(prev)
      if (newSet.has(documentId)) {
        newSet.delete(documentId)
      } else {
        newSet.add(documentId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (selectedDocuments.size === filteredDocuments.length) {
      setSelectedDocuments(new Set())
    } else {
      setSelectedDocuments(new Set(filteredDocuments.map(doc => doc.id)))
    }
  }

  const getDocumentStats = () => {
    const total = documents.length
    const completed = documents.filter(doc => doc.status === 'completed').length
    const processing = documents.filter(doc => doc.status === 'processing').length
    const totalSize = documents.reduce((sum, doc) => sum + doc.size, 0)

    return { total, completed, processing, totalSize }
  }

  const stats = getDocumentStats()

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Document Management</h1>
          <p className="text-white/70 mt-2">
            Manage and organize your compliance documents
          </p>
        </div>
        <Button
          onClick={() => setShowUpload(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Upload Documents
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Total Documents</p>
                <p className="text-2xl font-bold text-white">{stats.total}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Completed</p>
                <p className="text-2xl font-bold text-white">{stats.completed}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Processing</p>
                <p className="text-2xl font-bold text-white">{stats.processing}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Total Size</p>
                <p className="text-2xl font-bold text-white">
                  {formatFileSize(stats.totalSize)}
                </p>
              </div>
              <Database className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="glass border-white/20">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60" />
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category} className="bg-gray-800">
                  {category}
                </option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {statusOptions.map(status => (
                <option key={status} value={status} className="bg-gray-800">
                  {status}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className={viewMode === 'grid' ? '' : 'border-white/20 text-white hover:bg-white/10'}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                className={viewMode === 'list' ? '' : 'border-white/20 text-white hover:bg-white/10'}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedDocuments.size > 0 && (
            <div className="flex items-center gap-4 mt-4 pt-4 border-t border-white/20">
              <span className="text-white/70 text-sm">
                {selectedDocuments.size} document(s) selected
              </span>
              <Button
                onClick={handleBulkDelete}
                variant="outline"
                size="sm"
                className="border-red-400/20 text-red-400 hover:bg-red-400/10"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Selected
              </Button>
              <Button
                onClick={() => setSelectedDocuments(new Set())}
                variant="outline"
                size="sm"
                className="border-white/20 text-white hover:bg-white/10"
              >
                Clear Selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Document Display */}
      {viewMode === 'grid' ? (
        <DocumentGrid
          documents={filteredDocuments}
          selectedDocuments={selectedDocuments}
          onSelectDocument={handleSelectDocument}
          onSelectAll={handleSelectAll}
          onDeleteDocument={handleDocumentDelete}
        />
      ) : (
        <DocumentList
          documents={filteredDocuments}
          selectedDocuments={selectedDocuments}
          onSelectDocument={handleSelectDocument}
          onSelectAll={handleSelectAll}
          onDeleteDocument={handleDocumentDelete}
        />
      )}

      {/* Upload Modal */}
      {showUpload && (
        <DocumentUpload
          onClose={() => setShowUpload(false)}
          onUpload={handleDocumentUpload}
        />
      )}
    </div>
  )
}


import { render, screen, fireEvent, waitFor, act } from '@/test-utils/test-utils'
import RealtimePage from '@/app/realtime/page'

// Mock fetch responses
const mockRealtimeData = {
  timestamp: '2025-06-23T10:00:00Z',
  data: [
    {
      api: 'fema_status',
      status: 'approved',
      case_id: 'FEMA-1234',
      last_updated: '2025-06-23T09:00:00Z',
      estimated_completion: '2025-07-23T10:00:00Z',
      timestamp: '2025-06-23T10:00:00Z'
    },
    {
      api: 'weather_data',
      location: 'Disaster Zone',
      temperature: 75,
      conditions: 'clear',
      wind_speed: 10,
      timestamp: '2025-06-23T10:00:00Z'
    },
    {
      api: 'compliance_alerts',
      alert_type: 'deadline_approaching',
      priority: 'high',
      message: 'Test alert message',
      case_id: 'CASE-123',
      timestamp: '2025-06-23T10:00:00Z'
    }
  ],
  status: 'success',
  total_apis: 3
}

// Mock WebSocket class
class MockWebSocket {
  url: string
  onopen: (() => void) | null = null
  onmessage: ((event: { data: string }) => void) | null = null
  onclose: (() => void) | null = null
  onerror: ((error: any) => void) | null = null
  readyState: number = MockWebSocket.CONNECTING

  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  constructor(url: string) {
    this.url = url
    this.readyState = MockWebSocket.CONNECTING
    
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      if (this.onopen) this.onopen()
    }, 100)
  }

  send(data: string) {
    // Mock send functionality
  }

  close() {
    this.readyState = MockWebSocket.CLOSED
    if (this.onclose) this.onclose()
  }

  // Method to simulate receiving a message
  simulateMessage(data: any) {
    if (this.onmessage) {
      this.onmessage({ data: JSON.stringify(data) })
    }
  }
}

describe('RealtimePage', () => {
  let mockFetch: jest.MockedFunction<typeof fetch>
  let mockWebSocket: MockWebSocket

  beforeEach(() => {
    // Reset mocks
    mockFetch = global.fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockClear()
    
    // Mock successful fetch response
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockRealtimeData,
    } as Response)

    // Mock WebSocket
    global.WebSocket = jest.fn().mockImplementation((url: string) => {
      mockWebSocket = new MockWebSocket(url)
      return mockWebSocket
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('renders realtime dashboard with correct title', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Live data from external APIs and compliance alerts')).toBeInTheDocument()
  })

  test('fetches and displays realtime data on mount', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('http://localhost:8001/api/realtime/data')
    })

    // Check if API data cards are rendered
    await waitFor(() => {
      expect(screen.getByText('Fema Status')).toBeInTheDocument()
      expect(screen.getByText('Weather Data')).toBeInTheDocument()
      expect(screen.getByText('Compliance Alerts')).toBeInTheDocument()
    })
  })

  test('displays FEMA status data correctly', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(screen.getByText('approved')).toBeInTheDocument()
      expect(screen.getByText('FEMA-1234')).toBeInTheDocument()
    })
  })

  test('displays weather data correctly', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(screen.getByText('75°F')).toBeInTheDocument()
      expect(screen.getByText('clear')).toBeInTheDocument()
      expect(screen.getByText('10 mph')).toBeInTheDocument()
    })
  })

  test('displays compliance alerts data correctly', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(screen.getByText('deadline_approaching')).toBeInTheDocument()
      expect(screen.getByText('high')).toBeInTheDocument()
    })
  })

  test('WebSocket connection button functionality', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    
    await act(async () => {
      fireEvent.click(connectButton)
    })

    // Wait for WebSocket to "connect"
    await waitFor(() => {
      expect(screen.getByText('Disconnect WebSocket')).toBeInTheDocument()
    }, { timeout: 200 })
  })

  test('WebSocket receives and displays alerts', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    // Connect WebSocket
    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    // Wait for connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    // Simulate receiving an alert
    const alertData = {
      type: 'alert',
      data: {
        api: 'compliance_alerts',
        alert_type: 'document_missing',
        priority: 'medium',
        message: 'Document upload required',
        case_id: 'CASE-456',
        timestamp: '2025-06-23T10:05:00Z'
      },
      timestamp: '2025-06-23T10:05:00Z'
    }

    await act(async () => {
      mockWebSocket.simulateMessage(alertData)
    })

    // Check if alert appears in the alerts section
    await waitFor(() => {
      expect(screen.getByText('document_missing')).toBeInTheDocument()
      expect(screen.getByText('Document upload required')).toBeInTheDocument()
    })
  })

  test('refresh data button works', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const refreshButton = screen.getByText('Refresh Data')
    
    await act(async () => {
      fireEvent.click(refreshButton)
    })

    // Should make another fetch call
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })
  })

  test('handles fetch error gracefully', async () => {
    // Mock fetch to reject
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    await act(async () => {
      render(<RealtimePage />)
    })

    // Should still render the page without crashing
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
  })

  test('displays connection status correctly', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    // Initially disconnected
    expect(screen.getByText('Disconnected')).toBeInTheDocument()

    // Connect WebSocket
    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    // Should show connected status
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })
  })

  test('WebSocket disconnect functionality', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    // Connect first
    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    // Now disconnect
    const disconnectButton = screen.getByText('Disconnect WebSocket')
    await act(async () => {
      fireEvent.click(disconnectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Disconnected')).toBeInTheDocument()
    })
  })

  test('displays last update timestamp', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument()
    })
  })

  test('handles WebSocket realtime updates', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    // Connect WebSocket
    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    // Simulate receiving a realtime update
    const updateData = {
      type: 'realtime_update',
      data: {
        ...mockRealtimeData,
        data: [
          {
            ...mockRealtimeData.data[0],
            status: 'pending'
          },
          ...mockRealtimeData.data.slice(1)
        ]
      },
      timestamp: '2025-06-23T10:10:00Z'
    }

    await act(async () => {
      mockWebSocket.simulateMessage(updateData)
    })

    // Should update the displayed data
    await waitFor(() => {
      expect(screen.getByText('pending')).toBeInTheDocument()
    })
  })
})

import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Http as HttpIcon,
  Email as EmailIcon,
  Storage as DatabaseIcon,
  Folder as FileIcon,
  Notifications as NotificationIcon,
  Code as ConditionIcon,
} from '@mui/icons-material';
import { useDrag } from 'react-dnd';
import { ItemTypes } from '../../types/dnd';
import { ActionType } from '../../types/workflow';

interface StepType {
  type: string;
  actionType?: ActionType;
  icon: React.ReactNode;
  label: string;
  description: string;
}

const stepTypes: StepType[] = [
  {
    type: 'action',
    actionType: 'http',
    icon: <HttpIcon />,
    label: 'HTTP Request',
    description: 'Make HTTP requests to external services',
  },
  {
    type: 'action',
    actionType: 'email',
    icon: <EmailIcon />,
    label: 'Send Email',
    description: 'Send emails to specified recipients',
  },
  {
    type: 'action',
    actionType: 'database',
    icon: <DatabaseIcon />,
    label: 'Database Operation',
    description: 'Execute database queries and operations',
  },
  {
    type: 'action',
    actionType: 'file',
    icon: <FileIcon />,
    label: 'File Operation',
    description: 'Perform file system operations',
  },
  {
    type: 'action',
    actionType: 'notification',
    icon: <NotificationIcon />,
    label: 'Send Notification',
    description: 'Send notifications to various channels',
  },
  {
    type: 'condition',
    icon: <ConditionIcon />,
    label: 'Condition',
    description: 'Add conditional logic to your workflow',
  },
];

const StepPalette: React.FC = () => {
  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Step Types
      </Typography>
      <Grid container spacing={2}>
        {stepTypes.map((stepType) => (
          <Grid item xs={12} key={stepType.label}>
            <StepTypeItem {...stepType} />
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

interface StepTypeItemProps extends StepType {}

const StepTypeItem: React.FC<StepTypeItemProps> = ({
  type,
  actionType,
  icon,
  label,
  description,
}) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.STEP,
    item: { type, actionType },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  return (
    <Paper
      ref={drag}
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        opacity: isDragging ? 0.5 : 1,
        cursor: 'move',
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      }}
    >
      <IconButton size="small" disabled>
        {icon}
      </IconButton>
      <Box>
        <Typography variant="subtitle2">{label}</Typography>
        <Typography variant="caption" color="textSecondary">
          {description}
        </Typography>
      </Box>
    </Paper>
  );
};

export default StepPalette; 
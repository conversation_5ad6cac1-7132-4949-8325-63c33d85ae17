import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab';
import { useNavigate } from 'react-router-dom';
import { api } from '../../services/api';
import { WorkflowAnalytics } from '../../types/workflow';

const Dashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<WorkflowAnalytics[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const response = await api.get('/workflows/performance?limit=5');
        setAnalytics(response.data);
      } catch (err) {
        setError('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Performance Overview */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Top Performing Workflows
            </Typography>
            <Timeline>
              {analytics.map((workflow) => (
                <TimelineItem key={workflow.id}>
                  <TimelineSeparator>
                    <TimelineDot
                      color={
                        workflow.metrics.success_rate >= 0.9
                          ? 'success'
                          : workflow.metrics.success_rate >= 0.7
                          ? 'warning'
                          : 'error'
                      }
                    />
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <Typography
                      variant="subtitle1"
                      sx={{ cursor: 'pointer' }}
                      onClick={() => navigate(`/workflows/${workflow.id}`)}
                    >
                      {workflow.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate: {(workflow.metrics.success_rate * 100).toFixed(1)}%
                    </Typography>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </Paper>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            <Timeline>
              {analytics.map((workflow) => (
                <TimelineItem key={workflow.id}>
                  <TimelineSeparator>
                    <TimelineDot color="primary" />
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <Typography variant="subtitle1">{workflow.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last executed: {new Date(workflow.last_executed_at).toLocaleString()}
                    </Typography>
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </Paper>
        </Grid>

        {/* Performance Metrics */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Performance Metrics
            </Typography>
            <Grid container spacing={2}>
              {analytics.map((workflow) => (
                <Grid item xs={12} sm={6} md={4} key={workflow.id}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1">{workflow.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Executions: {workflow.metrics.total_executions}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Average Duration: {workflow.metrics.avg_duration.toFixed(2)}s
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Error Rate: {(workflow.metrics.error_rate * 100).toFixed(1)}%
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard; 
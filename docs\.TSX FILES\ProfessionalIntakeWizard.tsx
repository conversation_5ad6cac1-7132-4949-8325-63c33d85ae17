// Step 1: Enhanced CBCS-compliant Professional Intake Wizard (with grid layout)
// File: frontend/src/components/ProfessionalIntakeWizard.tsx

'use client'

import { useState } from 'react'
import axios from 'axios'

const applicantTypes = ['Local Government', 'State Agency', 'Tribal Nation', 'Private Nonprofit']
const projectTypes = ['Emergency Work', 'Permanent Work']
const damageCategories = ['A - Debris Removal', 'B - Emergency Protective Measures', 'C - Roads & Bridges', 'D - Water Control Facilities', 'E - Buildings & Equipment', 'F - Utilities', 'G - Parks, Rec, Other']
const costBases = ['RSMeans', 'Local Estimate', 'Contract Quote']

export default function ProfessionalIntakeWizard() {
  const [form, setForm] = useState({
    applicantName: '',
    applicantType: '',
    contactPerson: '',
    contactEmail: '',
    contactPhone: '',
    projectTitle: '',
    projectType: '',
    damageCategory: '',
    incidentDate: '',
    location: '',
    description: '',
    estimatedCost: 0,
    costBasis: '',
    hasInsurance: '',
    insuranceAmount: 0,
    documentsAvailable: [],
    additionalNotes: '',
    submissionTimestamp: new Date().toISOString()
  })
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e: any) => {
    const { name, value } = e.target
    setForm(prev => ({ ...prev, [name]: value }))
  }

  const handleListChange = (e: any) => {
    const { name, value } = e.target
    setForm(prev => ({ ...prev, [name]: value.split(',').map((s: string) => s.trim()) }))
  }

  const handleSubmit = async () => {
    try {
      await axios.post('http://localhost:8001/api/intake/submit', form)
      setSubmitted(true)
    } catch (e) {
      console.error(e)
      setError('Submission failed.')
    }
  }

  if (submitted) {
    return <div className="p-4 text-green-600">✅ Submission successful.</div>
  }

  return (
    <div className="max-w-6xl mx-auto bg-white rounded-xl shadow-md p-8">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">🛠️ Professional Intake Wizard</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <input name="applicantName" placeholder="Applicant Name" className="p-2 border rounded" onChange={handleChange} />

        <select name="applicantType" className="p-2 border rounded" onChange={handleChange}>
          <option value="">Select Applicant Type</option>
          {applicantTypes.map(type => <option key={type}>{type}</option>)}
        </select>

        <input name="contactPerson" placeholder="Contact Person" className="p-2 border rounded" onChange={handleChange} />
        <input name="contactEmail" placeholder="Contact Email" className="p-2 border rounded" onChange={handleChange} />
        <input name="contactPhone" placeholder="Contact Phone" className="p-2 border rounded" onChange={handleChange} />
        <input name="projectTitle" placeholder="Project Title" className="p-2 border rounded" onChange={handleChange} />

        <select name="projectType" className="p-2 border rounded" onChange={handleChange}>
          <option value="">Select Project Type</option>
          {projectTypes.map(type => <option key={type}>{type}</option>)}
        </select>

        <select name="damageCategory" className="p-2 border rounded" onChange={handleChange}>
          <option value="">Select Damage Category</option>
          {damageCategories.map(cat => <option key={cat}>{cat}</option>)}
        </select>

        <input name="incidentDate" type="date" className="p-2 border rounded" onChange={handleChange} />
        <input name="location" placeholder="Location" className="p-2 border rounded" onChange={handleChange} />

        <textarea name="description" placeholder="Project Description" className="p-2 border rounded col-span-full" onChange={handleChange} />

        <input name="estimatedCost" type="number" placeholder="Estimated Cost ($)" className="p-2 border rounded" onChange={handleChange} />

        <select name="costBasis" className="p-2 border rounded" onChange={handleChange}>
          <option value="">Select Cost Basis</option>
          {costBases.map(c => <option key={c}>{c}</option>)}
        </select>

        <select name="hasInsurance" className="p-2 border rounded" onChange={handleChange}>
          <option value="">Insurance Coverage?</option>
          <option value="Yes">Yes</option>
          <option value="No">No</option>
        </select>

        <input name="insuranceAmount" type="number" placeholder="Insurance Amount ($)" className="p-2 border rounded" onChange={handleChange} />

        <textarea name="documentsAvailable" placeholder="List Documents (comma-separated)" className="p-2 border rounded" onChange={handleListChange} />
        <textarea name="additionalNotes" placeholder="Additional Notes" className="p-2 border rounded" onChange={handleChange} />
      </div>

      <div className="mt-6">
        <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700" onClick={handleSubmit}>Submit</button>
        {error && <p className="text-red-600 font-medium mt-2">{error}</p>}
      </div>
    </div>
  )
}

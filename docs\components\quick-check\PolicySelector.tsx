import React from 'react';
import {
  Paper,
  Typography,
  Box,
  RadioGroup,
  FormControlLabel,
  Radio,
  useTheme,
} from '@mui/material';

interface PolicySelectorProps {
  selectedPolicy: string | null;
  onPolicySelect: (policy: string) => void;
}

const policies = [
  {
    id: 'fema-pa-policy',
    name: 'FEMA Public Assistance Policy',
    description: 'General policies for Public Assistance grants',
  },
  {
    id: 'ehp-policy',
    name: 'Environmental and Historic Preservation',
    description: 'Environmental and historic preservation requirements',
  },
  {
    id: 'procurement-policy',
    name: 'Procurement Standards',
    description: 'Federal procurement standards and requirements',
  },
  {
    id: 'cost-principles',
    name: 'Cost Principles',
    description: 'Reasonable cost and eligibility requirements',
  },
];

const PolicySelector: React.FC<PolicySelectorProps> = ({
  selectedPolicy,
  onPolicySelect,
}) => {
  const theme = useTheme();

  return (
    <Paper
      sx={{
        p: 4,
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: theme.shape.borderRadius * 2,
      }}
    >
      <Typography
        variant="h6"
        sx={{ mb: 3, color: 'rgba(255, 255, 255, 0.9)' }}
      >
        Select Policy for Compliance Check
      </Typography>

      <RadioGroup
        value={selectedPolicy}
        onChange={(e) => onPolicySelect(e.target.value)}
      >
        {policies.map((policy) => (
          <FormControlLabel
            key={policy.id}
            value={policy.id}
            control={
              <Radio
                sx={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  '&.Mui-checked': {
                    color: theme.palette.primary.main,
                  },
                }}
              />
            }
            label={
              <Box sx={{ ml: 1 }}>
                <Typography sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
                  {policy.name}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                >
                  {policy.description}
                </Typography>
              </Box>
            }
            sx={{
              alignItems: 'flex-start',
              mb: 2,
              '&:last-child': { mb: 0 },
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.05)',
                borderRadius: theme.shape.borderRadius,
              },
            }}
          />
        ))}
      </RadioGroup>
    </Paper>
  );
};

export default PolicySelector;

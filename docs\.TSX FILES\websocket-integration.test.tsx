
import { render, screen, fireEvent, waitFor, act } from '@/test-utils/test-utils'
import RealtimePage from '@/app/realtime/page'

// Mock WebSocket for integration testing
class MockWebSocketIntegration {
  url: string
  onopen: (() => void) | null = null
  onmessage: ((event: { data: string }) => void) | null = null
  onclose: (() => void) | null = null
  onerror: ((error: any) => void) | null = null
  readyState: number = MockWebSocketIntegration.CONNECTING
  
  static instances: MockWebSocketIntegration[] = []
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  constructor(url: string) {
    this.url = url
    this.readyState = MockWebSocketIntegration.CONNECTING
    MockWebSocketIntegration.instances.push(this)
    
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = MockWebSocketIntegration.OPEN
      if (this.onopen) this.onopen()
    }, 50)
  }

  send(data: string) {
    // Mock send functionality
  }

  close() {
    this.readyState = MockWebSocketIntegration.CLOSED
    if (this.onclose) this.onclose()
    MockWebSocketIntegration.instances = MockWebSocketIntegration.instances.filter(ws => ws !== this)
  }

  simulateMessage(data: any) {
    if (this.onmessage && this.readyState === MockWebSocketIntegration.OPEN) {
      this.onmessage({ data: JSON.stringify(data) })
    }
  }

  simulateError(error: any) {
    if (this.onerror) {
      this.onerror(error)
    }
  }

  simulateClose() {
    this.readyState = MockWebSocketIntegration.CLOSED
    if (this.onclose) this.onclose()
  }

  static broadcastToAll(data: any) {
    this.instances.forEach(ws => {
      if (ws.readyState === MockWebSocketIntegration.OPEN) {
        ws.simulateMessage(data)
      }
    })
  }

  static closeAll() {
    this.instances.forEach(ws => ws.close())
    this.instances = []
  }
}

describe('WebSocket Integration Tests', () => {
  let mockFetch: jest.MockedFunction<typeof fetch>

  beforeEach(() => {
    // Reset WebSocket instances
    MockWebSocketIntegration.closeAll()
    
    // Mock fetch
    mockFetch = global.fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockClear()
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        timestamp: '2025-06-23T10:00:00Z',
        data: [],
        status: 'success',
        total_apis: 0
      }),
    } as Response)

    // Mock WebSocket
    global.WebSocket = jest.fn().mockImplementation((url: string) => {
      return new MockWebSocketIntegration(url)
    })
  })

  afterEach(() => {
    MockWebSocketIntegration.closeAll()
    jest.clearAllMocks()
  })

  test('establishes WebSocket connection with correct URL', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:8001/ws/realtime')
  })

  test('handles multiple WebSocket connections', async () => {
    // Render multiple instances
    const { unmount: unmount1 } = render(<RealtimePage />)
    const { unmount: unmount2 } = render(<RealtimePage />)

    // Connect both
    const connectButtons = screen.getAllByText('Connect WebSocket')
    
    await act(async () => {
      fireEvent.click(connectButtons[0])
      fireEvent.click(connectButtons[1])
    })

    // Should have 2 WebSocket instances
    expect(MockWebSocketIntegration.instances).toHaveLength(2)

    // Cleanup
    unmount1()
    unmount2()
  })

  test('handles WebSocket connection errors', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    // Wait for connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    // Simulate error
    const wsInstance = MockWebSocketIntegration.instances[0]
    await act(async () => {
      wsInstance.simulateError(new Error('Connection error'))
    })

    // Should handle error gracefully
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
  })

  test('handles WebSocket unexpected disconnection', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    // Simulate unexpected disconnection
    const wsInstance = MockWebSocketIntegration.instances[0]
    await act(async () => {
      wsInstance.simulateClose()
    })

    await waitFor(() => {
      expect(screen.getByText('Disconnected')).toBeInTheDocument()
    })
  })

  test('processes different types of WebSocket messages', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    const wsInstance = MockWebSocketIntegration.instances[0]

    // Test alert message
    await act(async () => {
      wsInstance.simulateMessage({
        type: 'alert',
        data: {
          api: 'compliance_alerts',
          alert_type: 'test_alert',
          priority: 'high',
          message: 'Test alert message',
          case_id: 'CASE-TEST',
          timestamp: '2025-06-23T10:00:00Z'
        },
        timestamp: '2025-06-23T10:00:00Z'
      })
    })

    await waitFor(() => {
      expect(screen.getByText('test_alert')).toBeInTheDocument()
    })

    // Test periodic update message
    await act(async () => {
      wsInstance.simulateMessage({
        type: 'periodic_update',
        data: {
          timestamp: '2025-06-23T10:05:00Z',
          data: [
            {
              api: 'fema_status',
              status: 'approved',
              case_id: 'FEMA-UPDATE',
              timestamp: '2025-06-23T10:05:00Z'
            }
          ],
          status: 'success',
          total_apis: 1
        },
        timestamp: '2025-06-23T10:05:00Z'
      })
    })

    await waitFor(() => {
      expect(screen.getByText('FEMA-UPDATE')).toBeInTheDocument()
    })
  })

  test('handles malformed WebSocket messages gracefully', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    const wsInstance = MockWebSocketIntegration.instances[0]

    // Send malformed JSON
    await act(async () => {
      if (wsInstance.onmessage) {
        wsInstance.onmessage({ data: 'invalid json' })
      }
    })

    // Should not crash the application
    expect(screen.getByText('Real-time Dashboard')).toBeInTheDocument()
  })

  test('maintains alert history correctly', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    const wsInstance = MockWebSocketIntegration.instances[0]

    // Send multiple alerts
    for (let i = 0; i < 3; i++) {
      await act(async () => {
        wsInstance.simulateMessage({
          type: 'alert',
          data: {
            api: 'compliance_alerts',
            alert_type: `alert_${i}`,
            priority: 'medium',
            message: `Alert message ${i}`,
            case_id: `CASE-${i}`,
            timestamp: `2025-06-23T10:0${i}:00Z`
          },
          timestamp: `2025-06-23T10:0${i}:00Z`
        })
      })
    }

    // All alerts should be visible
    await waitFor(() => {
      expect(screen.getByText('alert_0')).toBeInTheDocument()
      expect(screen.getByText('alert_1')).toBeInTheDocument()
      expect(screen.getByText('alert_2')).toBeInTheDocument()
    })
  })

  test('limits alert history to prevent memory issues', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    const wsInstance = MockWebSocketIntegration.instances[0]

    // Send more than 10 alerts (the limit in the component)
    for (let i = 0; i < 12; i++) {
      await act(async () => {
        wsInstance.simulateMessage({
          type: 'alert',
          data: {
            api: 'compliance_alerts',
            alert_type: `alert_${i}`,
            priority: 'low',
            message: `Alert message ${i}`,
            case_id: `CASE-${i}`,
            timestamp: `2025-06-23T10:${i.toString().padStart(2, '0')}:00Z`
          },
          timestamp: `2025-06-23T10:${i.toString().padStart(2, '0')}:00Z`
        })
      })
    }

    // First two alerts should not be visible (only last 10 kept)
    await waitFor(() => {
      expect(screen.queryByText('alert_0')).not.toBeInTheDocument()
      expect(screen.queryByText('alert_1')).not.toBeInTheDocument()
      expect(screen.getByText('alert_11')).toBeInTheDocument()
    })
  })

  test('WebSocket reconnection behavior', async () => {
    await act(async () => {
      render(<RealtimePage />)
    })

    const connectButton = screen.getByText('Connect WebSocket')
    await act(async () => {
      fireEvent.click(connectButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument()
    })

    // Simulate disconnection
    const wsInstance = MockWebSocketIntegration.instances[0]
    await act(async () => {
      wsInstance.simulateClose()
    })

    await waitFor(() => {
      expect(screen.getByText('Disconnected')).toBeInTheDocument()
    })

    // The component should attempt to reconnect (after 3 seconds in the implementation)
    // For testing purposes, we'll just verify the state change
    expect(screen.getByText('Connect WebSocket')).toBeInTheDocument()
  })
})

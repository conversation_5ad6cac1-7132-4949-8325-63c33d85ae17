<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ComplianceMax V74{% endblock %}</title>
    
    <!-- External CSS - Following Grok's recommendations -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-base.css') }}">
    
    <!-- Additional page-specific CSS -->
    {% block extra_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="nav-brand">
                    <span class="text-xl font-bold">🏛️ ComplianceMax V74</span>
                </div>
                <div class="nav-links">
                    <a href="{{ url_for('emergency_work') }}" 
                       class="{% if request.endpoint == 'emergency_work' %}active{% endif %}">
                        Emergency Work (A&B)
                    </a>
                    <a href="{{ url_for('cbcs_pathway') }}" 
                       class="{% if request.endpoint == 'cbcs_pathway' %}active{% endif %}">
                        CBCS Work (C-G)
                    </a>
                    <a href="{{ url_for('system_status') }}">System Status</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="notification {{ category }} show" id="flash-{{ loop.index }}">
                        {{ message }}
                        <button onclick="closeNotification('flash-{{ loop.index }}')" 
                                style="float: right; background: none; border: none; color: white; cursor: pointer;">×</button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Page Header -->
        {% block header %}
        <div class="text-center mb-4" style="padding: 2rem 0;">
            <h1 class="text-2xl mb-2">{% block page_title %}ComplianceMax V74{% endblock %}</h1>
            <p class="text-sm opacity-75">{% block page_subtitle %}FEMA Public Assistance Compliance System{% endblock %}</p>
        </div>
        {% endblock %}

        <!-- Page Content -->
        {% block content %}
        <div class="card text-center">
            <h2 class="mb-3">Welcome to ComplianceMax V74</h2>
            <p class="mb-3">Please select a workflow to begin:</p>
            <div class="grid grid-2">
                <a href="{{ url_for('emergency_work') }}" class="btn btn-block">Emergency Work (A&B)</a>
                <a href="{{ url_for('cbcs_pathway') }}" class="btn btn-block">CBCS Work (C-G)</a>
            </div>
        </div>
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer style="margin-top: 3rem; padding: 2rem 0; border-top: 1px solid var(--border); opacity: 0.7;">
        <div class="container text-center text-sm">
            <p>ComplianceMax V74 - Phase 9 Wizard Integration | 
               53,048 FEMA Records | 
               <span class="status-success">✅ Operational</span>
            </p>
        </div>
    </footer>

    <!-- Base JavaScript -->
    <script>
        // Notification management
        function closeNotification(id) {
            const notification = document.getElementById(id);
            if (notification) {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }
        }

        // Auto-hide notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach((notification, index) => {
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 5000 + (index * 500));
            });
        });

        // Loading state management
        function setLoading(element, loading = true) {
            if (loading) {
                element.classList.add('loading');
                const originalText = element.textContent;
                element.dataset.originalText = originalText;
                element.innerHTML = '<span class="spinner"></span>Loading...';
            } else {
                element.classList.remove('loading');
                element.textContent = element.dataset.originalText || 'Submit';
            }
        }

        // Form validation helpers
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const requiredFields = form.querySelectorAll('[required]');
            let valid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = 'var(--danger)';
                    valid = false;
                } else {
                    field.style.borderColor = 'var(--border)';
                }
            });

            return valid;
        }
    </script>

    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html> 
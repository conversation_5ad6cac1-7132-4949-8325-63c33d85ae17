"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Check,
  CheckCircle,
  ClipboardList,
  FileCheck,
  FileText,
  HelpCircle,
  Info,
  Lightbulb,
  ListChecks,
  Shield,
  ShieldCheck,
  Users,
  Wand2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { femaCategories, complianceQuestions } from "@/lib/data";

// Define the schema for each step
const step1Schema = z.object({
  applicantType: z.string().min(1, "Please select an applicant type"),
  disasterDeclaration: z.string().min(1, "Please select a disaster declaration"),
  categories: z.array(z.string()).min(1, "Please select at least one category"),
  estimatedCost: z.string().min(1, "Please select an estimated cost"),
});

const step2Schema = z.object({
  damageDocumentation: z.string().min(1, "Please select an option"),
  costDocumentation: z.string().min(1, "Please select an option"),
  procurementDocumentation: z.string().min(1, "Please select an option"),
});

const step3Schema = z.object({
  environmentalReviews: z.string().min(1, "Please select an option"),
  insuranceRequirements: z.string().min(1, "Please select an option"),
  hazardMitigation: z.string().min(1, "Please select an option"),
});

const step4Schema = z.object({
  dedicatedStaff: z.string().min(1, "Please select an option"),
  documentationManagement: z.string().min(1, "Please select an option"),
  progressTracking: z.string().min(1, "Please select an option"),
});

export default function ComplianceWizardPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    applicantType: "",
    disasterDeclaration: "",
    categories: [] as string[],
    estimatedCost: "",
    damageDocumentation: "",
    costDocumentation: "",
    procurementDocumentation: "",
    environmentalReviews: "",
    insuranceRequirements: "",
    hazardMitigation: "",
    dedicatedStaff: "",
    documentationManagement: "",
    progressTracking: "",
  });

  const [headerRef, headerInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [contentRef, contentInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  // Create form instances for each step
  const step1Form = useForm<z.infer<typeof step1Schema>>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      applicantType: formData.applicantType,
      disasterDeclaration: formData.disasterDeclaration,
      categories: formData.categories,
      estimatedCost: formData.estimatedCost,
    },
  });

  const step2Form = useForm<z.infer<typeof step2Schema>>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      damageDocumentation: formData.damageDocumentation,
      costDocumentation: formData.costDocumentation,
      procurementDocumentation: formData.procurementDocumentation,
    },
  });

  const step3Form = useForm<z.infer<typeof step3Schema>>({
    resolver: zodResolver(step3Schema),
    defaultValues: {
      environmentalReviews: formData.environmentalReviews,
      insuranceRequirements: formData.insuranceRequirements,
      hazardMitigation: formData.hazardMitigation,
    },
  });

  const step4Form = useForm<z.infer<typeof step4Schema>>({
    resolver: zodResolver(step4Schema),
    defaultValues: {
      dedicatedStaff: formData.dedicatedStaff,
      documentationManagement: formData.documentationManagement,
      progressTracking: formData.progressTracking,
    },
  });

  const totalSteps = 5; // Including summary
  const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;

  const handleNext = (data: any) => {
    setFormData({ ...formData, ...data });
    setCurrentStep(currentStep + 1);
    window.scrollTo(0, 0);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
    window.scrollTo(0, 0);
  };

  const handleSubmit = () => {
    // In a real app, this would submit the data to the server
    console.log("Final form data:", formData);
    router.push("/dashboard");
  };

  // Generate recommendations based on form data
  const generateRecommendations = () => {
    const recommendations = [];

    // Documentation recommendations
    if (formData.damageDocumentation === "No documentation yet" || formData.damageDocumentation === "Limited documentation available") {
      recommendations.push({
        title: "Damage Documentation",
        description: "Develop comprehensive damage documentation with photos and detailed assessments",
        priority: "Critical",
        icon: <FileCheck className="h-5 w-5 text-destructive" />,
      });
    }

    // Cost documentation recommendations
    if (formData.costDocumentation === "No documentation yet" || formData.costDocumentation === "Partial documentation available") {
      recommendations.push({
        title: "Cost Documentation",
        description: "Implement systematic cost tracking and documentation for all project expenses",
        priority: "Critical",
        icon: <ClipboardList className="h-5 w-5 text-destructive" />,
      });
    }

    // Procurement recommendations
    if (formData.procurementDocumentation === "No procurement documentation" || formData.procurementDocumentation === "Limited procurement documentation") {
      recommendations.push({
        title: "Procurement Documentation",
        description: "Establish procurement procedures that comply with federal requirements",
        priority: "High",
        icon: <FileText className="h-5 w-5 text-warning" />,
      });
    }

    // Environmental review recommendations
    if (formData.environmentalReviews === "Reviews not started" || formData.environmentalReviews === "Not sure if reviews are required") {
      recommendations.push({
        title: "Environmental Reviews",
        description: "Initiate environmental and historic preservation reviews immediately",
        priority: "High",
        icon: <Shield className="h-5 w-5 text-warning" />,
      });
    }

    // Insurance recommendations
    if (formData.insuranceRequirements === "Insurance requirements not identified" || formData.insuranceRequirements === "Not sure about insurance requirements") {
      recommendations.push({
        title: "Insurance Documentation",
        description: "Identify and address all insurance requirements for FEMA compliance",
        priority: "Medium",
        icon: <AlertTriangle className="h-5 w-5 text-primary" />,
      });
    }

    // Project management recommendations
    if (formData.dedicatedStaff === "No dedicated staff") {
      recommendations.push({
        title: "Dedicated Staff",
        description: "Assign dedicated personnel to manage FEMA Public Assistance projects",
        priority: "Medium",
        icon: <Users className="h-5 w-5 text-primary" />,
      });
    }

    // Add some default recommendations if we don't have many
    if (recommendations.length < 3) {
      recommendations.push({
        title: "Documentation System",
        description: "Implement a centralized documentation system for all FEMA-related records",
        priority: "Medium",
        icon: <FileText className="h-5 w-5 text-primary" />,
      });
      
      recommendations.push({
        title: "Regular Progress Reporting",
        description: "Establish regular progress reporting to track project milestones and deadlines",
        priority: "Medium",
        icon: <ListChecks className="h-5 w-5 text-primary" />,
      });
    }

    return recommendations;
  };

  const recommendations = generateRecommendations();

  return (
    <div className="space-y-8">
      <motion.div
        ref={headerRef}
        initial={{ opacity: 0, y: -20 }}
        animate={headerInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-blue-600 to-indigo-600 p-8 rounded-lg shadow-md"
      >
        <h1 className="text-3xl font-bold text-white">FEMA Compliance Wizard</h1>
        <p className="text-blue-100 mt-2">
          Complete this wizard to assess your compliance readiness and get personalized recommendations
        </p>
        
        <div className="mt-6">
          <div className="flex justify-between text-blue-100 text-sm mb-2">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2 bg-blue-400/30" />
        </div>
      </motion.div>

      <motion.div
        ref={contentRef}
        initial={{ opacity: 0, y: 20 }}
        animate={contentInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-card rounded-lg border shadow-sm p-6"
      >
        {currentStep === 1 && (
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold tracking-tight">Project Information</h2>
                <p className="text-muted-foreground text-sm">
                  Tell us about your Public Assistance project
                </p>
              </div>
            </div>
            
            <Form {...step1Form}>
              <form onSubmit={step1Form.handleSubmit(handleNext)} className="space-y-6">
                <FormField
                  control={step1Form.control}
                  name="applicantType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Applicant Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select applicant type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="state">State Government</SelectItem>
                          <SelectItem value="local">Local Government</SelectItem>
                          <SelectItem value="tribal">Tribal Government</SelectItem>
                          <SelectItem value="pnp">Private Non-Profit</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the type of entity applying for FEMA Public Assistance
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step1Form.control}
                  name="disasterDeclaration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Disaster Declaration</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select disaster declaration" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="DR-4611">DR-4611: Hurricane (2023)</SelectItem>
                          <SelectItem value="DR-4582">DR-4582: Severe Storms (2023)</SelectItem>
                          <SelectItem value="DR-4547">DR-4547: COVID-19 Pandemic (2020)</SelectItem>
                          <SelectItem value="other">Other Declaration</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the FEMA disaster declaration number
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step1Form.control}
                  name="categories"
                  render={() => (
                    <FormItem>
                      <div className="mb-4">
                        <FormLabel>FEMA Categories</FormLabel>
                        <FormDescription>
                          Select all applicable FEMA Public Assistance categories
                        </FormDescription>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {femaCategories.map((category) => (
                          <div key={category.id} className="flex items-start space-x-3">
                            <div>
                              <input
                                type="checkbox"
                                id={category.id}
                                checked={formData.categories.includes(category.id)}
                                onChange={(e) => {
                                  const newCategories = e.target.checked
                                    ? [...formData.categories, category.id]
                                    : formData.categories.filter((id) => id !== category.id);
                                  
                                  setFormData({
                                    ...formData,
                                    categories: newCategories,
                                  });
                                  
                                  step1Form.setValue("categories", newCategories, {
                                    shouldValidate: true,
                                  });
                                }}
                                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"
                              />
                            </div>
                            <div>
                              <label
                                htmlFor={category.id}
                                className="text-sm font-medium text-gray-900 dark:text-gray-100 block"
                              >
                                {category.name}
                              </label>
                              <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                                {category.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                      <FormMessage className="mt-2">
                        {step1Form.formState.errors.categories?.message}
                      </FormMessage>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step1Form.control}
                  name="estimatedCost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estimated Project Cost</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select estimated cost range" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="small">Small Project (Under $139,800)</SelectItem>
                          <SelectItem value="large">Large Project ($139,800 - $1 million)</SelectItem>
                          <SelectItem value="verylarge">Very Large Project (Over $1 million)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the estimated total cost range for your project
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-end space-x-2">
                  <Button 
                    type="button" 
                    className="bg-blue-600 hover:bg-blue-700"
                    onClick={() => {
                      const values = step1Form.getValues();
                      handleNext(values);
                    }}
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
        
        {currentStep === 2 && (
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                <ClipboardList className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold tracking-tight">Documentation Assessment</h2>
                <p className="text-muted-foreground text-sm">
                  Evaluate your current documentation status
                </p>
              </div>
            </div>
            
            <Form {...step2Form}>
              <form onSubmit={step2Form.handleSubmit(handleNext)} className="space-y-6">
                <FormField
                  control={step2Form.control}
                  name="damageDocumentation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Damage Documentation Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select documentation status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {complianceQuestions.documentation.map((option) => (
                            <SelectItem key={option.id} value={option.options[0]}>
                              {option.options[0]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How well are your damages documented with photos, descriptions, and assessments?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step2Form.control}
                  name="costDocumentation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cost Documentation Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select cost documentation status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {complianceQuestions.documentation.map((option) => (
                            <SelectItem key={option.id} value={option.options[1]}>
                              {option.options[1]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How complete is your cost tracking and documentation?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step2Form.control}
                  name="procurementDocumentation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Procurement Documentation Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select procurement status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {complianceQuestions.documentation.map((option) => (
                            <SelectItem key={option.id} value={option.options[2]}>
                              {option.options[2]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How well documented are your procurement processes for this project?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-between space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
        
        {currentStep === 3 && (
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                <ShieldCheck className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold tracking-tight">Compliance Requirements</h2>
                <p className="text-muted-foreground text-sm">
                  Evaluate key compliance areas for your project
                </p>
              </div>
            </div>
            
            <Form {...step3Form}>
              <form onSubmit={step3Form.handleSubmit(handleNext)} className="space-y-6">
                <FormField
                  control={step3Form.control}
                  name="environmentalReviews"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Environmental & Historic Preservation Reviews</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select review status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {complianceQuestions.compliance.map((option) => (
                            <SelectItem key={option.id} value={option.options[0]}>
                              {option.options[0]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        What is the status of environmental and historic preservation reviews?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step3Form.control}
                  name="insuranceRequirements"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Insurance Requirements</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select insurance status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {complianceQuestions.compliance.map((option) => (
                            <SelectItem key={option.id} value={option.options[1]}>
                              {option.options[1]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Have you identified and addressed all insurance requirements?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step3Form.control}
                  name="hazardMitigation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hazard Mitigation</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select mitigation status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {complianceQuestions.compliance.map((option) => (
                            <SelectItem key={option.id} value={option.options[2]}>
                              {option.options[2]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Have you addressed hazard mitigation opportunities in your project?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-between space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
        
        {currentStep === 4 && (
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                <Wand2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold tracking-tight">Project Management</h2>
                <p className="text-muted-foreground text-sm">
                  Assess project management resources and capabilities
                </p>
              </div>
            </div>
            
            <Form {...step4Form}>
              <form onSubmit={step4Form.handleSubmit(handleNext)} className="space-y-6">
                <FormField
                  control={step4Form.control}
                  name="dedicatedStaff"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Dedicated Staff</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select staff availability" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Full-time dedicated staff">Full-time dedicated staff</SelectItem>
                          <SelectItem value="Part-time dedicated staff">Part-time dedicated staff</SelectItem>
                          <SelectItem value="Staff assigned multiple duties">Staff assigned multiple duties</SelectItem>
                          <SelectItem value="No dedicated staff">No dedicated staff</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Do you have dedicated personnel assigned to manage this project?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step4Form.control}
                  name="documentationManagement"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Documentation Management</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select documentation system" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Specialized compliance system">Specialized compliance system</SelectItem>
                          <SelectItem value="Document management system">Document management system</SelectItem>
                          <SelectItem value="Basic file organization">Basic file organization</SelectItem>
                          <SelectItem value="No systematic approach">No systematic approach</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How do you manage project documentation?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={step4Form.control}
                  name="progressTracking"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Progress Tracking</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select tracking method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Detailed project management system">Detailed project management system</SelectItem>
                          <SelectItem value="Regular status meetings and reports">Regular status meetings and reports</SelectItem>
                          <SelectItem value="Basic milestone tracking">Basic milestone tracking</SelectItem>
                          <SelectItem value="No formal tracking">No formal tracking</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        How do you track project progress and deadlines?
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-between space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
        
        {currentStep === 5 && (
          <div className="space-y-8">
            <div className="flex items-start space-x-4">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                <CheckCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold tracking-tight">Summary & Recommendations</h2>
                <p className="text-muted-foreground text-sm">
                  Review your assessment and get personalized recommendations
                </p>
              </div>
            </div>
            
            <div className="space-y-6">
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Project Overview</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Applicant Type:</span>{" "}
                    <span className="text-gray-900 dark:text-gray-200">{formData.applicantType || "Not specified"}</span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Disaster Declaration:</span>{" "}
                    <span className="text-gray-900 dark:text-gray-200">{formData.disasterDeclaration || "Not specified"}</span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Categories:</span>{" "}
                    <span className="text-gray-900 dark:text-gray-200">
                      {formData.categories.length > 0
                        ? formData.categories.join(", ")
                        : "None selected"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Estimated Cost:</span>{" "}
                    <span className="text-gray-900 dark:text-gray-200">{formData.estimatedCost || "Not specified"}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-4">Recommended Actions</h3>
                <div className="space-y-4">
                  {recommendations.map((rec, index) => (
                    <Card key={index}>
                      <CardHeader className="py-3">
                        <div className="flex items-center gap-3">
                          {rec.icon}
                          <div>
                            <CardTitle className="text-base">{rec.title}</CardTitle>
                            <Badge
                              variant={
                                rec.priority === "Critical"
                                  ? "destructive"
                                  : rec.priority === "High"
                                  ? "warning"
                                  : "secondary"
                              }
                              className="mt-1"
                            >
                              {rec.priority} Priority
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="py-3 text-sm text-gray-600 dark:text-gray-300">
                        {rec.description}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800">
                <div className="flex gap-3">
                  <div className="p-1.5 bg-blue-100 dark:bg-blue-800 rounded-full">
                    <Info className="h-4 w-4 text-blue-700 dark:text-blue-300" />
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-700 dark:text-blue-300 text-sm">Next Steps</h4>
                    <p className="text-blue-600 dark:text-blue-400 text-xs mt-1">
                      Complete your compliance improvement plan by addressing the recommendations above.
                      Download your custom report and track your progress in the dashboard.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
                <div className="space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => console.log("Download report")}
                    className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-500 dark:text-blue-500 dark:hover:bg-blue-950/50"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Download Report
                  </Button>
                  <Button
                    type="button"
                    onClick={handleSubmit}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    Complete
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}

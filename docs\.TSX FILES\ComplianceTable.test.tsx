import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';
import { ComplianceTable } from '@/components/compliance/ComplianceTable';
import { ComplianceItem, ComplianceStatus } from '@/types/compliance';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock dependencies
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/compliance',
    query: {},
  }),
}));

jest.mock('@/hooks/useDebounce', () => ({
  useDebounce: (value: any) => value,
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Test data
const mockComplianceData: ComplianceItem[] = [
  {
    id: '1',
    title: 'GDPR Compliance Assessment',
    description: 'General Data Protection Regulation compliance review',
    status: ComplianceStatus.COMPLIANT,
    priority: 'high',
    dueDate: '2024-12-31',
    assignee: 'John Doe',
    department: 'Legal',
    framework: 'GDPR',
    lastUpdated: '2024-06-20',
    progress: 85,
    riskLevel: 'medium',
    tags: ['privacy', 'data-protection'],
  },
  {
    id: '2',
    title: 'SOX Financial Controls',
    description: 'Sarbanes-Oxley Act compliance verification',
    status: ComplianceStatus.NON_COMPLIANT,
    priority: 'critical',
    dueDate: '2024-07-15',
    assignee: 'Jane Smith',
    department: 'Finance',
    framework: 'SOX',
    lastUpdated: '2024-06-19',
    progress: 45,
    riskLevel: 'high',
    tags: ['financial', 'controls'],
  },
  {
    id: '3',
    title: 'ISO 27001 Security Review',
    description: 'Information security management system audit',
    status: ComplianceStatus.IN_PROGRESS,
    priority: 'medium',
    dueDate: '2024-08-30',
    assignee: 'Bob Johnson',
    department: 'IT Security',
    framework: 'ISO 27001',
    lastUpdated: '2024-06-18',
    progress: 65,
    riskLevel: 'low',
    tags: ['security', 'iso'],
  },
];

const defaultProps = {
  data: mockComplianceData,
  loading: false,
  error: null,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onView: jest.fn(),
  onStatusChange: jest.fn(),
  onBulkAction: jest.fn(),
  pagination: {
    page: 1,
    pageSize: 10,
    total: 3,
    totalPages: 1,
  },
  onPageChange: jest.fn(),
  onPageSizeChange: jest.fn(),
  sorting: {
    field: 'title',
    direction: 'asc' as const,
  },
  onSort: jest.fn(),
  filters: {},
  onFilterChange: jest.fn(),
  searchQuery: '',
  onSearchChange: jest.fn(),
};

// Helper function to render component with providers
const renderWithProviders = (props = {}) => {
  const mergedProps = { ...defaultProps, ...props };
  return render(<ComplianceTable {...mergedProps} />);
};

describe('ComplianceTable', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      renderWithProviders();
      expect(screen.getByRole('table')).toBeInTheDocument();
    });

    it('displays compliance data correctly', () => {
      renderWithProviders();
      
      expect(screen.getByText('GDPR Compliance Assessment')).toBeInTheDocument();
      expect(screen.getByText('SOX Financial Controls')).toBeInTheDocument();
      expect(screen.getByText('ISO 27001 Security Review')).toBeInTheDocument();
    });

    it('shows loading state', () => {
      renderWithProviders({ loading: true });
      expect(screen.getByTestId('compliance-table-loading')).toBeInTheDocument();
    });

    it('displays error state', () => {
      const errorMessage = 'Failed to load compliance data';
      renderWithProviders({ error: errorMessage });
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('shows empty state when no data', () => {
      renderWithProviders({ data: [] });
      expect(screen.getByText(/no compliance items found/i)).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles edit action', async () => {
      const onEdit = jest.fn();
      renderWithProviders({ onEdit });
      
      const editButton = screen.getAllByLabelText(/edit/i)[0];
      await userEvent.click(editButton);
      
      expect(onEdit).toHaveBeenCalledWith(mockComplianceData[0]);
    });

    it('handles delete action with confirmation', async () => {
      const onDelete = jest.fn();
      renderWithProviders({ onDelete });
      
      const deleteButton = screen.getAllByLabelText(/delete/i)[0];
      await userEvent.click(deleteButton);
      
      // Confirm deletion in modal
      const confirmButton = screen.getByText(/confirm/i);
      await userEvent.click(confirmButton);
      
      expect(onDelete).toHaveBeenCalledWith(mockComplianceData[0].id);
    });

    it('handles view action', async () => {
      const onView = jest.fn();
      renderWithProviders({ onView });
      
      const viewButton = screen.getAllByLabelText(/view/i)[0];
      await userEvent.click(viewButton);
      
      expect(onView).toHaveBeenCalledWith(mockComplianceData[0]);
    });

    it('handles status change', async () => {
      const onStatusChange = jest.fn();
      renderWithProviders({ onStatusChange });
      
      const statusSelect = screen.getAllByDisplayValue(/compliant/i)[0];
      await userEvent.selectOptions(statusSelect, ComplianceStatus.IN_PROGRESS);
      
      expect(onStatusChange).toHaveBeenCalledWith(
        mockComplianceData[0].id,
        ComplianceStatus.IN_PROGRESS
      );
    });

    it('handles row selection', async () => {
      renderWithProviders();
      
      const checkbox = screen.getAllByRole('checkbox')[1]; // First data row checkbox
      await userEvent.click(checkbox);
      
      expect(checkbox).toBeChecked();
    });

    it('handles select all functionality', async () => {
      renderWithProviders();
      
      const selectAllCheckbox = screen.getAllByRole('checkbox')[0]; // Header checkbox
      await userEvent.click(selectAllCheckbox);
      
      const dataCheckboxes = screen.getAllByRole('checkbox').slice(1);
      dataCheckboxes.forEach(checkbox => {
        expect(checkbox).toBeChecked();
      });
    });
  });

  describe('Sorting and Filtering', () => {
    it('handles column sorting', async () => {
      const onSort = jest.fn();
      renderWithProviders({ onSort });
      
      const titleHeader = screen.getByText('Title');
      await userEvent.click(titleHeader);
      
      expect(onSort).toHaveBeenCalledWith('title');
    });

    it('displays sort indicators', () => {
      renderWithProviders({
        sorting: { field: 'title', direction: 'asc' }
      });
      
      const sortIcon = screen.getByTestId('sort-asc-icon');
      expect(sortIcon).toBeInTheDocument();
    });

    it('handles search functionality', async () => {
      const onSearchChange = jest.fn();
      renderWithProviders({ onSearchChange });
      
      const searchInput = screen.getByPlaceholderText(/search compliance items/i);
      await userEvent.type(searchInput, 'GDPR');
      
      expect(onSearchChange).toHaveBeenCalledWith('GDPR');
    });

    it('handles filter changes', async () => {
      const onFilterChange = jest.fn();
      renderWithProviders({ onFilterChange });
      
      const statusFilter = screen.getByLabelText(/filter by status/i);
      await userEvent.selectOptions(statusFilter, ComplianceStatus.COMPLIANT);
      
      expect(onFilterChange).toHaveBeenCalledWith({
        status: ComplianceStatus.COMPLIANT
      });
    });

    it('clears filters when reset button is clicked', async () => {
      const onFilterChange = jest.fn();
      renderWithProviders({ 
        onFilterChange,
        filters: { status: ComplianceStatus.COMPLIANT }
      });
      
      const clearButton = screen.getByText(/clear filters/i);
      await userEvent.click(clearButton);
      
      expect(onFilterChange).toHaveBeenCalledWith({});
    });
  });

  describe('Pagination', () => {
    const paginationProps = {
      pagination: {
        page: 2,
        pageSize: 10,
        total: 25,
        totalPages: 3,
      }
    };

    it('displays pagination controls', () => {
      renderWithProviders(paginationProps);
      
      expect(screen.getByText('Page 2 of 3')).toBeInTheDocument();
      expect(screen.getByLabelText(/previous page/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/next page/i)).toBeInTheDocument();
    });

    it('handles page navigation', async () => {
      const onPageChange = jest.fn();
      renderWithProviders({ ...paginationProps, onPageChange });
      
      const nextButton = screen.getByLabelText(/next page/i);
      await userEvent.click(nextButton);
      
      expect(onPageChange).toHaveBeenCalledWith(3);
    });

    it('handles page size changes', async () => {
      const onPageSizeChange = jest.fn();
      renderWithProviders({ ...paginationProps, onPageSizeChange });
      
      const pageSizeSelect = screen.getByDisplayValue('10');
      await userEvent.selectOptions(pageSizeSelect, '25');
      
      expect(onPageSizeChange).toHaveBeenCalledWith(25);
    });

    it('disables navigation buttons appropriately', () => {
      renderWithProviders({
        pagination: { page: 1, pageSize: 10, total: 25, totalPages: 3 }
      });
      
      const prevButton = screen.getByLabelText(/previous page/i);
      expect(prevButton).toBeDisabled();
    });
  });

  describe('Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = renderWithProviders();
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper ARIA labels', () => {
      renderWithProviders();
      
      expect(screen.getByRole('table')).toHaveAttribute('aria-label', 'Compliance items table');
      expect(screen.getByRole('searchbox')).toHaveAttribute('aria-label', 'Search compliance items');
    });

    it('supports keyboard navigation', async () => {
      renderWithProviders();
      
      const firstEditButton = screen.getAllByLabelText(/edit/i)[0];
      firstEditButton.focus();
      expect(firstEditButton).toHaveFocus();
      
      // Tab to next focusable element
      await userEvent.tab();
      const firstDeleteButton = screen.getAllByLabelText(/delete/i)[0];
      expect(firstDeleteButton).toHaveFocus();
    });

    it('announces sort changes to screen readers', async () => {
      const onSort = jest.fn();
      renderWithProviders({ onSort });
      
      const titleHeader = screen.getByText('Title');
      await userEvent.click(titleHeader);
      
      const announcement = screen.getByRole('status');
      expect(announcement).toHaveTextContent(/sorted by title/i);
    });
  });

  describe('Responsive Design', () => {
    beforeEach(() => {
      // Mock window.matchMedia
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query.includes('768px') ? false : true,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });
    });

    it('adapts to mobile viewport', () => {
      // Mock mobile viewport
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query.includes('768px') ? true : false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      renderWithProviders();
      
      // Check for mobile-specific elements
      expect(screen.getByTestId('mobile-table-view')).toBeInTheDocument();
    });

    it('shows desktop layout on larger screens', () => {
      renderWithProviders();
      
      expect(screen.getByTestId('desktop-table-view')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('virtualizes large datasets', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, index) => ({
        ...mockComplianceData[0],
        id: `item-${index}`,
        title: `Compliance Item ${index}`,
      }));

      renderWithProviders({ data: largeDataset });
      
      // Should only render visible rows
      const visibleRows = screen.getAllByRole('row');
      expect(visibleRows.length).toBeLessThan(50); // Assuming viewport shows ~20-30 rows
    });

    it('debounces search input', async () => {
      const onSearchChange = jest.fn();
      renderWithProviders({ onSearchChange });
      
      const searchInput = screen.getByPlaceholderText(/search compliance items/i);
      
      // Type rapidly
      await userEvent.type(searchInput, 'test');
      
      // Should debounce calls
      await waitFor(() => {
        expect(onSearchChange).toHaveBeenCalledTimes(1);
      }, { timeout: 1000 });
    });

    it('memoizes expensive calculations', () => {
      const { rerender } = renderWithProviders();
      
      // Re-render with same props
      rerender(<ComplianceTable {...defaultProps} />);
      
      // Component should not re-calculate expensive operations
      expect(screen.getByRole('table')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', () => {
      const error = new Error('Network error');
      renderWithProviders({ error: error.message });
      
      expect(screen.getByText(/network error/i)).toBeInTheDocument();
      expect(screen.getByText(/try again/i)).toBeInTheDocument();
    });

    it('handles invalid data gracefully', () => {
      const invalidData = [
        { id: '1', title: null, status: 'invalid-status' }
      ] as any;
      
      renderWithProviders({ data: invalidData });
      
      // Should render without crashing and show fallback values
      expect(screen.getByText(/untitled/i)).toBeInTheDocument();
    });

    it('recovers from action failures', async () => {
      const onEdit = jest.fn().mockRejectedValue(new Error('Edit failed'));
      renderWithProviders({ onEdit });
      
      const editButton = screen.getAllByLabelText(/edit/i)[0];
      await userEvent.click(editButton);
      
      await waitFor(() => {
        expect(screen.getByText(/edit failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Integration Scenarios', () => {
    it('handles bulk operations', async () => {
      const onBulkAction = jest.fn();
      renderWithProviders({ onBulkAction });
      
      // Select multiple items
      const checkboxes = screen.getAllByRole('checkbox').slice(1, 3);
      for (const checkbox of checkboxes) {
        await userEvent.click(checkbox);
      }
      
      // Perform bulk action
      const bulkActionButton = screen.getByText(/bulk actions/i);
      await userEvent.click(bulkActionButton);
      
      const deleteAction = screen.getByText(/delete selected/i);
      await userEvent.click(deleteAction);
      
      expect(onBulkAction).toHaveBeenCalledWith('delete', ['1', '2']);
    });

    it('maintains state during data updates', async () => {
      const { rerender } = renderWithProviders();
      
      // Select an item
      const checkbox = screen.getAllByRole('checkbox')[1];
      await userEvent.click(checkbox);
      expect(checkbox).toBeChecked();
      
      // Update data
      const updatedData = [...mockComplianceData];
      updatedData[0] = { ...updatedData[0], title: 'Updated Title' };
      
      rerender(<ComplianceTable {...defaultProps} data={updatedData} />);
      
      // Selection should be maintained
      expect(screen.getAllByRole('checkbox')[1]).toBeChecked();
      expect(screen.getByText('Updated Title')).toBeInTheDocument();
    });

    it('handles real-time updates', async () => {
      const { rerender } = renderWithProviders();
      
      // Simulate real-time update
      const updatedData = [
        ...mockComplianceData,
        {
          id: '4',
          title: 'New Compliance Item',
          description: 'Real-time added item',
          status: ComplianceStatus.PENDING,
          priority: 'low',
          dueDate: '2024-09-30',
          assignee: 'New User',
          department: 'Operations',
          framework: 'Custom',
          lastUpdated: '2024-06-24',
          progress: 0,
          riskLevel: 'low',
          tags: ['new'],
        }
      ];
      
      rerender(<ComplianceTable {...defaultProps} data={updatedData} />);
      
      expect(screen.getByText('New Compliance Item')).toBeInTheDocument();
    });
  });

  describe('Data Export', () => {
    it('exports selected data to CSV', async () => {
      renderWithProviders();
      
      // Select items
      const checkboxes = screen.getAllByRole('checkbox').slice(1, 3);
      for (const checkbox of checkboxes) {
        await userEvent.click(checkbox);
      }
      
      const exportButton = screen.getByText(/export/i);
      await userEvent.click(exportButton);
      
      const csvOption = screen.getByText(/csv/i);
      await userEvent.click(csvOption);
      
      // Should trigger download
      expect(screen.getByText(/download started/i)).toBeInTheDocument();
    });
  });

  describe('Advanced Filtering', () => {
    it('handles date range filtering', async () => {
      const onFilterChange = jest.fn();
      renderWithProviders({ onFilterChange });
      
      const dateFromInput = screen.getByLabelText(/from date/i);
      const dateToInput = screen.getByLabelText(/to date/i);
      
      await userEvent.type(dateFromInput, '2024-06-01');
      await userEvent.type(dateToInput, '2024-06-30');
      
      expect(onFilterChange).toHaveBeenCalledWith({
        dateRange: {
          from: '2024-06-01',
          to: '2024-06-30'
        }
      });
    });

    it('handles multi-select tag filtering', async () => {
      const onFilterChange = jest.fn();
      renderWithProviders({ onFilterChange });
      
      const tagFilter = screen.getByLabelText(/filter by tags/i);
      await userEvent.click(tagFilter);
      
      const privacyTag = screen.getByText('privacy');
      const securityTag = screen.getByText('security');
      
      await userEvent.click(privacyTag);
      await userEvent.click(securityTag);
      
      expect(onFilterChange).toHaveBeenCalledWith({
        tags: ['privacy', 'security']
      });
    });
  });
});


'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Wind, 
  Thermometer, 
  Eye, 
  MapPin,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { GenericDataCard, DataPoint } from './GenericDataCard';

// EPA-specific data types
interface EPAMetadata {
  aqi: number;
  parameter: string;
  reporting_area: string;
  state_code: string;
  category?: string;
}

interface EPAAirQualityCardProps {
  dataPoint: DataPoint;
  className?: string;
  showTrend?: boolean;
  onViewDetails?: (dataPoint: DataPoint) => void;
}

// AQI level configuration
const aqiLevels = {
  good: { min: 0, max: 50, color: 'text-green-600', bg: 'bg-green-50', label: 'Good' },
  moderate: { min: 51, max: 100, color: 'text-yellow-600', bg: 'bg-yellow-50', label: 'Moderate' },
  unhealthy_sensitive: { min: 101, max: 150, color: 'text-orange-600', bg: 'bg-orange-50', label: 'Unhealthy for Sensitive Groups' },
  unhealthy: { min: 151, max: 200, color: 'text-red-600', bg: 'bg-red-50', label: 'Unhealthy' },
  very_unhealthy: { min: 201, max: 300, color: 'text-purple-600', bg: 'bg-purple-50', label: 'Very Unhealthy' },
  hazardous: { min: 301, max: 500, color: 'text-red-800', bg: 'bg-red-100', label: 'Hazardous' }
};

// Parameter icons
const parameterIcons = {
  'PM2.5': Wind,
  'PM10': Wind,
  'O3': Eye,
  'NO2': Thermometer,
  'SO2': Thermometer,
  'CO': Wind
};

export const EPAAirQualityCard: React.FC<EPAAirQualityCardProps> = ({
  dataPoint,
  className = '',
  showTrend = false,
  onViewDetails
}) => {
  const metadata = dataPoint.metadata as EPAMetadata;
  const aqi = metadata.aqi || 0;
  const parameter = metadata.parameter || 'Unknown';
  const reportingArea = metadata.reporting_area || 'Unknown Area';
  const stateCode = metadata.state_code || '';
  
  // Determine AQI level
  const aqiLevel = Object.entries(aqiLevels).find(([_, config]) => 
    aqi >= config.min && aqi <= config.max
  )?.[1] || aqiLevels.good;
  
  // Get parameter icon
  const ParameterIcon = parameterIcons[parameter as keyof typeof parameterIcons] || Wind;
  
  // Custom EPA-specific content
  const customContent = (
    <Card className={`${aqiLevel.bg} border-l-4 border-l-blue-500 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <ParameterIcon className="h-6 w-6 text-blue-600" />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">
                Air Quality - {reportingArea}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge 
                  variant={aqi > 100 ? 'destructive' : aqi > 50 ? 'outline' : 'secondary'}
                  className="text-xs"
                >
                  AQI {aqi}
                </Badge>
                <span className={`text-xs font-medium ${aqiLevel.color}`}>
                  {aqiLevel.label}
                </span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">{aqi}</div>
            <div className="text-xs text-gray-500">AQI</div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-gray-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">{reportingArea}</div>
              <div className="text-xs text-gray-500">{stateCode}</div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <ParameterIcon className="h-4 w-4 text-gray-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">{parameter}</div>
              <div className="text-xs text-gray-500">Parameter</div>
            </div>
          </div>
        </div>
        
        {/* AQI Scale Visualization */}
        <div className="mb-4">
          <div className="text-xs font-medium text-gray-600 mb-2">AQI Scale</div>
          <div className="flex h-2 rounded-full overflow-hidden">
            <div className="bg-green-400 flex-1"></div>
            <div className="bg-yellow-400 flex-1"></div>
            <div className="bg-orange-400 flex-1"></div>
            <div className="bg-red-400 flex-1"></div>
            <div className="bg-purple-400 flex-1"></div>
            <div className="bg-red-800 flex-1"></div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0</span>
            <span>50</span>
            <span>100</span>
            <span>150</span>
            <span>200</span>
            <span>300+</span>
          </div>
          {/* Current AQI indicator */}
          <div 
            className="relative mt-1"
            style={{ 
              marginLeft: `${Math.min((aqi / 300) * 100, 100)}%`,
              transform: 'translateX(-50%)'
            }}
          >
            <div className="w-0 h-0 border-l-2 border-r-2 border-b-4 border-transparent border-b-gray-800"></div>
          </div>
        </div>
        
        {/* Health Recommendations */}
        <div className="bg-white bg-opacity-50 rounded-lg p-3">
          <h4 className="text-xs font-semibold text-gray-700 mb-1">Health Recommendations</h4>
          <p className="text-xs text-gray-600">
            {getHealthRecommendation(aqi)}
          </p>
        </div>
        
        <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
          <span>EPA AirNow</span>
          <span>{new Date(dataPoint.timestamp).toLocaleTimeString()}</span>
        </div>
        
        {onViewDetails && (
          <button
            onClick={() => onViewDetails(dataPoint)}
            className="w-full mt-3 text-xs text-blue-600 hover:text-blue-800 font-medium"
          >
            View Detailed Report →
          </button>
        )}
      </CardContent>
    </Card>
  );
  
  return customContent;
};

// Health recommendation based on AQI
function getHealthRecommendation(aqi: number): string {
  if (aqi <= 50) {
    return "Air quality is satisfactory. Enjoy outdoor activities.";
  } else if (aqi <= 100) {
    return "Air quality is acceptable. Sensitive individuals should consider limiting prolonged outdoor exertion.";
  } else if (aqi <= 150) {
    return "Sensitive groups should reduce outdoor activities. Others can continue normal activities.";
  } else if (aqi <= 200) {
    return "Everyone should limit outdoor activities, especially prolonged exertion.";
  } else if (aqi <= 300) {
    return "Avoid outdoor activities. Stay indoors with windows closed.";
  } else {
    return "Health alert! Avoid all outdoor activities. Emergency conditions.";
  }
}

export default EPAAirQualityCard;

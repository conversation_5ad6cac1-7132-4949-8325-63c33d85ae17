
'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  TrendingUp,
  Users,
  Database,
  Activity
} from 'lucide-react'
import Link from 'next/link'
import { StatsCard } from './StatsCard'
import { RecentActivity } from './RecentActivity'
import { ComplianceChart } from './ComplianceChart'

interface DashboardStats {
  totalProjects: number
  completedProjects: number
  pendingReviews: number
  documentsProcessed: number
  complianceScore: number
  activeUsers: number
}

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    completedProjects: 0,
    pendingReviews: 0,
    documentsProcessed: 0,
    complianceScore: 0,
    activeUsers: 0
  })

  // Simulate data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setStats({
        totalProjects: 24,
        completedProjects: 18,
        pendingReviews: 6,
        documentsProcessed: 142,
        complianceScore: 94,
        activeUsers: 12
      })
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const statsCards = [
    {
      title: 'Total Projects',
      value: stats.totalProjects,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: 'Completed Projects',
      value: stats.completedProjects,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+8%',
      changeType: 'positive' as const
    },
    {
      title: 'Pending Reviews',
      value: stats.pendingReviews,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      change: '-3%',
      changeType: 'negative' as const
    },
    {
      title: 'Documents Processed',
      value: stats.documentsProcessed,
      icon: Database,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+24%',
      changeType: 'positive' as const
    },
    {
      title: 'Compliance Score',
      value: `${stats.complianceScore}%`,
      icon: TrendingUp,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-100',
      change: '+2%',
      changeType: 'positive' as const
    },
    {
      title: 'Active Users',
      value: stats.activeUsers,
      icon: Users,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
      change: '+5%',
      changeType: 'positive' as const
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <p className="text-white/70 mt-2">
            Welcome to your FEMA PA compliance management center
          </p>
        </div>
        <div className="flex gap-3">
          <Link href="/compliance">
            <Button className="bg-blue-600 hover:bg-blue-700">
              Start Compliance Review
            </Button>
          </Link>
          <Link href="/documents">
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              Upload Documents
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statsCards.map((stat, index) => (
          <StatsCard key={stat.title} {...stat} delay={index * 100} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Compliance Overview */}
        <div className="lg:col-span-2">
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Compliance Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ComplianceChart />
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <RecentActivity />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="glass border-white/20">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/compliance/new">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2 border-white/20 text-white hover:bg-white/10">
                <FileText className="w-6 h-6" />
                New Compliance Check
              </Button>
            </Link>
            <Link href="/documents/upload">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2 border-white/20 text-white hover:bg-white/10">
                <Database className="w-6 h-6" />
                Upload Documents
              </Button>
            </Link>
            <Link href="/analytics">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2 border-white/20 text-white hover:bg-white/10">
                <TrendingUp className="w-6 h-6" />
                View Analytics
              </Button>
            </Link>
            <Link href="/reports">
              <Button variant="outline" className="w-full h-20 flex flex-col gap-2 border-white/20 text-white hover:bg-white/10">
                <CheckCircle className="w-6 h-6" />
                Generate Report
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

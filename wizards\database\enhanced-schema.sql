-- ComplianceMax Enhanced Schema Integration
-- SAFE ENHANCEMENT to existing schema.sql - preserves all current functionality
-- Adds best-in-class elements from ALL NEW APP DOCS analysis

-- =============================================================================
-- ENHANCEMENT 1: POLICY FRAMEWORK INTEGRATION
-- Adds sophisticated policy matching and framework management
-- =============================================================================

-- Policy frameworks table (NEW - adds policy management capabilities)
CREATE TABLE IF NOT EXISTS policy_frameworks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  framework_name VARCHAR(100) NOT NULL,
  framework_type VARCHAR(50) CHECK (framework_type IN ('Legislative', 'Regulatory', 'Technical', 'Environmental', 'Procedural')),
  version_identifier VARCHAR(50) NOT NULL,
  effective_date_start DATE NOT NULL,
  effective_date_end DATE,
  superseded_by VARCHAR(50),
  conditional_triggers TEXT[],
  integration_priority INTEGER DEFAULT 5,
  
  -- Integration metadata
  source_document TEXT,
  cfr_references TEXT[],
  related_frameworks TEXT[],
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(framework_name, version_identifier)
);

-- Enhanced document classification (NEW - adds policy matching engine capabilities)
CREATE TABLE IF NOT EXISTS document_classifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  classification_name VARCHAR(100) NOT NULL UNIQUE,
  classification_type VARCHAR(50),
  keywords TEXT[],
  file_patterns TEXT[],
  priority_boost DECIMAL(3,2) DEFAULT 1.0,
  
  -- Matching criteria
  content_patterns TEXT[],
  filename_patterns TEXT[],
  specialized_rules JSONB,
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- Enhanced policy matching results (NEW - tracks document-to-policy relationships)
CREATE TABLE IF NOT EXISTS policy_document_matches (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID REFERENCES processed_documents(id) ON DELETE CASCADE,
  policy_framework_id UUID REFERENCES policy_frameworks(id),
  
  -- Matching scores and metadata
  match_score DECIMAL(8,4) NOT NULL,
  matching_criteria JSONB,
  confidence_level VARCHAR(20) CHECK (confidence_level IN ('HIGH', 'MEDIUM', 'LOW')),
  
  -- Manual override capabilities
  manually_verified BOOLEAN DEFAULT FALSE,
  verification_notes TEXT,
  verified_by VARCHAR(255),
  verified_at TIMESTAMP,
  
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(document_id, policy_framework_id)
);

-- =============================================================================
-- ENHANCEMENT 2: COMPREHENSIVE COMPLIANCE TOPIC MANAGEMENT
-- Adds the 13 key compliance topics identified in ALL NEW APP analysis
-- =============================================================================

-- Compliance topics (NEW - adds structured topic management)
CREATE TABLE IF NOT EXISTS compliance_topics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  topic_name VARCHAR(100) NOT NULL UNIQUE,
  topic_description TEXT,
  topic_category VARCHAR(50),
  
  -- Topic-specific keywords and patterns
  primary_keywords TEXT[],
  secondary_keywords TEXT[],
  exclusion_keywords TEXT[],
  
  -- Regulatory framework associations
  associated_cfr_sections TEXT[],
  associated_policies TEXT[],
  
  -- Processing preferences
  document_type_preferences JSONB,
  scoring_multipliers JSONB,
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- Enhanced compliance step associations (NEW - links topics to our existing steps)
CREATE TABLE IF NOT EXISTS compliance_step_topics (
  compliance_step_id UUID REFERENCES compliance_steps(id) ON DELETE CASCADE,
  compliance_topic_id UUID REFERENCES compliance_topics(id) ON DELETE CASCADE,
  relevance_score DECIMAL(3,2) DEFAULT 1.0,
  
  PRIMARY KEY (compliance_step_id, compliance_topic_id)
);

-- =============================================================================
-- ENHANCEMENT 3: ADVANCED CONDITIONAL LOGIC SYSTEM
-- Expands our existing trigger_condition_if system with multi-dimensional logic
-- =============================================================================

-- Enhanced conditional triggers (NEW - adds complex condition management)
CREATE TABLE IF NOT EXISTS conditional_triggers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trigger_name VARCHAR(100) NOT NULL,
  trigger_category VARCHAR(50),
  
  -- Base condition from existing system
  base_condition TEXT NOT NULL,
  
  -- Enhanced multi-dimensional conditions
  date_conditions JSONB, -- incident_date logic
  location_conditions JSONB, -- geographic/jurisdictional logic
  project_conditions JSONB, -- project type/scope logic
  regulatory_conditions JSONB, -- applicable laws/regulations
  
  -- Conditional outputs
  required_actions TEXT[],
  required_documentation TEXT[],
  responsible_parties TEXT[],
  
  -- Integration with existing schema
  related_compliance_steps UUID[],
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- ENHANCEMENT 4: CORRECTED PAPPG VERSION LOGIC
-- Updates our existing function with the corrected timeline from ALL NEW APP
-- =============================================================================

-- Enhanced PAPPG version determination with corrected dates
CREATE OR REPLACE FUNCTION determine_pappg_version_enhanced(incident_date DATE)
RETURNS pappg_version_enum AS $$
BEGIN
  CASE 
    -- Corrected timeline based on ALL NEW APP analysis
    WHEN incident_date >= '2025-01-06' THEN RETURN 'PAPPG v5.0 (Oct 2022)';
    WHEN incident_date >= '2020-06-01' THEN RETURN 'PAPPG v4.0 (June 2020)';
    WHEN incident_date >= '2017-08-23' THEN RETURN 'PAPPG v3.1 (May 2018)';
    WHEN incident_date >= '2017-04-01' THEN RETURN 'PAPPG v2.0 (Apr 2017)';
    ELSE RETURN 'PAPPG v1.0 (Jan 2016)';
  END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =============================================================================
-- ENHANCEMENT 5: PERFORMANCE OPTIMIZATION INDEXES
-- Adds indexes for the new enhanced functionality
-- =============================================================================

-- Policy framework indexes
CREATE INDEX IF NOT EXISTS idx_policy_frameworks_effective_dates ON policy_frameworks(effective_date_start, effective_date_end);
CREATE INDEX IF NOT EXISTS idx_policy_frameworks_type ON policy_frameworks(framework_type);
CREATE INDEX IF NOT EXISTS idx_policy_frameworks_triggers ON policy_frameworks USING gin(conditional_triggers);

-- Document classification indexes  
CREATE INDEX IF NOT EXISTS idx_document_classifications_keywords ON document_classifications USING gin(keywords);
CREATE INDEX IF NOT EXISTS idx_document_classifications_patterns ON document_classifications USING gin(file_patterns);

-- Policy matching indexes
CREATE INDEX IF NOT EXISTS idx_policy_document_matches_score ON policy_document_matches(match_score DESC);
CREATE INDEX IF NOT EXISTS idx_policy_document_matches_confidence ON policy_document_matches(confidence_level);

-- Conditional triggers indexes
CREATE INDEX IF NOT EXISTS idx_conditional_triggers_category ON conditional_triggers(trigger_category);
CREATE INDEX IF NOT EXISTS idx_conditional_triggers_conditions ON conditional_triggers USING gin(date_conditions, location_conditions, project_conditions);

-- Compliance topics indexes
CREATE INDEX IF NOT EXISTS idx_compliance_topics_keywords ON compliance_topics USING gin(primary_keywords, secondary_keywords);
CREATE INDEX IF NOT EXISTS idx_compliance_topics_category ON compliance_topics(topic_category);

-- =============================================================================
-- ENHANCEMENT 6: DATA POPULATION FUNCTIONS
-- Populates enhanced features with best-in-class data from ALL NEW APP analysis
-- =============================================================================

-- Function to populate compliance topics from ALL NEW APP analysis
CREATE OR REPLACE FUNCTION populate_compliance_topics()
RETURNS void AS $$
BEGIN
  -- Insert the 13 key compliance topics identified in ALL NEW APP analysis
  INSERT INTO compliance_topics (topic_name, topic_description, topic_category, primary_keywords, associated_cfr_sections) VALUES
  ('Cost Reasonableness', 'Evaluation of costs for reasonableness and necessity', 'Financial', 
   ARRAY['reasonable cost', 'cost analysis', 'market price', 'cost comparison', 'fair market value'], 
   ARRAY['2 CFR 200.404', '2 CFR 200.403']),
   
  ('Costing Protocols', 'Proper cost estimation and documentation procedures', 'Financial',
   ARRAY['cost estimation', 'CEF', 'unit cost', 'force account', 'cost documentation'],
   ARRAY['2 CFR 200.430', '2 CFR 200.431']),
   
  ('Consensus-Based Codes and Standards', 'Building codes and technical standards compliance', 'Technical',
   ARRAY['building code', 'CBCS', 'IBC', 'ASCE', 'substantial damage', 'flood resistant'],
   ARRAY['DRRA Section 1235b', 'Stafford Act §406(e)']),
   
  ('Environmental & Historic Preservation', 'Environmental and cultural resource compliance', 'Environmental',
   ARRAY['NEPA', 'NHPA', 'Section 106', 'EHP', 'environmental review', 'cultural resource'],
   ARRAY['44 CFR Part 9', '36 CFR 800', '40 CFR 1500-1508']),
   
  ('Management Costs (Category Z)', 'Administrative and management cost allowability', 'Financial',
   ARRAY['Category Z', 'management cost', 'administrative', 'indirect cost', 'program management'],
   ARRAY['2 CFR 200.414', 'PAPPG Category Z']),
   
  ('Project Formulation', 'Proper project development and scoping', 'Process',
   ARRAY['project formulation', 'scope development', 'project approval', 'alternative procedures'],
   ARRAY['PAPPG Chapter 3', '44 CFR 206.223']),
   
  ('NFIP Compliance', 'National Flood Insurance Program requirements', 'Regulatory',
   ARRAY['NFIP', 'floodplain', 'substantial damage', 'flood insurance', 'base flood elevation'],
   ARRAY['44 CFR Part 60', '44 CFR Part 206 Subpart I']),
   
  ('Procurement & Contracting', 'Federal procurement standards compliance', 'Financial',
   ARRAY['procurement', 'contracting', 'competitive bidding', 'sole source', 'contract provisions'],
   ARRAY['2 CFR 200.318-327', '2 CFR 200.320']),
   
  ('Insurance Requirements', 'Insurance coverage and reduction calculations', 'Financial',
   ARRAY['insurance', 'coverage', 'deductible', 'insurance reduction', 'insurance proceeds'],
   ARRAY['Stafford Act §311', '44 CFR Part 206 Subpart I']),
   
  ('Site Inspection Process', 'Damage assessment and validation procedures', 'Process',
   ARRAY['site inspection', 'damage assessment', 'field inspection', 'validation'],
   ARRAY['PAPPG Chapter 4', 'FEMA PA Guide']),
   
  ('Project Worksheet Preparation', 'PW development and documentation', 'Process',
   ARRAY['project worksheet', 'PW preparation', 'damage description', 'scope of work'],
   ARRAY['PAPPG Chapter 4', 'PW Preparation Guide']),
   
  ('Mitigation Requirements', 'Hazard mitigation and resilience measures', 'Technical',
   ARRAY['mitigation', 'hazard mitigation', 'resilience', '406 mitigation', 'risk reduction'],
   ARRAY['Stafford Act §406(e)', 'PAPPG mitigation']),
   
  ('Case Management File', 'Documentation and record keeping requirements', 'Administrative',
   ARRAY['case management', 'documentation', 'record keeping', 'file organization'],
   ARRAY['PAPPG Chapter 2', 'Documentation standards'])
   
  ON CONFLICT (topic_name) DO NOTHING;
  
  RAISE NOTICE 'Compliance topics populated successfully';
END;
$$ LANGUAGE plpgsql;

-- Function to populate document classifications from ALL NEW APP analysis
CREATE OR REPLACE FUNCTION populate_document_classifications()
RETURNS void AS $$
BEGIN
  INSERT INTO document_classifications (classification_name, classification_type, keywords, file_patterns, priority_boost) VALUES
  ('Building Code', 'Technical', 
   ARRAY['building_code', 'IBC', 'ASCE', 'NFPA', 'seismic', 'model_code'], 
   ARRAY['%building%code%', '%ibc%', '%asce%'], 1.5),
   
  ('Flood Information', 'Regulatory',
   ARRAY['flood', 'NFIP', 'floodplain', 'base_flood', 'flood_insurance', 'substantial_damage'],
   ARRAY['%flood%', '%nfip%', '%floodplain%'], 1.4),
   
  ('Insurance', 'Financial',
   ARRAY['insurance', 'coverage', 'policy', 'premium', 'deductible', 'claim'],
   ARRAY['%insurance%', '%coverage%', '%policy%'], 1.3),
   
  ('Mitigation', 'Technical',
   ARRAY['mitigation', 'hazard_mitigation', 'resilience', 'risk_reduction'],
   ARRAY['%mitigation%', '%resilience%'], 1.4),
   
  ('Environmental & Historic Preservation', 'Environmental',
   ARRAY['environmental', 'historic', 'EHP', 'NEPA', 'NHPA', 'Section_106'],
   ARRAY['%environmental%', '%historic%', '%ehp%'], 1.3),
   
  ('Form', 'Administrative',
   ARRAY['form', 'worksheet', 'FF-104', 'template', 'application', 'checklist'],
   ARRAY['%form%', '%worksheet%', '%ff-104%', '%.xlsx', '%.xls'], 1.2),
   
  ('Guidance', 'Administrative',
   ARRAY['guide', 'guidance', 'instruction', 'manual', 'procedure', 'advisory'],
   ARRAY['%guide%', '%guidance%', '%manual%'], 1.1),
   
  ('Memo', 'Administrative',
   ARRAY['memo', 'memorandum', 'directive', 'bulletin', 'circular'],
   ARRAY['%memo%', '%memorandum%'], 1.1),
   
  ('Job Aid', 'Administrative',
   ARRAY['job_aid', 'quick_reference', 'field_guide', 'desk_reference', 'toolkit'],
   ARRAY['%job%aid%', '%quick%guide%'], 1.2),
   
  ('Cost Reasonableness', 'Financial',
   ARRAY['cost_reasonableness', 'cost_analysis', 'price_analysis', 'market_price'],
   ARRAY['%cost%reasonable%', '%cost%analysis%'], 1.3)
   
  ON CONFLICT (classification_name) DO NOTHING;
  
  RAISE NOTICE 'Document classifications populated successfully';
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- INITIALIZATION: POPULATE ENHANCED FEATURES
-- =============================================================================

-- Populate the enhanced features
SELECT populate_compliance_topics();
SELECT populate_document_classifications();

-- Success confirmation
SELECT 'Enhanced schema integration completed - all existing functionality preserved' as status; 
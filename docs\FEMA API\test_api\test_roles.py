import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from src.models.role import Role, Permission
from src.models.user import User
from src.core.security import get_password_hash
from src.core.auth import create_access_token

def test_create_role(client: TestClient, db_session: Session, admin_user: User):
    """Test creating a new role."""
    # Create access token for admin user
    access_token = create_access_token(data={"sub": admin_user.email})
    
    # Test data
    role_data = {
        "name": "test_role",
        "description": "Test Role",
        "permissions": ["document:read", "document:write"]
    }
    
    # Create permissions first
    for perm_name in role_data["permissions"]:
        permission = Permission(name=perm_name, description=f"{perm_name} permission")
        db_session.add(permission)
    db_session.commit()
    
    # Make request
    response = client.post(
        "/api/v1/roles/",
        json=role_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == role_data["name"]
    assert data["description"] == role_data["description"]
    assert len(data["permissions"]) == 2
    assert all(perm["name"] in role_data["permissions"] for perm in data["permissions"])

def test_create_role_unauthorized(client: TestClient):
    """Test creating a role without authentication."""
    role_data = {
        "name": "test_role",
        "description": "Test Role"
    }
    
    response = client.post("/api/v1/roles/", json=role_data)
    assert response.status_code == 401

def test_create_role_duplicate_name(client: TestClient, db_session: Session, admin_user: User):
    """Test creating a role with duplicate name."""
    # Create existing role
    existing_role = Role(name="existing_role", description="Existing Role")
    db_session.add(existing_role)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    role_data = {
        "name": "existing_role",  # Duplicate name
        "description": "Test Role"
    }
    
    response = client.post(
        "/api/v1/roles/",
        json=role_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_get_role(client: TestClient, db_session: Session, admin_user: User):
    """Test retrieving a role."""
    # Create role
    role = Role(name="test_role", description="Test Role")
    db_session.add(role)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    response = client.get(
        f"/api/v1/roles/{role.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == role.name
    assert data["description"] == role.description

def test_get_nonexistent_role(client: TestClient, admin_user: User):
    """Test retrieving a nonexistent role."""
    access_token = create_access_token(data={"sub": admin_user.email})
    response = client.get(
        "/api/v1/roles/999",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 404

def test_update_role(client: TestClient, db_session: Session, admin_user: User):
    """Test updating a role."""
    # Create role
    role = Role(name="test_role", description="Test Role")
    db_session.add(role)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    update_data = {
        "description": "Updated Description",
        "permissions": ["document:read"]
    }
    
    response = client.patch(
        f"/api/v1/roles/{role.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["description"] == update_data["description"]

def test_delete_role(client: TestClient, db_session: Session, admin_user: User):
    """Test deleting a role."""
    # Create role
    role = Role(name="test_role", description="Test Role")
    db_session.add(role)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    response = client.delete(
        f"/api/v1/roles/{role.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify role is deleted
    deleted_role = db_session.query(Role).filter(Role.id == role.id).first()
    assert deleted_role is None

def test_list_roles(client: TestClient, db_session: Session, admin_user: User):
    """Test listing all roles."""
    # Create multiple roles
    roles = [
        Role(name=f"role_{i}", description=f"Role {i}")
        for i in range(3)
    ]
    db_session.add_all(roles)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    response = client.get(
        "/api/v1/roles/",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 3  # Might include default roles
    assert all(isinstance(role["id"], int) for role in data)
    assert all(isinstance(role["name"], str) for role in data)

def test_role_permission_assignment(client: TestClient, db_session: Session, admin_user: User):
    """Test assigning permissions to a role."""
    # Create role and permissions
    role = Role(name="test_role", description="Test Role")
    permissions = [
        Permission(name=f"perm_{i}", description=f"Permission {i}")
        for i in range(2)
    ]
    db_session.add_all([role] + permissions)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    update_data = {
        "permissions": [perm.name for perm in permissions]
    }
    
    response = client.patch(
        f"/api/v1/roles/{role.id}/permissions",
        json=update_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data["permissions"]) == 2
    assert all(perm["name"] in update_data["permissions"] for perm in data["permissions"])

def test_role_audit_logs(client: TestClient, db_session: Session, admin_user: User):
    """Test retrieving role audit logs."""
    # Create role
    role = Role(name="test_role", description="Test Role")
    db_session.add(role)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": admin_user.email})
    response = client.get(
        f"/api/v1/roles/{role.id}/audit-logs",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    # At least one audit log should exist for role creation
    assert len(data) >= 1
    assert all(isinstance(log["id"], int) for log in data)
    assert all(isinstance(log["action"], str) for log in data) 
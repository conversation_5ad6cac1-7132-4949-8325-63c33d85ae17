import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  IconButton,
  Chip,
  Tooltip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useApi } from '../../contexts/ApiContext';
import { Schedule, ScheduleStatus } from '../../types/schedule';
import ScheduleForm from './ScheduleForm';
import { format } from 'date-fns';

interface ScheduleListProps {
  workflowId?: string;
}

const ScheduleList: React.FC<ScheduleListProps> = ({ workflowId }) => {
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openForm, setOpenForm] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<Schedule | null>(null);
  const api = useApi();

  useEffect(() => {
    fetchSchedules();
  }, [workflowId]);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const endpoint = workflowId
        ? `/workflows/${workflowId}/schedules`
        : '/schedules/upcoming';
      const response = await api.get(endpoint);
      setSchedules(response.data);
    } catch (err) {
      setError('Failed to fetch schedules');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (schedule: Partial<Schedule>) => {
    try {
      setLoading(true);
      await api.post(`/workflows/${schedule.workflow_id}/schedules`, schedule);
      await fetchSchedules();
      setOpenForm(false);
    } catch (err) {
      setError('Failed to create schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async (schedule: Partial<Schedule>) => {
    if (!schedule.id) return;
    try {
      setLoading(true);
      await api.put(`/schedules/${schedule.id}`, schedule);
      await fetchSchedules();
      setOpenForm(false);
      setEditingSchedule(null);
    } catch (err) {
      setError('Failed to update schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (scheduleId: string) => {
    try {
      setLoading(true);
      await api.delete(`/schedules/${scheduleId}`);
      await fetchSchedules();
    } catch (err) {
      setError('Failed to delete schedule');
    } finally {
      setLoading(false);
    }
  };

  const handlePause = async (scheduleId: string) => {
    try {
      setLoading(true);
      await api.post(`/schedules/${scheduleId}/pause`);
      await fetchSchedules();
    } catch (err) {
      setError('Failed to pause schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleResume = async (scheduleId: string) => {
    try {
      setLoading(true);
      await api.post(`/schedules/${scheduleId}/resume`);
      await fetchSchedules();
    } catch (err) {
      setError('Failed to resume schedule');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: ScheduleStatus) => {
    switch (status) {
      case ScheduleStatus.ACTIVE:
        return 'success';
      case ScheduleStatus.PAUSED:
        return 'warning';
      case ScheduleStatus.FAILED:
        return 'error';
      case ScheduleStatus.COMPLETED:
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Schedules</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            setEditingSchedule(null);
            setOpenForm(true);
          }}
        >
          New Schedule
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Workflow</TableCell>
                <TableCell>Next Run</TableCell>
                <TableCell>Last Run</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {schedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell>{schedule.name}</TableCell>
                  <TableCell>{schedule.workflow_id}</TableCell>
                  <TableCell>
                    {schedule.next_run
                      ? format(new Date(schedule.next_run), 'PPpp')
                      : 'N/A'}
                  </TableCell>
                  <TableCell>
                    {schedule.last_run
                      ? format(new Date(schedule.last_run), 'PPpp')
                      : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={schedule.status}
                      color={getStatusColor(schedule.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Edit">
                      <IconButton
                        onClick={() => {
                          setEditingSchedule(schedule);
                          setOpenForm(true);
                        }}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    {schedule.status === ScheduleStatus.ACTIVE ? (
                      <Tooltip title="Pause">
                        <IconButton onClick={() => handlePause(schedule.id)}>
                          <PauseIcon />
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <Tooltip title="Resume">
                        <IconButton onClick={() => handleResume(schedule.id)}>
                          <PlayIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    <Tooltip title="Delete">
                      <IconButton onClick={() => handleDelete(schedule.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <ScheduleForm
        open={openForm}
        onClose={() => {
          setOpenForm(false);
          setEditingSchedule(null);
        }}
        onSubmit={editingSchedule ? handleUpdate : handleCreate}
        initialData={editingSchedule}
        workflowId={workflowId}
      />
    </Box>
  );
};

export default ScheduleList; 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self';">
    <title>Document Upload & Analysis System - ComplianceMax</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <link rel="stylesheet" href="ui/sidewalk-hints.css">
    <!-- <link rel="stylesheet" href="ui/sidewalk.css"> QUARANTINED: moved to legacy/sidewalk/ -->
    <link rel="stylesheet" href="print.css" media="print">
    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <script defer src="fema_compliance_engine.js"></script>
    <script defer src="comprehensive_pod_system.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Lato', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 80px 20px 20px 20px;
        }

        .container {
            max-width: 1400px;
            width: 95%;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #253464 0%, #1e2a5a 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }

        .section-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #253464;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-zone {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .upload-zone:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .upload-zone.dragover {
            border-color: #667eea;
            background: #e8f2ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .document-tags {
            margin-bottom: 20px;
        }

        .tag-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .tag-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .tag-option:hover {
            border-color: #667eea;
        }

        .tag-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
            color: #667eea;
            font-weight: bold;
        }

        .uploaded-files {
            background: white;
            border-radius: 15px;
            padding: 25px;
        }

        .file-item {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .file-item:hover {
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .file-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .file-name {
            font-weight: bold;
            color: #253464;
            flex: 1;
        }

        .file-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-processing {
            background: #fff3cd;
            color: #856404;
        }

        .status-complete {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .file-details {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
        }

        .analysis-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .analysis-section {
            margin-bottom: 15px;
        }

        .analysis-title {
            font-weight: bold;
            color: #253464;
            margin-bottom: 8px;
        }

        .analysis-content {
            font-size: 0.9em;
            line-height: 1.5;
        }

        .cost-analysis {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin: 10px 0;
        }

        .compliance-check {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }

        .progress-bar {
            background: #eee;
            border-radius: 10px;
            height: 6px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 15px;
            font-size: 0.8em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #5a6fd8;
        }

        .action-btn.secondary {
            background: #6c757d;
        }

        .action-btn.secondary:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            .tag-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body data-page="upload" data-flow="professional" data-step="4">
<div id="universal-nav"></div>

<main id="main" class="container">
    <div class="container">
        <div class="header">
            <a href="#" class="back-btn" id="backBtn">← Back</a>
            <div class="title">📄 Document Upload & Analysis</div>
            <div class="subtitle">AI-Powered FEMA Compliance Processing</div>
        </div>

        <div class="main-content">
            <!-- Upload Section -->
            <div class="upload-section">
                <div class="section-title">
                    📤 Upload Documents
                </div>

                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        Drag & drop files here or click to browse
                    </div>
                    <div style="font-size: 0.9em; color: #999; margin-bottom: 15px;">
                        Supports: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (Max 50MB each)
                    </div>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose Files
                    </button>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" style="display: none;">
                </div>

                <div class="document-tags">
                    <div class="section-title" style="font-size: 1.1em;">
                        🏷️ Document Type (Auto-detected via OCR)
                    </div>
                    <div class="tag-grid" id="tagGrid">
                        <div class="tag-option" data-tag="invoice">📄 Invoices</div>
                        <div class="tag-option" data-tag="receipt">🧾 Receipts</div>
                        <div class="tag-option" data-tag="force-account-labor">👷 Force Account Labor</div>
                        <div class="tag-option" data-tag="force-account-materials">🧱 Force Account Materials</div>
                        <div class="tag-option" data-tag="force-account-equipment">🚛 Force Account Equipment</div>
                        <div class="tag-option" data-tag="contract">📋 Contracts</div>
                        <div class="tag-option" data-tag="timesheet">⏰ Timesheets</div>
                        <div class="tag-option" data-tag="damage-photos">📸 Damage Photos</div>
                        <div class="tag-option" data-tag="insurance">🛡️ Insurance Docs</div>
                        <div class="tag-option" data-tag="permits">📜 Permits</div>
                        <div class="tag-option" data-tag="environmental">🌿 Environmental</div>
                        <div class="tag-option" data-tag="other">📎 Other</div>
                    </div>
                </div>

                <div style="background: #fff3cd; border-radius: 10px; padding: 15px; margin-top: 20px;">
                    <strong>🤖 AI Processing Pipeline:</strong><br>
                    1. OCR text extraction<br>
                    2. Document type classification<br>
                    3. Cost data extraction & validation<br>
                    4. FEMA compliance analysis<br>
                    5. Auto-populate FEMA forms
                </div>
            </div>

            <!-- Analysis Section -->
            <div class="uploaded-files">
                <div class="section-title">
                    📊 Document Analysis & Processing
                </div>

                <div id="filesList">
                    <div style="text-align: center; color: #999; padding: 40px;">
                        No documents uploaded yet.<br>
                        Upload documents to see AI analysis results.
                    </div>
                </div>

                <div style="background: #e8f5e8; border-radius: 10px; padding: 15px; margin-top: 20px;">
                    <strong>📋 Auto-Generated FEMA Forms:</strong><br>
                    • FEMA Form 90-91 (Project Worksheet)<br>
                    • Site Worksheets<br>
                    • Cost Estimates<br>
                    • Force Account Summaries<br>
                    • Compliance Checklists
                </div>
            </div>
        </div>
    </div>

    <script>
        let uploadedFiles = [];
        let selectedTags = [];

        // Handle return navigation from URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const returnUrl = urlParams.get('return');
            const context = urlParams.get('context');

            // Update back button
            const backBtn = document.getElementById('backBtn');
            if (returnUrl) {
                backBtn.href = decodeURIComponent(returnUrl);
                backBtn.title = `Return to ${context || 'previous page'}`;
            } else {
                backBtn.href = 'emergency_intake.html';
            }

            // Show context-specific message
            if (context) {
                const contextMessage = document.createElement('div');
                contextMessage.style.cssText = `
                    background: rgba(59, 130, 246, 0.1);
                    border: 1px solid rgba(59, 130, 246, 0.3);
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 20px;
                    color: #1e40af;
                    font-size: 14px;
                `;
                contextMessage.innerHTML = `
                    <strong>📍 Context:</strong> ${getContextMessage(context)}
                    ${returnUrl ? '<br><em>Files uploaded here will be processed and you\'ll return to your previous workflow.</em>' : ''}
                `;
                document.querySelector('.main-content').insertBefore(contextMessage, document.querySelector('.upload-section'));
            }
        });

        function getContextMessage(context) {
            switch(context) {
                case 'compliance-review': return 'Uploading documents for compliance review workflow';
                case 'wizard-step': return 'Uploading documents as part of wizard process';
                case 'quick-check': return 'Uploading documents for quick compliance check';
                case 'policy-matcher': return 'Uploading requirements document for policy matching';
                default: return 'Document upload and analysis';
            }
        }

        // Document type definitions for OCR classification
        const documentTypes = {
            'invoice': {
                keywords: ['invoice', 'bill', 'amount due', 'total', 'payment'],
                icon: '📄',
                costRelevant: true
            },
            'receipt': {
                keywords: ['receipt', 'paid', 'transaction', 'purchase'],
                icon: '🧾',
                costRelevant: true
            },
            'force-account-labor': {
                keywords: ['labor', 'hours', 'wage', 'overtime', 'employee'],
                icon: '👷',
                costRelevant: true
            },
            'force-account-materials': {
                keywords: ['materials', 'supplies', 'concrete', 'steel', 'lumber'],
                icon: '🧱',
                costRelevant: true
            },
            'force-account-equipment': {
                keywords: ['equipment', 'rental', 'machinery', 'truck', 'excavator'],
                icon: '🚛',
                costRelevant: true
            },
            'contract': {
                keywords: ['contract', 'agreement', 'contractor', 'scope of work'],
                icon: '📋',
                costRelevant: true
            },
            'timesheet': {
                keywords: ['timesheet', 'hours worked', 'time log', 'daily report'],
                icon: '⏰',
                costRelevant: true
            },
            'damage-photos': {
                keywords: ['damage', 'before', 'after', 'photo', 'image'],
                icon: '📸',
                costRelevant: false
            },
            'insurance': {
                keywords: ['insurance', 'policy', 'claim', 'coverage', 'deductible'],
                icon: '🛡️',
                costRelevant: false
            },
            'permits': {
                keywords: ['permit', 'license', 'approval', 'authorization'],
                icon: '📜',
                costRelevant: false
            },
            'environmental': {
                keywords: ['environmental', 'EHP', 'historic', 'cultural', 'endangered'],
                icon: '🌿',
                costRelevant: false
            }
        };

        // File upload handling
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');

        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        // Tag selection
        document.querySelectorAll('.tag-option').forEach(tag => {
            tag.addEventListener('click', function() {
                this.classList.toggle('selected');
                const tagValue = this.dataset.tag;
                if (selectedTags.includes(tagValue)) {
                    selectedTags = selectedTags.filter(t => t !== tagValue);
                } else {
                    selectedTags.push(tagValue);
                }
            });
        });

        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (file.size > 50 * 1024 * 1024) {
                    alert(`File ${file.name} is too large. Maximum size is 50MB.`);
                    return;
                }

                const fileId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const fileObj = {
                    id: fileId,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    file: file,
                    status: 'processing',
                    progress: 0,
                    analysis: null
                };

                uploadedFiles.push(fileObj);
                renderFilesList();
                processFile(fileObj);
            });
        }

        function renderFilesList() {
            const filesList = document.getElementById('filesList');
            
            if (uploadedFiles.length === 0) {
                filesList.innerHTML = `
                    <div style="text-align: center; color: #999; padding: 40px;">
                        No documents uploaded yet.<br>
                        Upload documents to see AI analysis results.
                    </div>
                `;
                return;
            }

            filesList.innerHTML = uploadedFiles.map(file => `
                <div class="file-item">
                    <div class="file-header">
                        <div class="file-name">${file.name}</div>
                        <div class="file-status status-${file.status}">
                            ${file.status === 'processing' ? '⏳ Processing' : 
                              file.status === 'complete' ? '✅ Complete' : 
                              '❌ Error'}
                        </div>
                    </div>
                    <div class="file-details">
                        Size: ${formatFileSize(file.size)} | Type: ${file.type}
                    </div>
                    ${file.status === 'processing' ? `
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${file.progress}%"></div>
                        </div>
                    ` : ''}
                    ${file.analysis ? renderAnalysis(file.analysis) : ''}
                    ${file.status === 'complete' ? `
                        <div class="action-buttons">
                            <button class="action-btn" onclick="generateFEMAForm('${file.id}')">📋 Generate FEMA Form</button>
                            <button class="action-btn" onclick="viewCostAnalysis('${file.id}')">💰 Cost Analysis</button>
                            <button class="action-btn secondary" onclick="downloadResults('${file.id}')">📥 Download</button>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        function renderAnalysis(analysis) {
            return `
                <div class="analysis-results">
                    <div class="analysis-section">
                        <div class="analysis-title">🤖 Document Classification</div>
                        <div class="analysis-content">
                            Type: <strong>${analysis.documentType}</strong> (${analysis.confidence}% confidence)<br>
                            OCR Text Extracted: ${analysis.textLength} characters
                        </div>
                    </div>
                    ${analysis.costData ? `
                        <div class="cost-analysis">
                            <div class="analysis-title">💰 Cost Data Extracted</div>
                            <div class="analysis-content">
                                Total Amount: <strong>$${analysis.costData.total}</strong><br>
                                Line Items: ${analysis.costData.lineItems}<br>
                                Reasonable Cost Check: ${analysis.costData.reasonable ? '✅ Passed' : '⚠️ Needs Review'}
                            </div>
                        </div>
                    ` : ''}
                    <div class="compliance-check">
                        <div class="analysis-title">📋 FEMA Compliance Analysis</div>
                        <div class="analysis-content">
                            PAPPG Compliance: ${analysis.compliance.pappg ? '✅' : '❌'}<br>
                            Documentation Complete: ${analysis.compliance.documentation}%<br>
                            Recommended Actions: ${analysis.compliance.actions}
                        </div>
                    </div>
                </div>
            `;
        }

        async function processFile(fileObj) {
            try {
                console.log(`🔍 Starting comprehensive pod analysis for: ${fileObj.name}`);

                // Enhanced processing pipeline with pod routing
                const steps = [
                    { name: 'Extracting Document Content (OCR)', duration: 2000, progress: 15 },
                    { name: 'Routing to Analysis Pods', duration: 1500, progress: 30 },
                    { name: 'Pod-Specific Analysis', duration: 2500, progress: 55 },
                    { name: 'FEMA Compliance Validation', duration: 2000, progress: 75 },
                    { name: 'Cross-Pod Integration', duration: 1500, progress: 90 },
                    { name: 'Generating Actionable Recommendations', duration: 1000, progress: 100 }
                ];

                // Step 1: Extract document content
                fileObj.currentStep = steps[0].name;
                fileObj.progress = steps[0].progress;
                renderFilesList();
                await new Promise(resolve => setTimeout(resolve, steps[0].duration));

                const extractedData = await extractDocumentContent(fileObj);

                // Step 2: Route to appropriate pods
                fileObj.currentStep = steps[1].name;
                fileObj.progress = steps[1].progress;
                renderFilesList();
                await new Promise(resolve => setTimeout(resolve, steps[1].duration));

                const podRouting = await comprehensivePodSystem.routeDocumentToPods(fileObj, extractedData);

                // Step 3: Pod-specific analysis
                fileObj.currentStep = `${steps[2].name} (${comprehensivePodSystem.pods[podRouting.primary_pod]?.name})`;
                fileObj.progress = steps[2].progress;
                renderFilesList();
                await new Promise(resolve => setTimeout(resolve, steps[2].duration));

                // Run pod-specific analysis
                const podAnalysisResults = await runPodAnalysis(extractedData, podRouting);

                // Step 4: FEMA compliance validation
                fileObj.currentStep = steps[3].name;
                fileObj.progress = steps[3].progress;
                renderFilesList();
                await new Promise(resolve => setTimeout(resolve, steps[3].duration));

                // Get project context for analysis
                const projectData = {
                    category: fileObj.selectedTags?.includes('permanent-work') ? 'C' :
                             fileObj.selectedTags?.includes('emergency-work') ? 'B' : 'C',
                    drNumber: 'FEMA-DR-4000',
                    incidentDate: '2024-01-15',
                    applicantType: 'local_government',
                    estimatedCost: 500000
                };

                // Run compliance analysis
                const complianceResults = await femaComplianceEngine.analyzeDocument(fileObj, projectData);

                // Step 5: Cross-pod integration
                fileObj.currentStep = steps[4].name;
                fileObj.progress = steps[4].progress;
                renderFilesList();
                await new Promise(resolve => setTimeout(resolve, steps[4].duration));

                // Integrate pod results with compliance analysis
                const integratedResults = await integratePodResults(podAnalysisResults, complianceResults, podRouting);

                // Step 6: Generate recommendations
                fileObj.currentStep = steps[5].name;
                fileObj.progress = steps[5].progress;
                renderFilesList();
                await new Promise(resolve => setTimeout(resolve, steps[5].duration));

                // Transform results for UI with pod information
                fileObj.analysis = {
                    // Pod routing information
                    podRouting: {
                        primary_pod: podRouting.primary_pod,
                        primary_pod_name: comprehensivePodSystem.pods[podRouting.primary_pod]?.name,
                        secondary_pods: podRouting.secondary_pods,
                        workflow_stage: podRouting.workflow_stage,
                        analysis_pipeline: podRouting.analysis_pipeline,
                        confidence_scores: podRouting.confidence_scores,
                        routing_rationale: podRouting.routing_rationale
                    },

                    // Document classification
                    documentType: complianceResults.documentClassification?.primary_type || 'unknown',
                    confidence: Math.round((complianceResults.documentClassification?.confidence || 0) * 100),

                    // Compliance scoring
                    complianceScore: complianceResults.complianceScore || 0,
                    complianceStatus: complianceResults.status === 'compliant' ? 'Compliant' : 'Needs Action',

                    // Pod-specific analysis results
                    podAnalysis: podAnalysisResults,

                    // Workbook population (if triggered)
                    workbookPopulation: podRouting.triggers_workbook ? {
                        required: true,
                        auto_populate: podAnalysisResults.auto_populate || false,
                        tabs_affected: podAnalysisResults.workbook_tabs || [],
                        extracted_data: podAnalysisResults.extracted_data || {}
                    } : null,

                    // Integrated compliance results
                    compliance: {
                        pappg: complianceResults.policyValidation?.pappg_compliance?.compliance_score > 80,
                        documentation: complianceResults.complianceScore || 0,
                        actions: complianceResults.recommendations?.length || 0,
                        criticalIssues: complianceResults.criticalGaps?.length || 0,
                        pod_specific_issues: podAnalysisResults.compliance_issues || []
                    },

                    // Cost analysis (if applicable)
                    costData: integratedResults.costAnalysis || null,

                    // Comprehensive recommendations
                    recommendations: integratedResults.recommendations || [],
                    nextSteps: integratedResults.nextSteps || [],
                    regulatoryCitations: integratedResults.regulatoryCitations || [],

                    // Raw analysis for detailed view
                    detailedResults: {
                        podAnalysis: podAnalysisResults,
                        complianceAnalysis: complianceResults,
                        integratedResults: integratedResults
                    }
                };

                fileObj.status = 'complete';
                fileObj.currentStep = 'Pod Analysis Complete';
                renderFilesList();

                console.log(`✅ Comprehensive pod analysis completed for: ${fileObj.name}`);
                console.log(`📋 Primary pod: ${podRouting.primary_pod} (${comprehensivePodSystem.pods[podRouting.primary_pod]?.name})`);

                // Trigger workbook population if required
                if (podRouting.triggers_workbook && podAnalysisResults.auto_populate) {
                    console.log(`📊 Auto-populating FEMA Project Workbook from ${podRouting.primary_pod} pod analysis`);
                    await populateWorkbookFromPodData(fileObj, podAnalysisResults);
                }

            } catch (error) {
                console.error(`❌ Analysis failed for ${fileObj.name}:`, error);

                // Fallback to basic analysis
                fileObj.analysis = generateFallbackAnalysis(fileObj, error);
                fileObj.status = 'error';
                fileObj.currentStep = 'Analysis Failed';
                renderFilesList();
            }
        }

        /**
         * Extract document content (simulated OCR)
         */
        async function extractDocumentContent(fileObj) {
            // Simulate OCR extraction
            return {
                text: `Extracted content from ${fileObj.name}...`,
                metadata: {
                    pages: Math.floor(Math.random() * 10) + 1,
                    file_size: fileObj.size,
                    file_type: fileObj.name.split('.').pop()
                }
            };
        }

        /**
         * Run pod-specific analysis
         */
        async function runPodAnalysis(extractedData, podRouting) {
            const primaryPod = comprehensivePodSystem.pods[podRouting.primary_pod];

            const podResults = {
                pod_id: podRouting.primary_pod,
                pod_name: primaryPod.name,
                analysis_type: primaryPod.analysis_type,
                workflow_stage: primaryPod.workflow_stage,
                compliance_issues: [],
                extracted_data: {},
                auto_populate: false,
                workbook_tabs: primaryPod.workbook_tabs || []
            };

            // Pod-specific analysis based on type
            switch (podRouting.primary_pod) {
                case 'costs':
                    podResults.extracted_data = await extractCostData(extractedData);
                    podResults.auto_populate = true;
                    podResults.cost_reasonableness_check = true;
                    break;

                case 'force_account':
                    podResults.extracted_data = await extractForceAccountData(extractedData);
                    podResults.auto_populate = true;
                    podResults.rate_validation = true;
                    break;

                case 'procurement':
                    podResults.extracted_data = await extractProcurementData(extractedData);
                    podResults.auto_populate = true;
                    podResults.competition_validation = true;
                    break;

                case 'damage_assessment':
                    podResults.extracted_data = await extractDamageData(extractedData);
                    podResults.photo_validation = true;
                    break;

                case 'engineering':
                    podResults.extracted_data = await extractEngineeringData(extractedData);
                    podResults.cbcs_validation = true;
                    break;

                case 'environmental':
                    podResults.extracted_data = await extractEnvironmentalData(extractedData);
                    podResults.ehp_clearance_tracking = true;
                    break;

                case 'insurance':
                    podResults.extracted_data = await extractInsuranceData(extractedData);
                    podResults.auto_populate = true;
                    podResults.dob_calculation = true;
                    break;
            }

            return podResults;
        }

        /**
         * Extract cost data from documents
         */
        async function extractCostData(extractedData) {
            return {
                total_cost: Math.floor(Math.random() * 500000) + 50000,
                labor_costs: Math.floor(Math.random() * 200000) + 20000,
                equipment_costs: Math.floor(Math.random() * 150000) + 15000,
                materials_costs: Math.floor(Math.random() * 100000) + 10000,
                line_items: Math.floor(Math.random() * 20) + 5,
                vendor_info: 'Extracted vendor information',
                cost_breakdown_available: true
            };
        }

        /**
         * Extract force account data
         */
        async function extractForceAccountData(extractedData) {
            return {
                labor_hours: Math.floor(Math.random() * 500) + 100,
                equipment_hours: Math.floor(Math.random() * 300) + 50,
                hourly_rates: {
                    labor: Math.floor(Math.random() * 50) + 25,
                    equipment: Math.floor(Math.random() * 100) + 50
                },
                productivity_data: 'Productivity analysis available',
                forms_present: ['FF-104-FY-21-137', 'FF-104-FY-21-141']
            };
        }

        /**
         * Integrate pod results with compliance analysis
         */
        async function integratePodResults(podResults, complianceResults, podRouting) {
            const integrated = {
                costAnalysis: null,
                recommendations: [],
                nextSteps: [],
                regulatoryCitations: [],
                workbook_population: null
            };

            // Integrate cost analysis if applicable
            if (podRouting.primary_pod === 'costs' || podRouting.primary_pod === 'force_account' || podRouting.primary_pod === 'procurement') {
                integrated.costAnalysis = {
                    hasAnalysis: true,
                    total_cost: podResults.extracted_data.total_cost || 0,
                    reasonable: Math.random() > 0.3,
                    red_flags: Math.floor(Math.random() * 3),
                    workbook_required: true
                };
            }

            // Combine recommendations
            integrated.recommendations = [
                ...(complianceResults.recommendations || []),
                ...generatePodSpecificRecommendations(podResults)
            ];

            // Generate next steps
            integrated.nextSteps = generateIntegratedNextSteps(podResults, complianceResults);

            // Combine regulatory citations
            integrated.regulatoryCitations = [
                ...(complianceResults.regulatoryCitations || []),
                ...getPodPolicyReferences(podRouting.primary_pod)
            ];

            return integrated;
        }

        /**
         * Generate pod-specific recommendations
         */
        function generatePodSpecificRecommendations(podResults) {
            const recommendations = [];

            switch (podResults.pod_id) {
                case 'costs':
                    recommendations.push({
                        priority: 'high',
                        title: 'Complete Cost Documentation',
                        description: 'Ensure all cost documentation meets FEMA reasonableness standards',
                        specific_actions: [
                            'Verify all invoices and receipts are included',
                            'Conduct market rate comparison',
                            'Document cost estimation methodology'
                        ]
                    });
                    break;

                case 'force_account':
                    recommendations.push({
                        priority: 'high',
                        title: 'Validate Force Account Documentation',
                        description: 'Ensure force account logs meet FEMA requirements',
                        specific_actions: [
                            'Verify labor and equipment logs are complete',
                            'Validate hourly rates against FEMA schedule',
                            'Document productivity standards'
                        ]
                    });
                    break;

                case 'damage_assessment':
                    recommendations.push({
                        priority: 'medium',
                        title: 'Complete Damage Documentation',
                        description: 'Ensure damage assessment meets FEMA standards',
                        specific_actions: [
                            'Verify GPS coordinates on all photos',
                            'Complete damage inventory forms',
                            'Document cause of damage'
                        ]
                    });
                    break;
            }

            return recommendations;
        }

        /**
         * Populate workbook from pod data
         */
        async function populateWorkbookFromPodData(fileObj, podResults) {
            if (!podResults.auto_populate || !podResults.extracted_data) return;

            console.log(`📊 Auto-populating workbook with data from ${podResults.pod_name}`);

            // This would integrate with the actual FEMA workbook system
            // For now, just log the population
            const populationSummary = {
                source_pod: podResults.pod_name,
                tabs_populated: podResults.workbook_tabs,
                data_summary: podResults.extracted_data,
                auto_calculations: true
            };

            console.log('📋 Workbook population summary:', populationSummary);
        }

        function generateFallbackAnalysis(fileObj, error) {
            return {
                documentType: 'unknown',
                confidence: 0,
                complianceScore: 0,
                complianceStatus: 'Analysis Failed',
                podRouting: {
                    primary_pod: 'quality_assurance',
                    primary_pod_name: 'Quality Assurance Pod',
                    workflow_stage: 'manual_review',
                    routing_rationale: ['Analysis failed - manual review required']
                },
                compliance: {
                    pappg: false,
                    documentation: 0,
                    actions: 1,
                    criticalIssues: 1
                },
                recommendations: [{
                    priority: 'critical',
                    title: 'Manual Review Required',
                    description: `Automated analysis failed: ${error.message}`,
                    specific_actions: [
                        'Review document manually',
                        'Contact system administrator',
                        'Verify document format and content'
                    ]
                }],
                error: error.message
            };
        }

        function generateMockAnalysis(fileObj) {
            const fileName = fileObj.name.toLowerCase();
            let documentType = 'other';
            let confidence = 85;

            // Simple classification based on filename
            for (const [type, config] of Object.entries(documentTypes)) {
                if (config.keywords.some(keyword => fileName.includes(keyword))) {
                    documentType = type;
                    confidence = 92;
                    break;
                }
            }

            const analysis = {
                documentType: documentType,
                confidence: confidence,
                textLength: Math.floor(Math.random() * 5000) + 500,
                compliance: {
                    pappg: Math.random() > 0.2,
                    documentation: Math.floor(Math.random() * 30) + 70,
                    actions: Math.floor(Math.random() * 3) + 1
                }
            };

            // Add cost data if relevant
            if (documentTypes[documentType]?.costRelevant) {
                analysis.costData = {
                    total: (Math.random() * 50000 + 1000).toFixed(2),
                    lineItems: Math.floor(Math.random() * 10) + 1,
                    reasonable: Math.random() > 0.3
                };
            }

            return analysis;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function generateFEMAForm(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);

            // Create FEMA form generation modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 1000; display: flex;
                align-items: center; justify-content: center; padding: 20px;
            `;

            const femaForms = generateFEMAFormData(file);

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 900px; width: 100%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #253464; margin: 0;">📋 FEMA Form Generation: ${file.name}</h2>
                        <button onclick="this.closest('.modal').remove()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        ${femaForms.map(form => `
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #667eea;">
                                <h3 style="color: #253464; margin-bottom: 10px;">${form.icon} ${form.name}</h3>
                                <p style="font-size: 0.9em; color: #666; margin-bottom: 10px;">${form.description}</p>
                                <div style="font-size: 0.8em; color: #28a745;">
                                    <strong>Auto-populated fields:</strong><br>
                                    ${form.fields.join('<br>')}
                                </div>
                                <button onclick="downloadForm('${form.id}', '${fileId}')" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 15px; margin-top: 10px; cursor: pointer; width: 100%;">
                                    📥 Download ${form.name}
                                </button>
                            </div>
                        `).join('')}
                    </div>

                    <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #1976d2; margin-bottom: 10px;">🤖 AI Auto-Population Status</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 0.9em;">
                            <div>✅ Project Information: 100%</div>
                            <div>✅ Cost Data: ${file.analysis.costData ? '95%' : '0%'}</div>
                            <div>✅ Damage Description: 90%</div>
                            <div>✅ Location Data: 85%</div>
                            <div>⚠️ Insurance Information: 60%</div>
                            <div>⚠️ Environmental Review: 45%</div>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="generateAllForms('${fileId}')" style="background: #667eea; color: white; border: none; padding: 15px 30px; border-radius: 25px; margin-right: 10px; cursor: pointer; font-weight: bold;">📦 Generate All Forms</button>
                        <button onclick="this.closest('.modal').remove()" style="background: #6c757d; color: white; border: none; padding: 15px 30px; border-radius: 25px; cursor: pointer;">Close</button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        function generateFEMAFormData(file) {
            const documentType = file.analysis.documentType;
            const hasCostData = file.analysis.costData !== null;

            return [
                {
                    id: 'form-90-91',
                    name: 'FEMA Form 90-91',
                    icon: '📋',
                    description: 'Project Worksheet - Primary FEMA PA application form',
                    fields: [
                        '• Applicant Information',
                        '• Project Location & Description',
                        '• Damage Assessment',
                        hasCostData ? '• Cost Estimates (from uploaded docs)' : '• Cost Estimates (manual entry required)',
                        '• Category of Work',
                        '• Insurance Information'
                    ]
                },
                {
                    id: 'site-worksheet',
                    name: 'Site Worksheet',
                    icon: '📍',
                    description: 'Detailed site-specific damage documentation',
                    fields: [
                        '• Site Location & GPS Coordinates',
                        '• Facility Information',
                        '• Pre-disaster Condition',
                        '• Damage Description',
                        '• Scope of Work',
                        '• Photos & Documentation Links'
                    ]
                },
                {
                    id: 'cost-estimate',
                    name: 'Cost Estimate Worksheet',
                    icon: '💰',
                    description: 'Detailed cost breakdown and justification',
                    fields: [
                        hasCostData ? '• Labor Costs (extracted from docs)' : '• Labor Costs (manual entry)',
                        hasCostData ? '• Material Costs (extracted from docs)' : '• Material Costs (manual entry)',
                        hasCostData ? '• Equipment Costs (extracted from docs)' : '• Equipment Costs (manual entry)',
                        '• Overhead & Profit Calculations',
                        '• Reasonable Cost Analysis',
                        '• Procurement Method Justification'
                    ]
                },
                {
                    id: 'force-account',
                    name: 'Force Account Summary',
                    icon: '👷',
                    description: 'Summary of force account labor, equipment, and materials',
                    fields: [
                        documentType.includes('force-account') ? '• Labor Hours (from uploaded timesheets)' : '• Labor Hours (manual entry)',
                        documentType.includes('force-account') ? '• Equipment Usage (from uploaded logs)' : '• Equipment Usage (manual entry)',
                        documentType.includes('force-account') ? '• Material Quantities (from uploaded receipts)' : '• Material Quantities (manual entry)',
                        '• Rate Justifications',
                        '• Supervisor Certifications',
                        '• Daily Work Reports'
                    ]
                },
                {
                    id: 'compliance-checklist',
                    name: 'Compliance Checklist',
                    icon: '✅',
                    description: 'FEMA PA compliance verification checklist',
                    fields: [
                        '• PAPPG Compliance Verification',
                        '• Environmental & Historic Preservation',
                        '• Procurement Requirements',
                        '• Insurance Coordination',
                        '• Duplication of Benefits Check',
                        '• Required Documentation Status'
                    ]
                }
            ];
        }

        function downloadForm(formId, fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);
            alert(`📥 Downloading ${formId} for ${file.name}\n\nForm includes:\n• Auto-populated data from AI analysis\n• Pre-filled compliance sections\n• Embedded supporting documentation\n• Digital signatures ready\n\nForm will be available in PDF format in 15 seconds.`);
        }

        function generateAllForms(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);
            alert(`📦 Generating Complete FEMA PA Package for ${file.name}\n\nPackage includes:\n• All required FEMA forms\n• Supporting documentation\n• Compliance checklists\n• Cost analysis reports\n• Digital submission package\n\nEstimated completion: 2 minutes`);
        }

        function viewCostAnalysis(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);

            // Create detailed cost analysis modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 1000; display: flex;
                align-items: center; justify-content: center; padding: 20px;
            `;

            const costData = file.analysis.costData;
            const reasonableCostAnalysis = generateReasonableCostAnalysis(costData, file.analysis.documentType);

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; padding: 30px; max-width: 800px; width: 100%; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2 style="color: #253464; margin: 0;">💰 Cost Analysis: ${file.name}</h2>
                        <button onclick="this.closest('.modal').remove()" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                            <h3 style="color: #28a745; margin-bottom: 10px;">📊 Extracted Cost Data</h3>
                            <p><strong>Total Amount:</strong> $${costData.total}</p>
                            <p><strong>Line Items:</strong> ${costData.lineItems}</p>
                            <p><strong>Document Type:</strong> ${file.analysis.documentType}</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                            <h3 style="color: #007bff; margin-bottom: 10px;">⚖️ Reasonable Cost Status</h3>
                            <p><strong>Status:</strong> ${costData.reasonable ? '✅ Reasonable' : '⚠️ Needs Review'}</p>
                            <p><strong>Market Rate:</strong> ${reasonableCostAnalysis.marketRate}</p>
                            <p><strong>FEMA Guidelines:</strong> ${reasonableCostAnalysis.femaCompliant ? '✅ Compliant' : '❌ Non-compliant'}</p>
                        </div>
                    </div>

                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #155724; margin-bottom: 10px;">📋 FEMA Reasonable Cost Analysis</h3>
                        <div style="font-size: 0.9em; line-height: 1.6;">
                            ${reasonableCostAnalysis.analysis}
                        </div>
                    </div>

                    <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #856404; margin-bottom: 10px;">🔍 Compliance Recommendations</h3>
                        <ul style="margin: 0; padding-left: 20px;">
                            ${reasonableCostAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="openDocumentWorkbook('${fileId}')" style="background: #059669; color: white; border: none; padding: 12px 25px; border-radius: 25px; margin-right: 10px; cursor: pointer;">📋 Open FEMA Workbook</button>
                        <button onclick="generateCostReport('${fileId}')" style="background: #667eea; color: white; border: none; padding: 12px 25px; border-radius: 25px; margin-right: 10px; cursor: pointer;">📄 Generate Cost Report</button>
                        <button onclick="this.closest('.modal').remove()" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 25px; cursor: pointer;">Close</button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        function generateReasonableCostAnalysis(costData, documentType) {
            const baseRates = {
                'force-account-labor': { min: 25, max: 85, unit: 'per hour' },
                'force-account-equipment': { min: 150, max: 500, unit: 'per day' },
                'force-account-materials': { min: 0.8, max: 1.2, unit: 'markup factor' },
                'contract': { min: 0.9, max: 1.3, unit: 'market rate factor' }
            };

            const rate = baseRates[documentType] || { min: 50, max: 200, unit: 'estimated' };
            const marketRate = `$${rate.min}-${rate.max} ${rate.unit}`;
            const femaCompliant = Math.random() > 0.3;

            const analysis = `
                <strong>Cost Reasonableness Analysis per 2 CFR 200.404:</strong><br><br>
                • <strong>Market Analysis:</strong> Compared against ${documentType === 'force-account-labor' ? 'prevailing wage rates' : 'regional market rates'}<br>
                • <strong>Historical Data:</strong> Reviewed against similar disaster projects in the region<br>
                • <strong>Procurement Compliance:</strong> ${Math.random() > 0.5 ? 'Competitive bidding process verified' : 'Sole source justification required'}<br>
                • <strong>FEMA Guidelines:</strong> Cross-referenced with PAPPG cost principles<br><br>
                <strong>Determination:</strong> ${costData.reasonable ? 'Costs appear reasonable and necessary for the work performed.' : 'Additional documentation required to support cost reasonableness.'}
            `;

            const recommendations = [
                'Verify all costs are directly related to disaster damage',
                'Ensure proper documentation of work performed',
                'Cross-reference with FEMA cost guidelines',
                documentType.includes('force-account') ? 'Validate force account rates against local standards' : 'Review contractor qualifications and bidding process',
                'Confirm insurance proceeds have been properly deducted'
            ];

            return {
                marketRate,
                femaCompliant,
                analysis,
                recommendations
            };
        }

        function openDocumentWorkbook(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);

            // Extract data from document analysis
            const documentData = {
                applicantName: 'Document Upload Applicant',
                projectTitle: `Project from ${file.name}`,
                category: file.analysis?.category || 'Category C - Roads & Bridges',
                projectLocation: file.analysis?.location || 'Location extracted from documents',
                damageDescription: file.analysis?.damageDescription || 'Damage assessment from uploaded documents',
                extractedCosts: file.analysis?.costs || {},
                documentSource: file.name,
                analysisDate: new Date().toLocaleDateString()
            };

            // Open workbook in new window
            const workbookWindow = window.open('', 'FEMAWorkbook', 'width=1400,height=900,scrollbars=yes,resizable=yes');
            if (!workbookWindow) throw new Error('Popup blocked');

            const doc = workbookWindow.document;
            const html = doc.createElement('html');
            const head = doc.createElement('head');
            const body = doc.createElement('body');

            // Create head elements
            const title = doc.createElement('title');
            title.textContent = 'FEMA Project Workbook - Document Analysis';
            head.appendChild(title);

            const script = doc.createElement('script');
            script.src = 'fema_workbook_integration.js';
            head.appendChild(script);

            // Create body elements
            body.style.cssText = 'font-family: Arial, sans-serif; padding: 20px;';

            const headerDiv = doc.createElement('div');
            headerDiv.style.cssText = 'background: #253464; color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;';

            const h1 = doc.createElement('h1');
            h1.textContent = '📄 FEMA Project Workbook Template';
            headerDiv.appendChild(h1);

            const p = doc.createElement('p');
            p.textContent = `Auto-populated from Document Analysis: ${file.name}`;
            headerDiv.appendChild(p);

            const container = doc.createElement('div');
            container.id = 'document-workbook-container';

            body.appendChild(headerDiv);
            body.appendChild(container);

            // Create initialization script
            const initScript = doc.createElement('script');
            initScript.textContent = `
                document.addEventListener('DOMContentLoaded', function() {
                    const container = document.getElementById('document-workbook-container');
                    if (typeof femaWorkbook !== 'undefined') {
                        container.innerHTML = femaWorkbook.generateWorkbookHTML();
                        femaWorkbook.populateWorkbook(${JSON.stringify(documentData)});
                        if (typeof switchWorkbookTab === 'function') {
                            switchWorkbookTab('project-info');
                        }
                    }

                    // Add document analysis summary
                    const analysisSection = document.createElement('div');
                    analysisSection.style.cssText = 'background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 8px; padding: 15px; margin: 20px 0;';

                    const summaryTitle = document.createElement('h4');
                    summaryTitle.style.color = '#1d4ed8';
                    summaryTitle.textContent = '📄 Document Analysis Summary';

                    const sourceP = document.createElement('p');
                    sourceP.innerHTML = '<strong>Source Document:</strong> ${file.name}';

                    const dateP = document.createElement('p');
                    dateP.innerHTML = '<strong>Analysis Date:</strong> ${documentData.analysisDate}';

                    const dataP = document.createElement('p');
                    dataP.innerHTML = '<strong>Extracted Data:</strong> Cost estimates, damage descriptions, project specifications';

                    analysisSection.appendChild(summaryTitle);
                    analysisSection.appendChild(sourceP);
                    analysisSection.appendChild(dateP);
                    analysisSection.appendChild(dataP);
                    container.appendChild(analysisSection);
                });
            `;
            body.appendChild(initScript);

            html.appendChild(head);
            html.appendChild(body);
            doc.replaceChildren(html);
        }

        function generateCostReport(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);
            alert(`📊 Generating Comprehensive Cost Report for ${file.name}\n\nReport will include:\n• Detailed cost breakdown\n• Reasonable cost analysis\n• FEMA compliance assessment\n• Supporting documentation\n• Recommended actions\n\nReport will be available for download in 30 seconds.`);
        }

        function downloadResults(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);
            alert(`📥 Downloading analysis package for ${file.name}\n\nIncludes:\n• OCR extracted text\n• Classification results\n• Cost analysis report\n• Compliance checklist\n• FEMA form templates`);
        }
    </script>

    <!-- MAX ASSIST + AUTOPILOT INTEGRATION (SIDEWALK QUARANTINED) -->
    <link rel="stylesheet" href="assist/assist.css">
    <!-- <link rel="stylesheet" href="ui/sidewalk.css"> QUARANTINED: moved to legacy/sidewalk/ -->
    <!-- <script src="ui/sidewalk.js" defer></script> QUARANTINED: moved to legacy/sidewalk/ -->
    <script src="assist/assist.js" defer></script>
    <script src="assist/autofill.js" defer></script>
</main>

<div id="cmx-footer"></div>

<script defer src="js/sidewalk-hints.js"></script>
</body>
</html>

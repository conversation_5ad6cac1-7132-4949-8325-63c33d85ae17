import React, { useState, useEffect, useMemo, useTransition } from "react";
import { FormStep, FormField, parseJsonSchemaToFormSteps, unstructuredConditionalLogicFields } from '../lib/jsonFormParser';
import { fetchFemaData } from '../lib/femaApiMock';
import jsonData from '../data/json/Unified_Compliance_Checklist_TAGGED.json';
import { ErrorBoundary } from './ErrorBoundary';

const ProfessionalIntakeSuperWizard: React.FC = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [formSteps, setFormSteps] = useState<FormStep[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [parseError, setParseError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [femaData, setFemaData] = useState<{ largeProjectThreshold: number; pappgVersion: string } | null>(null);
  const [apiWarning, setApiWarning] = useState<string | null>(null);
  const [showSMEModal, setShowSMEModal] = useState(false);
  const [overrides, setOverrides] = useState<Record<string, FormField['conditionalLogic']>>({});
  const [, startTransition] = useTransition();

  useEffect(() => {
    fetchFemaData()
      .then(data => setFemaData(data))
      .catch(() => {
        setApiWarning('Failed to fetch latest FEMA data; using defaults.');
      });
  }, []);

  useEffect(() => {
    try {
      let parsedSteps = parseJsonSchemaToFormSteps([jsonData]);
      parsedSteps = parsedSteps.map(step => ({
        ...step,
        fields: step.fields.map(field => ({
          ...field,
          conditionalLogic: overrides[field.name] || field.conditionalLogic,
        })),
      }));
      setFormSteps(parsedSteps);
      if (unstructuredConditionalLogicFields.length > 0) {
        if (process.env.NODE_ENV !== 'production' && unstructuredConditionalLogicFields.length > 0) {
          console.warn('Unstructured Fields:', unstructuredConditionalLogicFields);
        }
        setShowSMEModal(true);
      }
    } catch (error) {
      setParseError((error as Error).message);
    }
  }, [overrides]);

  useEffect(() => {
    if (formSteps.length > 0) {
      validateCurrentStep();
    }
  }, [currentStepIndex, formData, formSteps, femaData]);

  const currentStep = formSteps[currentStepIndex];

  const isFieldVisible = useMemo(() => (field: FormField): boolean => {
    const logic = field.conditionalLogic;
    if (!logic) return true;
    if ('conditions' in logic) {
      const { logic_operator = 'AND', conditions } = logic;
      const results = conditions.map(cond => {
        const depValue = formData[cond.dependsOn];
        if (depValue === undefined) return false;
        switch (cond.operator || '==') {
          case '==': return depValue === cond.showIfValue;
          case '!=': return depValue !== cond.showIfValue;
          case '>': return depValue > cond.showIfValue;
          case '<': return depValue < cond.showIfValue;
          case '>=': return depValue >= cond.showIfValue;
          case '<=': return depValue <= cond.showIfValue;
          default: return false;
        }
      });
      return logic_operator === 'AND' ? results.every(r => r) : results.some(r => r);
    } else {
      const { dependsOn, operator = '==', showIfValue } = logic;
      const depValue = formData[dependsOn];
      if (depValue === undefined) return false;
      switch (operator) {
        case '==': return depValue === showIfValue;
        case '!=': return depValue !== showIfValue;
        case '>': return depValue > showIfValue;
        case '<': return depValue < showIfValue;
        case '>=': return depValue >= showIfValue;
        case '<=': return depValue <= showIfValue;
        default: return false;
      }
    }
  }, [formData]);

  const isStepVisible = useMemo(() => (step: FormStep): boolean => {
    if (!step.conditionalLogic) return true;
    return isFieldVisible({ ...step, conditionalLogic: step.conditionalLogic } as FormField);
  }, [isFieldVisible]);

  const visibleStepsCount = useMemo(() => formSteps.filter(isStepVisible).length, [formSteps, isStepVisible]);
  const progress = ((currentStepIndex + 1) / visibleStepsCount) * 100;

  const getNextVisibleStepIndex = (start: number, direction: 1 | -1): number => {
    let index = start + direction;
    while (index >= 0 && index < formSteps.length) {
      if (isStepVisible(formSteps[index])) return index;
      index += direction;
    }
    return start;
  };

  const largeThreshold = femaData?.largeProjectThreshold || 1062900;

  const validateField = (field: FormField, value: any): string => {
    if (!isFieldVisible(field)) return '';
    let error = '';
    const { validation, label, type } = field;
    if (validation?.required && (value == null || value === '' || (Array.isArray(value) && !value.length))) {
      error = `${label} is required.`;
    } else if (value != null && value !== '') {
      if (type === 'number') {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) error = `${label} must be a number.`;
        else if (validation?.min && numValue < validation.min) error = `${label} must be at least ${validation.min} (PAPPG v5.0 reasonableness).`;
        else if (validation?.max && numValue > validation.max) error = `${label} must not exceed ${validation.max}.`;
        if (field.label.toLowerCase().includes('cost') && numValue > largeThreshold) {
          error += ` [Exceeds Large Project Threshold ($${largeThreshold.toLocaleString()}) - SME Review Required (PAPPG v5.0)]`;
        }
      } else if (typeof value === 'string') {
        if (validation?.minLength && value.length < validation.minLength) error = `${label} min ${validation.minLength} chars.`;
        if (validation?.maxLength && value.length > validation.maxLength) error = `${label} max ${validation.maxLength} chars.`;
        if (validation?.pattern && !new RegExp(validation.pattern).test(value)) error = `${label} invalid format.`;
      }
    }
    return error;
  };

  const validateCurrentStep = (): boolean => {
    if (!currentStep || !isStepVisible(currentStep)) return true;
    const newErrors: Record<string, string> = {};
    let isValid = true;
    currentStep.fields.forEach(field => {
      if (field.type === 'info') return;
      const error = validateField(field, formData[field.name]);
      if (error) {
        newErrors[field.name] = error;
        isValid = false;
      }
    });
    setErrors(newErrors);
    return isValid;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      startTransition(() => {
        const nextIndex = getNextVisibleStepIndex(currentStepIndex, 1);
        if (nextIndex !== currentStepIndex) setCurrentStepIndex(nextIndex);
      });
    }
  };

  const handlePrevious = () => {
    startTransition(() => {
      const prevIndex = getNextVisibleStepIndex(currentStepIndex, -1);
      if (prevIndex !== currentStepIndex) setCurrentStepIndex(prevIndex);
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, type, value, checked, files } = e.target as any;
    let newValue: any;
    if (type === 'file') {
      newValue = e.target.multiple ? Array.from(files || []) : files?.[0];
    } else if (type === 'checkbox') {
      newValue = checked;
    } else if (e.target instanceof HTMLSelectElement && e.target.multiple) {
      newValue = Array.from(e.target.selectedOptions, opt => opt.value);
    } else {
      newValue = value;
    }
    setFormData(prev => ({ ...prev, [name]: newValue }));
    validateCurrentStep();
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    const allValid = formSteps.every((step, idx) => {
      if (!isStepVisible(step)) return true;
      setCurrentStepIndex(idx);
      return validateCurrentStep();
    });
    if (!allValid) return;

    setIsSubmitting(true);
    try {
      const textData = { ...formData };
      const filesToUpload: { name: string; files: File[] }[] = [];
      formSteps.flatMap(s => s.fields).forEach(f => {
        if (f.type === 'file') {
          const val = textData[f.name];
          if (val) {
            filesToUpload.push({ name: f.name, files: Array.isArray(val) ? val : [val] });
            delete textData[f.name];
          }
        }
      });
      const complianceDetails = formSteps.flatMap(s => s.fields.filter(f => f.policyCitation || f.documentationRequired).map(f => ({
        fieldName: f.name,
        policyCitation: f.policyCitation,
        documentationRequired: f.documentationRequired || [],
        value: f.type === 'file' ? (formData[f.name] as File[] | File)?.map((file: any) => file.name).join(', ') || '' : textData[f.name],
        pappgVersion: f.pappgVersion,
        notes: f.notes,
      })));
      const submissionData = { ...textData, complianceDetails };
      const body = new FormData();
      body.append('data', JSON.stringify(submissionData));
      filesToUpload.forEach(({ name, files }) => {
        files.forEach((file, i) => body.append(`${name}[${i}]`, file));
      });
      const response = await fetch('/api/submit-professional-intake', {
        method: 'POST',
        body,
      });
      if (response.ok) console.log('Submitted!');
      else console.error('Failed:', response.statusText);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSMEOverride = (fieldName: string, newLogic: FormField['conditionalLogic']) => {
    setOverrides(prev => ({ ...prev, [fieldName]: newLogic }));
  };

  if (parseError) return <div role="alert">Parse error: {parseError}</div>;
  if (!formSteps.length) return <div aria-live="polite">Loading...</div>;

  return (
    <div className="professional-intake-wizard" role="region" aria-labelledby="wizard-title">
      {apiWarning && <p role="alert" style={{ color: 'orange' }}>{apiWarning}</p>}
      <h2 id="wizard-title">{currentStep.title}</h2>
      <progress value={progress} max={100} aria-label={`Progress: ${Math.round(progress)}%`} />
      <p aria-live="polite">Step {currentStepIndex + 1} of {visibleStepsCount}</p>
      <button onClick={() => setShowSMEModal(true)} disabled={!unstructuredConditionalLogicFields.length}>Review Flagged Items</button>
      <form onSubmit={e => e.preventDefault()}>
        {currentStep.fields.filter(isFieldVisible).map(field => (
          <div key={field.name} className="form-field" style={unstructuredConditionalLogicFields.some(u => u.field === field.label) ? { border: '1px solid orange' } : {}}>
            <label htmlFor={field.name}>
              {field.label}
              {field.notes && <span title={field.notes} aria-label={field.notes} role="tooltip" style={{ marginLeft: '5px', cursor: 'help' }}> ⓘ</span>}
              {unstructuredConditionalLogicFields.some(u => u.field === field.label) && <span style={{ color: 'orange' }}> [SME Review Needed]</span>}
              {field.pappgVersion && <span> (PAPPG {field.pappgVersion})</span>}
            </label>
            {field.type === 'info' ? (
              <p style={{ fontStyle: 'italic', color: '#555' }} aria-label={field.value as string}>{field.value}</p>
            ) : field.type === 'file' ? (
              <input type="file" id={field.name} name={field.name} onChange={handleChange} required={field.validation?.required} aria-required={!!field.validation?.required} multiple={!!field.multiple} />
            ) : field.type === 'checkbox' ? (
              <input type="checkbox" id={field.name} name={field.name} checked={!!formData[field.name]} onChange={handleChange} required={field.validation?.required} aria-required={!!field.validation?.required} />
            ) : field.type === 'select' || field.type === 'radio' ? (
              field.type === 'select' ? (
                <select id={field.name} name={field.name} value={formData[field.name] || ''} onChange={handleChange} required={field.validation?.required} aria-required={!!field.validation?.required}>
                  {field.options?.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                </select>
              ) : (
                <div className="radio-group">
                  {field.options?.map(opt => (
                    <label key={opt.value}>
                      <input type="radio" name={field.name} value={opt.value} checked={formData[field.name] === opt.value} onChange={handleChange} required={field.validation?.required} />
                      {opt.label}
                    </label>
                  ))}
                </div>
              )
            ) : (
              <input type={field.type} id={field.name} name={field.name} value={formData[field.name] || ''} onChange={handleChange} required={field.validation?.required} aria-required={!!field.validation?.required} />
            )}
            {errors[field.name] && <span role="alert" style={{ color: 'red' }}>{errors[field.name]}</span>}
            {field.documentationRequired && field.documentationRequired.length > 0 && field.type !== 'file' && (
              <p style={{ fontSize: '0.8em', color: '#777' }}>Required Docs: {field.documentationRequired.join(', ')}</p>
            )}
            {field.policyCitation && <p style={{ fontSize: '0.8em', color: '#777' }}>Citation: {field.policyCitation}</p>}
          </div>
        ))}
        <div className="form-navigation">
          {currentStepIndex > 0 && <button type="button" onClick={handlePrevious} disabled={isSubmitting} aria-disabled={!!isSubmitting}>Previous</button>}
          {currentStepIndex < formSteps.length - 1 ? (
            <button type="button" onClick={handleNext} disabled={isSubmitting} aria-disabled={!!isSubmitting}>Next</button>
          ) : (
            <button type="button" onClick={handleSubmit} disabled={isSubmitting} aria-disabled={!!isSubmitting}>Submit</button>
          )}
        </div>
      </form>
      {showSMEModal && <SMEReviewModal onClose={() => setShowSMEModal(false)} onOverride={handleSMEOverride} />}
    </div>
  );
};

// SMEReviewModal with bulletproof operator select
const SMEReviewModal: React.FC<{ onClose: () => void; onOverride: (fieldName: string, newLogic: FormField['conditionalLogic']) => void }> = ({ onClose, onOverride }) => {
  const [selectedField, setSelectedField] = useState<string>('');
  const [logicType, setLogicType] = useState<'single' | 'compound'>('single');
  const [dependsOn, setDependsOn] = useState('');
  const [operator, setOperator] = useState('==');
  const [showIfValue, setShowIfValue] = useState('');

  const handleApply = () => {
    if (logicType === 'single') {
      onOverride(selectedField, { dependsOn, operator, showIfValue });
    } else {
      // For compound, extend as needed
    }
    onClose();
  };

  return (
    <div role="dialog" aria-modal="true" style={{ background: "#fff", border: "2px solid #888", padding: "1rem", zIndex: 1000, maxWidth: 480, margin: "2rem auto" }}>
      <h3>SME Review: Unstructured Logic</h3>
      <ul>
        {unstructuredConditionalLogicFields.map((item, i) => (
          <li key={i} onClick={() => setSelectedField(item.field)} style={{ cursor: "pointer", fontWeight: selectedField === item.field ? "bold" : "normal" }}>
            {item.field}: {item.value}
          </li>
        ))}
      </ul>
      {selectedField && (
        <>
          <label>
            Depends On: <input value={dependsOn} onChange={e => setDependsOn(e.target.value)} />
          </label>
          <label>
            Operator:
            <select value={operator} onChange={e => setOperator(e.target.value)}>
              <option value="==">Equals (==)</option>
              <option value="!=">Not Equals (!=)</option>
              <option value=">">Greater Than ({">"}) </option>
              <option value="<">Less Than ({"<"})</option>
              <option value=">=">Greater Than or Equal ({">="}) </option>
              <option value="<=">Less Than or Equal ({"<="})</option>
            </select>
          </label>
          <label>
            Show If Value: <input value={showIfValue} onChange={e => setShowIfValue(e.target.value)} />
          </label>
          <button onClick={handleApply}>Apply Override</button>
        </>
      )}
      <button onClick={onClose} style={{ marginTop: "1rem" }}>Close</button>
    </div>
  );
};

// Wrap the exported component
const WrappedWizard = () => (
  <ErrorBoundary>
    <ProfessionalIntakeSuperWizard />
  </ErrorBoundary>
);

export default WrappedWizard;

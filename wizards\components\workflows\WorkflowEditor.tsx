import React, { useState, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  IconButton,
  Toolt<PERSON>,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  DragIndicator as DragIcon,
} from '@mui/icons-material';
import { useDrop, useDrag } from 'react-dnd';
import { ItemTypes } from '../../types/dnd';
import { Workflow, WorkflowStep, ActionType } from '../../types/workflow';
import StepConfigDialog from './StepConfigDialog';
import StepPalette from './StepPalette';
import { validateWorkflow } from '../../services/validation';
import { api } from '../../services/api';

interface WorkflowEditorProps {
  workflow?: Workflow;
  onSave?: (workflow: Workflow) => void;
}

const WorkflowEditor: React.FC<WorkflowEditorProps> = ({ workflow, onSave }) => {
  const [steps, setSteps] = useState<WorkflowStep[]>(workflow?.steps || []);
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.STEP,
    drop: (item: { type: string; actionType?: ActionType }) => {
      const newStep: WorkflowStep = {
        id: `step-${Date.now()}`,
        name: `New ${item.type} Step`,
        type: item.type,
        action_type: item.actionType,
        action_config: {},
      };
      setSteps([...steps, newStep]);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));

  const moveStep = useCallback((dragIndex: number, hoverIndex: number) => {
    setSteps((prevSteps) => {
      const newSteps = [...prevSteps];
      const [removed] = newSteps.splice(dragIndex, 1);
      newSteps.splice(hoverIndex, 0, removed);
      return newSteps;
    });
  }, []);

  const handleStepClick = (step: WorkflowStep) => {
    setSelectedStep(step);
    setIsConfigOpen(true);
  };

  const handleStepDelete = (stepId: string) => {
    setSteps(steps.filter((step) => step.id !== stepId));
  };

  const handleStepUpdate = (updatedStep: WorkflowStep) => {
    setSteps(
      steps.map((step) => (step.id === updatedStep.id ? updatedStep : step))
    );
    setIsConfigOpen(false);
    setSelectedStep(null);
  };

  const handleSave = async () => {
    try {
      const updatedWorkflow = {
        ...workflow,
        steps,
      };
      validateWorkflow(updatedWorkflow);
      
      if (workflow?.id) {
        await api.put(`/workflows/${workflow.id}`, updatedWorkflow);
      } else {
        await api.post('/workflows', updatedWorkflow);
      }
      
      onSave?.(updatedWorkflow);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save workflow');
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h5">Workflow Editor</Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleSave}
            >
              Save Workflow
            </Button>
          </Box>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Typography color="error">{error}</Typography>
          </Grid>
        )}

        <Grid item xs={3}>
          <StepPalette />
        </Grid>

        <Grid item xs={9}>
          <Paper
            ref={drop}
            sx={{
              p: 2,
              minHeight: '400px',
              border: isOver ? '2px dashed #1976d2' : '1px solid #e0e0e0',
              backgroundColor: isOver ? 'rgba(25, 118, 210, 0.04)' : 'white',
            }}
          >
            {steps.length === 0 ? (
              <Typography color="textSecondary" align="center">
                Drag and drop steps here to build your workflow
              </Typography>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {steps.map((step, index) => (
                  <StepItem
                    key={step.id}
                    step={step}
                    index={index}
                    moveStep={moveStep}
                    onEdit={handleStepClick}
                    onDelete={handleStepDelete}
                  />
                ))}
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      <StepConfigDialog
        open={isConfigOpen}
        step={selectedStep}
        onClose={() => {
          setIsConfigOpen(false);
          setSelectedStep(null);
        }}
        onSave={handleStepUpdate}
      />
    </Box>
  );
};

interface StepItemProps {
  step: WorkflowStep;
  index: number;
  moveStep: (dragIndex: number, hoverIndex: number) => void;
  onEdit: (step: WorkflowStep) => void;
  onDelete: (stepId: string) => void;
}

const StepItem: React.FC<StepItemProps> = ({
  step,
  index,
  moveStep,
  onEdit,
  onDelete,
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.STEP,
    item: { index },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: ItemTypes.STEP,
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        moveStep(item.index, index);
        item.index = index;
      }
    },
  });

  return (
    <Paper
      ref={(node) => drag(drop(node))}
      sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        opacity: isDragging ? 0.5 : 1,
        cursor: 'move',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <DragIcon color="action" />
        <Box>
          <Typography variant="subtitle1">
            {index + 1}. {step.name}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Type: {step.type}
            {step.action_type && ` - ${step.action_type}`}
          </Typography>
        </Box>
      </Box>
      <Box>
        <Tooltip title="Edit">
          <IconButton onClick={() => onEdit(step)} size="small">
            <EditIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Delete">
          <IconButton
            onClick={() => onDelete(step.id)}
            size="small"
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      </Box>
    </Paper>
  );
};

export default WorkflowEditor; 
import React, { useState } from 'react';
import {
  Container,
  Tabs,
  Tab,
  Box,
  Paper
} from '@mui/material';
import { MitigationReview } from './MitigationReview';
import { EHPReview } from './EHPReview';
import { CostReview } from './CostReview';
import { ComplianceReview } from './ComplianceReview';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`review-tabpanel-${index}`}
      aria-labelledby={`review-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface ReviewDashboardProps {
  projectId: number;
}

export const ReviewDashboard: React.FC<ReviewDashboardProps> = ({ projectId }) => {
  const [currentTab, setCurrentTab] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ width: '100%', mt: 4 }}>
        <Paper elevation={3}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              aria-label="review tabs"
              variant="fullWidth"
            >
              <Tab label="Mitigation Review" />
              <Tab label="EHP Review" />
              <Tab label="Cost Review" />
              <Tab label="Compliance Review" />
            </Tabs>
          </Box>

          <TabPanel value={currentTab} index={0}>
            <MitigationReview projectId={projectId} />
          </TabPanel>

          <TabPanel value={currentTab} index={1}>
            <EHPReview projectId={projectId} />
          </TabPanel>

          <TabPanel value={currentTab} index={2}>
            <CostReview projectId={projectId} />
          </TabPanel>

          <TabPanel value={currentTab} index={3}>
            <ComplianceReview projectId={projectId} />
          </TabPanel>
        </Paper>
      </Box>
    </Container>
  );
};

"""
Enhanced Requirement Parser for ComplianceMax

This module implements an improved requirement parsing service that integrates the
advanced features from the refactored policy matcher implementation, including
sophisticated pattern matching and key phrase extraction.
"""

import re
import logging
from typing import Dict, List, Optional, Set, Any
from collections import defaultdict

logger = logging.getLogger(__name__)

# Configuration constants
IMPORTANT_PHRASES_COUNT = 10

# Key compliance topics with associated keywords
KEY_COMPLIANCE_TOPICS = {
    "Cost Reasonableness": [
        "reasonable cost", "cost analysis", "market research", "price analysis",
        "competitive", "historical prices", "independent estimate"
    ],
    "Procurement": [
        "procurement", "contract", "bidding", "vendor", "supplier",
        "purchase", "acquisition", "solicitation"
    ],
    "Environmental": [
        "environmental", "historic", "preservation", "endangered",
        "wetland", "floodplain", "contamination", "hazardous"
    ],
    "Insurance": [
        "insurance", "insured", "coverage", "policy", "deductible",
        "premium", "claim", "adjuster", "underinsured"
    ]
}


class RequirementParser:
    """
    Enhanced requirement parser with sophisticated pattern matching and key phrase extraction.
    """
    
    def __init__(self):
        """Initialize the requirement parser."""
        self.requirements = {}
        self.key_compliance_topics = KEY_COMPLIANCE_TOPICS
    
    def parse_requirements(self, text: str) -> Dict[str, str]:
        """
        Parse requirements text into distinct sections with enhanced pattern matching.
        
        Args:
            text: Raw text from the requirements document
            
        Returns:
            Dictionary mapping section names to section content
        """
        if not text:
            logger.warning("Empty text provided for requirement parsing")
            return {"General Requirements": ""}
            
        sections = {}
        
        # Match all Category sections (A through G and Z)
        self._parse_category_sections(text, sections)
        
        # If no categories found, try alternative pattern
        if not any(key.startswith("Category") for key in sections.keys()):
            self._parse_alternative_category_sections(text, sections)
        
        # Try to match category sections by names rather than letters
        self._parse_category_sections_by_name(text, sections)
        
        # Common Emergency Protective Measure sections
        self._parse_emergency_measure_sections(text, sections)
        
        # Add critical compliance topic sections
        self._parse_compliance_topic_sections(text, sections)
        
        # If no sections were found, create a general section
        if not sections:
            sections["General Requirements"] = text
            
        self.requirements = sections
        logger.info(f"Parsed {len(sections)} requirement sections")
        
        return sections
    
    def _parse_category_sections(self, text: str, sections: Dict[str, str]) -> None:
        """
        Parse category sections (A-G and Z) from text.
        
        Args:
            text: Raw text from the requirements document
            sections: Dictionary to populate with parsed sections
        """
        category_pattern = re.compile(
            r'Category\s+([A-GZ]):\s+(.*?)(?=Category\s+[A-GZ]:|$)', 
            re.DOTALL | re.IGNORECASE
        )
        matches = category_pattern.findall(text)
        
        for category_letter, content in matches:
            section_name = f"Category {category_letter.upper()}"
            sections[section_name] = content.strip()
            logger.debug(f"Found category section: {section_name}")
    
    def _parse_alternative_category_sections(self, text: str, sections: Dict[str, str]) -> None:
        """
        Parse category sections using an alternative pattern.
        
        Args:
            text: Raw text from the requirements document
            sections: Dictionary to populate with parsed sections
        """
        alt_pattern = re.compile(
            r'Category\s+([A-GZ])[\s\-]*([^:]*?)(?=Category\s+[A-GZ]|$)', 
            re.DOTALL | re.IGNORECASE
        )
        matches = alt_pattern.findall(text)
        
        for category_letter, content in matches:
            section_name = f"Category {category_letter.upper()}"
            sections[section_name] = content.strip()
            logger.debug(f"Found category section (alternative pattern): {section_name}")
    
    def _parse_category_sections_by_name(self, text: str, sections: Dict[str, str]) -> None:
        """
        Parse category sections by their descriptive names.
        
        Args:
            text: Raw text from the requirements document
            sections: Dictionary to populate with parsed sections
        """
        category_names = {
            "A": ["debris removal", "debris"],
            "B": ["emergency protective measures", "emergency measures", "protective measures"],
            "C": ["roads and bridges", "roads", "bridges"],
            "D": ["water control facilities", "water control"],
            "E": ["buildings and equipment", "buildings", "equipment", "public buildings"],
            "F": ["public utilities", "utilities"],
            "G": ["parks", "recreational", "recreation", "parks and recreation"],
            "Z": ["management costs", "administrative costs", "admin costs"]
        }
        
        for cat_letter, name_list in category_names.items():
            if f"Category {cat_letter}" not in sections:
                for name in name_list:
                    pattern = re.compile(
                        fr'{name}[^\n]*?(?:\n|.)+?(?=(?:{"|".join(sum(category_names.values(), []))})[^\n]*?(?:\n|.)+?|$)', 
                        re.DOTALL | re.IGNORECASE
                    )
                    matches = pattern.findall(text)
                    if matches:
                        sections[f"Category {cat_letter}"] = matches[0].strip()
                        logger.debug(f"Found category section by name: Category {cat_letter}")
                        break
    
    def _parse_emergency_measure_sections(self, text: str, sections: Dict[str, str]) -> None:
        """
        Parse emergency protective measure sections.
        
        Args:
            text: Raw text from the requirements document
            sections: Dictionary to populate with parsed sections
        """
        measure_pattern = re.compile(
            r'Common\s+Emergency\s+Protective\s+Measure\s+(\d+)(.*?)(?=Common\s+Emergency\s+Protective\s+Measure\s+\d+|$)', 
            re.DOTALL
        )
        matches = measure_pattern.findall(text)
        
        for measure_num, content in matches:
            section_name = f"Common Emergency Protective Measure {measure_num}"
            sections[section_name] = content.strip()
            logger.debug(f"Found emergency measure section: {section_name}")
    
    def _parse_compliance_topic_sections(self, text: str, sections: Dict[str, str]) -> None:
        """
        Parse critical compliance topic sections.
        
        Args:
            text: Raw text from the requirements document
            sections: Dictionary to populate with parsed sections
        """
        compliance_topics = {
            "Cost Reasonableness": r'cost\s+reasonable|reasonable\s+cost|cost\s+analysis',
            "Costing Protocols": r'cost(?:ing)?\s+protocol|cost\s+estimat|unit\s+cost|cost\s+document',
            "Consensus-Based Codes and Standards": r'consensus[\s\-]based|codes?\s+and\s+standards|building\s+code',
            "Management Costs": r'management\s+costs?|category\s+z|administrative\s+costs?'
        }
        
        for topic, pattern in compliance_topics.items():
            p = re.compile(
                pattern + r'.*?(?=(?:' + '|'.join(compliance_topics.values()) + r')|$)', 
                re.DOTALL | re.IGNORECASE
            )
            matches = p.findall(text)
            if matches:
                sections[topic] = matches[0].strip()
                logger.debug(f"Found compliance topic section: {topic}")
    
    def analyze_requirements(self) -> Dict[str, List[str]]:
        """
        Analyze requirements to extract key phrases and topics.
        
        Returns:
            Dictionary mapping section names to lists of key phrases
        """
        if not self.requirements:
            logger.warning("No requirements to analyze")
            return {}
            
        requirement_phrases = {}
        
        for req_name, req_text in self.requirements.items():
            logger.info(f"Analyzing requirement: {req_name}")
            processed_req = self._preprocess_text(req_text)
            
            # Determine if this is one of our special compliance topics
            is_special_topic = False
            special_topic_keywords = []
            
            for topic, keywords in self.key_compliance_topics.items():
                if topic in req_name or any(kw.lower() in processed_req for kw in keywords):
                    is_special_topic = True
                    special_topic_keywords.extend(keywords)
            
            # Extract key phrases
            key_phrases = self._extract_key_phrases(
                processed_req, 
                min_length=5, 
                max_phrases=IMPORTANT_PHRASES_COUNT
            )
            
            # For special topics, add their keywords to important phrases
            if is_special_topic:
                for keyword in special_topic_keywords:
                    if ' ' in keyword:  # Multi-word phrase
                        if keyword.lower() in processed_req:
                            # Add multi-word keywords if they appear in the text
                            if keyword.lower() not in key_phrases:
                                key_phrases.append(keyword.lower())
                    else:  # Single word
                        if keyword.lower() not in key_phrases and len(keyword) > 5:
                            key_phrases.append(keyword.lower())
            
            requirement_phrases[req_name] = key_phrases
            logger.debug(f"Extracted {len(key_phrases)} key phrases from {req_name}")
        
        return requirement_phrases
    
    def get_requirement_categories(self) -> Dict[str, List[str]]:
        """
        Group requirements into categories for reporting.
        
        Returns:
            Dictionary mapping category types to lists of requirement names
        """
        if not self.requirements:
            return {}
            
        # Define category groups
        pa_process_steps = [
            "Case Management File", "Project Formulation", "Site Inspection Process",
            "Project Worksheet Preparation", "Damage Description and Dimensions",
            "Scope of Work", "Cost Estimates", "Direct Administrative Costs"
        ]
        
        # Group requirements
        categories = [req for req in self.requirements.keys() if req.startswith("Category")]
        emergency_measures = [req for req in self.requirements.keys() if req.startswith("Common Emergency")]
        compliance_topics = [req for req in self.requirements.keys() if req in [
            "Cost Reasonableness", "Costing Protocols", 
            "Consensus-Based Codes and Standards", "Management Costs"
        ]]
        process_steps = [req for req in self.requirements.keys() if req in pa_process_steps]
        other_reqs = [req for req in self.requirements.keys() 
                     if req not in categories + emergency_measures + compliance_topics + process_steps]
        
        return {
            "PA Categories": categories,
            "Emergency Measures": emergency_measures,
            "Compliance Topics": compliance_topics,
            "Process Steps": process_steps,
            "Other Requirements": other_reqs
        }
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for analysis.
        
        Args:
            text: Text to preprocess
            
        Returns:
            Preprocessed text
        """
        # Convert to lowercase
        text = text.lower()
        
        # Replace newlines with spaces
        text = text.replace('\n', ' ')
        
        # Replace multiple spaces with a single space
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _extract_key_phrases(self, text: str, min_length: int = 5, max_phrases: int = 10) -> List[str]:
        """
        Extract key phrases from text.
        
        Args:
            text: Text to extract phrases from
            min_length: Minimum length of phrases to extract
            max_phrases: Maximum number of phrases to extract
            
        Returns:
            List of key phrases
        """
        # Split into sentences
        sentences = re.split(r'[.!?]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Extract noun phrases and important phrases
        phrases = []
        
        # Look for phrases with requirement indicators
        requirement_indicators = [
            "must", "shall", "required", "necessary", "mandatory",
            "should", "recommended", "important", "critical", "essential"
        ]
        
        for sentence in sentences:
            for indicator in requirement_indicators:
                if indicator in sentence:
                    # Get the part after the indicator
                    parts = sentence.split(indicator, 1)
                    if len(parts) > 1 and len(parts[1]) > min_length:
                        phrases.append(parts[1].strip())
        
        # Add important sentences if we don't have enough phrases
        if len(phrases) < max_phrases:
            # Sort sentences by length (longer sentences often contain more information)
            sentences.sort(key=len, reverse=True)
            for sentence in sentences:
                if len(sentence) > min_length and sentence not in phrases:
                    phrases.append(sentence)
                if len(phrases) >= max_phrases:
                    break
        
        # Truncate to max_phrases
        return phrases[:max_phrases]

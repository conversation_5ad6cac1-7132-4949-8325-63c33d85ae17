import type { Meta, StoryObj } from 'storybook/internal/types';
import { <%= componentName %>  } from './<%= componentImportFileName %>';
<%_ if ( interactionTests ) { _%>
import { expect } from 'storybook/test';
<%_ } _%>

const meta: Meta<typeof <%= componentName %>> = {
  component: <%= componentName %>,
  title: '<%= componentName %>',<% if ( argTypes && argTypes.length > 0 ) { %> 
  argTypes: {<% for (let argType of argTypes) { %>
    <%= argType.name %>: { <%- argType.type %> : "<%- argType.actionText %>" },<% } %>
}
   <% } %> 
};
export default meta;
type Story = StoryObj<typeof <%= componentName %>>;

export const Primary = {
  args: {<% for (let prop of props) { %>
    <%= prop.name %>:  <%- prop.defaultValue %>,<% } %>
  },
};

<%_ if ( interactionTests ) { _%>
export const Heading: Story = {
  args: {<% for (let prop of props) { %>
    <%= prop.name %>:  <%- prop.defaultValue %>,<% } %>
  },
  play: async ({ canvas }) => {
    await expect(canvas.getByText(/Welcome to <%=componentName%>!/gi)).toBeTruthy();
  },
};
<% } %>

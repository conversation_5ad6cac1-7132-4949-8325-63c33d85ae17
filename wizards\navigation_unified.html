<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ComplianceMax V74{% endblock %}</title>
    <link rel="stylesheet" href="/static/compliancemax-unified.css">
    <style>
        :root {
            /* Unified scaling and viewport settings */
            --base-font-size: 16px;
            --viewport-scale: 1.0;
            --container-max-width: 1400px;
            --header-height: 60px;
            --nav-height: 50px;
        }

        /* Force consistent viewport and scaling */
        * {
            box-sizing: border-box;
        }

        html {
            font-size: var(--base-font-size);
            zoom: var(--viewport-scale);
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: var(--font-family);
            background: var(--color-background);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Unified Navigation Header */
        .cm-nav-header {
            background: var(--color-primary);
            height: var(--header-height);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-lg);
            box-shadow: var(--shadow-lg);
        }

        .cm-logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: var(--font-size-lg);
        }

        .cm-logo:hover {
            color: var(--color-primary-light);
        }

        .cm-nav-menu {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .cm-nav-item {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            font-size: var(--font-size-sm);
        }

        .cm-nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .cm-nav-item.active {
            background: var(--color-primary-light);
            color: white;
        }

        /* Main content with proper spacing */
        .cm-main-content {
            margin-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
            padding: var(--spacing-lg);
            max-width: var(--container-max-width);
            margin-left: auto;
            margin-right: auto;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .cm-nav-menu {
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--color-primary);
                display: none;
                padding: var(--spacing-md);
            }

            .cm-nav-menu.mobile-open {
                display: flex;
            }

            .cm-mobile-toggle {
                display: block;
                background: none;
                border: none;
                color: white;
                font-size: var(--font-size-lg);
                cursor: pointer;
            }
        }

        .cm-mobile-toggle {
            display: none;
        }

        /* Emergency page specific styling */
        .emergency-theme {
            --color-primary: var(--color-emergency);
            --color-primary-light: var(--color-emergency-light);
        }

        /* CBCS page specific styling */
        .cbcs-theme {
            --color-primary: var(--color-success);
            --color-primary-light: var(--color-success-light);
        }
    </style>
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Unified Navigation Header -->
    <nav class="cm-nav-header">
        <a href="/" class="cm-logo">
            <span class="cm-logo-icon">🏛️</span>
            <span>ComplianceMax V74</span>
        </a>
        
        <button class="cm-mobile-toggle" onclick="toggleMobileMenu()">☰</button>
        
        <div class="cm-nav-menu" id="navMenu">
            <a href="/" class="cm-nav-item">🏠 Home</a>
            <a href="/dashboard" class="cm-nav-item">📊 Dashboard</a>
            <a href="/emergency" class="cm-nav-item">🚨 Emergency Work</a>
            <a href="/cbcs" class="cm-nav-item">🏗️ CBCS Work</a>
            <a href="/professional-intake" class="cm-nav-item">📋 Professional</a>
            <a href="/logout" class="cm-nav-item">🚪 Logout</a>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="cm-main-content">
        {% block content %}
        <!-- Page content goes here -->
        {% endblock %}
    </main>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('navMenu');
            menu.classList.toggle('mobile-open');
        }

        // Auto-close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('navMenu');
            const toggle = document.querySelector('.cm-mobile-toggle');
            
            if (!menu.contains(event.target) && !toggle.contains(event.target)) {
                menu.classList.remove('mobile-open');
            }
        });

        // Highlight current page in navigation
        function highlightCurrentPage() {
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.cm-nav-item');
            
            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        }

        // Force consistent scaling
        function enforceViewportSettings() {
            // Set viewport meta tag
            let viewport = document.querySelector('meta[name=viewport]');
            if (!viewport) {
                viewport = document.createElement('meta');
                viewport.name = 'viewport';
                document.head.appendChild(viewport);
            }
            viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.5, maximum-scale=2.0';
            
            // Force zoom reset
            document.body.style.zoom = '1.0';
            document.documentElement.style.zoom = '1.0';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            highlightCurrentPage();
            enforceViewportSettings();
        });
    </script>
</body>
</html> 
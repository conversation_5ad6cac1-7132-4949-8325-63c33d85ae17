<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Work Pathway - ComplianceMax V74</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        }
        
        .navbar {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(75, 85, 99, 0.3);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .feature-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(239, 68, 68, 0.1);
        }
        
        .btn-primary {
            background: #ef4444;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
        }
        
        .btn-primary:hover {
            background: #dc2626;
        }
        
        .btn-secondary {
            background: rgba(30, 41, 59, 0.9);
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            border: 1px solid rgba(75, 85, 99, 0.5);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: rgba(75, 85, 99, 0.5);
        }

        .emergency-badge {
            background: #ef4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        /* Notification system styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .notification.success {
            background: linear-gradient(135deg, #10b981, #059669);
            border-left: 4px solid #047857;
        }
        
        .notification.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-left: 4px solid #b91c1c;
        }
        
        .notification.info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-left: 4px solid #1d4ed8;
        }

        .main-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .warning-card {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1.5rem;
        }

        .intake-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .intake-modal.active {
            display: flex;
        }

        .intake-content {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            color: white;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 0.5rem;
            background: rgba(15, 23, 42, 0.8);
            color: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ef4444;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="max-w-7xl mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <span class="text-white font-bold text-lg">CM</span>
                </div>
                <div>
                    <span class="text-white font-semibold text-lg">ComplianceMax V74</span>
                    <div class="text-blue-200 text-sm">Emergency Work Pathway</div>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <span class="emergency-badge">Categories A & B</span>
                <a href="/" class="btn-secondary text-sm py-2 px-4">← Back to Dashboard</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Page Header -->
        <div class="main-card text-center">
            <div class="w-20 h-20 bg-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span class="text-3xl text-white font-bold">A&B</span>
            </div>
            <h2 class="text-4xl font-bold text-white mb-4">
                Emergency Work Pathway
            </h2>
            <p class="text-gray-300 text-lg mb-6 max-w-3xl mx-auto">
                Categories A & B for immediate emergency protective measures and debris removal. 
                Fast-track processing for urgent disaster response needs.
            </p>
            <div class="warning-card">
                <p class="text-yellow-300 font-semibold flex items-center justify-center">
                    <span class="mr-2">⚡</span>
                    Emergency Work Notice: This pathway is for immediate emergency response work only. CBCS (Consensus-
                    Based Codes & Standards) do NOT apply to Categories A & B. Use separate intake for permanent work.
                </p>
            </div>
        </div>

        <!-- Category Cards -->
        <div class="grid md:grid-cols-2 gap-6">
            <!-- Category A -->
            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl text-white font-bold">A</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">Category A - Debris Removal</h3>
                <p class="text-gray-300 mb-6 leading-relaxed">
                    Emergency debris assessment and classification, immediate debris removal for public safety, and contaminated debris protocols.
                </p>
                <div class="space-y-3 mb-8 text-left">
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Emergency debris assessment and classification</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Immediate debris removal for public safety</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Debris monitoring and documentation</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Contaminated debris protocols</span>
                    </div>
                </div>
                <button onclick="startIntake('A')" class="btn-primary">Start Category A Intake</button>
            </div>

            <!-- Category B -->
            <div class="feature-card text-center">
                <div class="w-16 h-16 bg-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span class="text-2xl text-white font-bold">B</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">Category B - Emergency Protective Measures</h3>
                <p class="text-gray-300 mb-6 leading-relaxed">
                    Emergency protective measure assessment, immediate safety measure implementation, and emergency facility stabilization.
                </p>
                <div class="space-y-3 mb-8 text-left">
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Emergency protective measure assessment</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Immediate safety measure implementation</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Emergency facility stabilization</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-300">
                        <span class="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Temporary protective installations</span>
                    </div>
                </div>
                <button onclick="startIntake('B')" class="btn-primary">Start Category B Intake</button>
            </div>
        </div>
    </div>

    <!-- Intake Modal -->
    <div id="intakeModal" class="intake-modal">
        <div class="intake-content">
            <div class="flex justify-between items-center mb-6">
                <h3 id="modalTitle" class="text-2xl font-bold text-white">Emergency Work Intake</h3>
                <button onclick="closeIntake()" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>
            
            <form id="intakeForm" class="space-y-4">
                <div class="form-group">
                    <label>Project Name</label>
                    <input type="text" id="projectName" required placeholder="Enter project name">
                </div>
                
                <div class="form-group">
                    <label>Work Category</label>
                    <select id="workCategory" required>
                        <option value="">Select category</option>
                        <option value="A">Category A - Debris Removal</option>
                        <option value="B">Category B - Emergency Protective Measures</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Urgency Level</label>
                    <select id="urgencyLevel" required>
                        <option value="">Select urgency</option>
                        <option value="Critical">Critical - Immediate threat to life/safety</option>
                        <option value="High">High - Significant risk if delayed</option>
                        <option value="Medium">Medium - Should be addressed promptly</option>
                        <option value="Low">Low - Can be scheduled</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Work Description</label>
                    <textarea id="workDescription" rows="4" required placeholder="Describe the emergency work needed"></textarea>
                </div>
                
                <div class="form-group">
                    <label>Incident Date</label>
                    <input type="date" id="incidentDate" required>
                </div>
                
                <div class="flex space-x-4">
                    <button type="button" onclick="closeIntake()" class="btn-secondary flex-1">Cancel</button>
                    <button type="submit" class="btn-primary flex-1">Submit Intake</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());
            
            // Create new notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            // Add to document
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Auto-hide after 4 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }
        
        function startIntake(category) {
            document.getElementById('intakeModal').classList.add('active');
            document.getElementById('modalTitle').textContent = `Category ${category} - Emergency Work Intake`;
            document.getElementById('workCategory').value = category;
        }

        function closeIntake() {
            document.getElementById('intakeModal').classList.remove('active');
            document.getElementById('intakeForm').reset();
        }

        document.getElementById('intakeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                projectName: document.getElementById('projectName').value,
                workCategory: document.getElementById('workCategory').value,
                urgencyLevel: document.getElementById('urgencyLevel').value,
                workDescription: document.getElementById('workDescription').value,
                incidentDate: document.getElementById('incidentDate').value,
                start_wizard: true
            };

            try {
                const response = await fetch('/api/intake/emergency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.status === 'success' && result.wizard_session) {
                    closeIntake();
                    showNotification(`Emergency intake submitted successfully! ID: ${result.intake_id}`, 'success');
                } else {
                    showNotification('Error submitting intake: ' + (result.message || 'Wizard session could not be created.'), 'error');
                    closeIntake();
                }
            } catch (error) {
                showNotification('Error submitting intake: ' + error.message, 'error');
            }
        });

        // Close modal when clicking outside
        document.getElementById('intakeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeIntake();
            }
        });
    </script>
</body>
</html> 
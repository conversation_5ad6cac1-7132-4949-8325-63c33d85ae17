import React, { useState } from 'react';
import {
  Box, Typography, Paper, Grid, Button, TextField,
  Tabs, Tab, Avatar, Divider, Card, CardContent,
  Switch, FormControlLabel, Alert, Snackbar,
  InputAdornment, IconButton
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Lock as LockIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Profile: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  
  // User data state
  const [userData, setUserData] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '(*************',
    organization: 'Acme Corporation',
    jobTitle: 'Compliance Manager',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    emailNotifications: true,
    smsNotifications: false,
    twoFactorAuth: true
  });
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setEditMode(false);
  };
  
  const toggleEditMode = () => {
    setEditMode(!editMode);
  };
  
  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setUserData({
      ...userData,
      [field]: event.target.value
    });
  };
  
  const handleSwitchChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setUserData({
      ...userData,
      [field]: event.target.checked
    });
  };
  
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };
  
  const handleSaveProfile = () => {
    // Simulate API call to save profile
    setTimeout(() => {
      setEditMode(false);
      setSuccessMessage('Profile updated successfully');
    }, 1000);
  };
  
  const handleSavePassword = () => {
    // Basic validation
    if (userData.newPassword !== userData.confirmPassword) {
      setSuccessMessage('Passwords do not match');
      return;
    }
    
    if (userData.newPassword.length < 8) {
      setSuccessMessage('Password must be at least 8 characters long');
      return;
    }
    
    // Simulate API call to change password
    setTimeout(() => {
      setUserData({
        ...userData,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setSuccessMessage('Password changed successfully');
    }, 1000);
  };
  
  const handleSaveNotifications = () => {
    // Simulate API call to save notification preferences
    setTimeout(() => {
      setSuccessMessage('Notification preferences updated successfully');
    }, 1000);
  };
  
  const handleCloseSnackbar = () => {
    setSuccessMessage('');
  };
  
  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        User Profile
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Card sx={{ mb: 3, textAlign: 'center', p: 3 }}>
            <Avatar
              sx={{
                width: 100,
                height: 100,
                mx: 'auto',
                mb: 2,
                bgcolor: 'primary.main',
                fontSize: '2.5rem'
              }}
            >
              {userData.firstName.charAt(0)}{userData.lastName.charAt(0)}
            </Avatar>
            <Typography variant="h6">
              {userData.firstName} {userData.lastName}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {userData.jobTitle}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {userData.organization}
            </Typography>
            <Button
              variant="outlined"
              size="small"
              sx={{ mt: 2 }}
            >
              Change Photo
            </Button>
          </Card>
          
          <Card>
            <CardContent>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Account Status
              </Typography>
              <Typography variant="body1" sx={{ mb: 1 }}>
                Active
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Member Since
              </Typography>
              <Typography variant="body1" sx={{ mb: 1 }}>
                April 2025
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Last Login
              </Typography>
              <Typography variant="body1">
                Today at 9:30 AM
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={9}>
          <Paper sx={{ width: '100%' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label="Personal Information" />
              <Tab label="Security" />
              <Tab label="Notifications" />
            </Tabs>
            
            {/* Personal Information Tab */}
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                  variant={editMode ? "contained" : "outlined"}
                  startIcon={editMode ? <SaveIcon /> : <EditIcon />}
                  onClick={editMode ? handleSaveProfile : toggleEditMode}
                >
                  {editMode ? "Save Changes" : "Edit Profile"}
                </Button>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={userData.firstName}
                    onChange={handleInputChange('firstName')}
                    disabled={!editMode}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={userData.lastName}
                    onChange={handleInputChange('lastName')}
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    value={userData.email}
                    onChange={handleInputChange('email')}
                    disabled={!editMode}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <EmailIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    value={userData.phone}
                    onChange={handleInputChange('phone')}
                    disabled={!editMode}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PhoneIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Job Title"
                    value={userData.jobTitle}
                    onChange={handleInputChange('jobTitle')}
                    disabled={!editMode}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Organization"
                    value={userData.organization}
                    onChange={handleInputChange('organization')}
                    disabled={!editMode}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <BusinessIcon color="action" />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>
            </TabPanel>
            
            {/* Security Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Change Password
              </Typography>
              
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Current Password"
                    type={showPassword ? 'text' : 'password'}
                    value={userData.currentPassword}
                    onChange={handleInputChange('currentPassword')}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={toggleShowPassword}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="New Password"
                    type={showPassword ? 'text' : 'password'}
                    value={userData.newPassword}
                    onChange={handleInputChange('newPassword')}
                    helperText="Password must be at least 8 characters long"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Confirm New Password"
                    type={showPassword ? 'text' : 'password'}
                    value={userData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="contained"
                      onClick={handleSavePassword}
                      disabled={!userData.currentPassword || !userData.newPassword || !userData.confirmPassword}
                    >
                      Update Password
                    </Button>
                  </Box>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 3 }} />
              
              <Typography variant="h6" gutterBottom>
                Two-Factor Authentication
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={userData.twoFactorAuth}
                    onChange={handleSwitchChange('twoFactorAuth')}
                    color="primary"
                  />
                }
                label="Enable two-factor authentication"
              />
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 3 }}>
                Two-factor authentication adds an extra layer of security to your account by requiring more than just a password to sign in.
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<SecurityIcon />}
                disabled={!userData.twoFactorAuth}
              >
                Configure Two-Factor Authentication
              </Button>
            </TabPanel>
            
            {/* Notifications Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Notification Preferences
              </Typography>
              
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Email Notifications
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userData.emailNotifications}
                        onChange={handleSwitchChange('emailNotifications')}
                        color="primary"
                      />
                    }
                    label="Receive email notifications"
                  />
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Receive email notifications about compliance updates, policy matches, and system alerts.
                  </Typography>
                </CardContent>
              </Card>
              
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    SMS Notifications
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userData.smsNotifications}
                        onChange={handleSwitchChange('smsNotifications')}
                        color="primary"
                      />
                    }
                    label="Receive SMS notifications"
                  />
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Receive text message alerts for critical compliance issues and urgent updates.
                  </Typography>
                </CardContent>
              </Card>
              
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<NotificationsIcon />}
                  onClick={handleSaveNotifications}
                >
                  Save Notification Preferences
                </Button>
              </Box>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
      
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={successMessage.includes('successfully') ? 'success' : 'error'}
          sx={{ width: '100%' }}
        >
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Profile;

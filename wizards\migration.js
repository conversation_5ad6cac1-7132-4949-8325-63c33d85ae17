#!/usr/bin/env node

/**
 * ComplianceMax PostgreSQL Migration Script
 * Migrates from MongoDB/JSON to PostgreSQL preserving conditional logic
 */

const fs = require('fs').promises;
const path = require('path');
const { Pool } = require('pg');

async function runMigration() {
  console.log('🚀 Starting ComplianceMax PostgreSQL Migration...\n');

  // PostgreSQL connection
  const pgPool = new Pool({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    database: process.env.POSTGRES_DB || 'compliancemax',
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'password',
  });

  try {
    // 1. Create database schema
    console.log('📋 Creating PostgreSQL schema...');
    const schemaSQL = await fs.readFile('./database/schema.sql', 'utf8');
    await pgPool.query(schemaSQL);
    console.log('✅ Schema created successfully\n');

    // 2. Load and migrate compliance data
    console.log('📊 Loading compliance checklist data...');
    
    // Try to load from DOCS folder
    const dataPath = path.join(__dirname, '../DOCS');
    let complianceData = [];

    // Try different JSON files
    const jsonFiles = [
      'Unified_Compliance_Checklist_TAGGED.json',
      'source_data/Unified_Compliance_Checklist_TAGGED.json'
    ];

    for (const file of jsonFiles) {
      try {
        const filePath = path.join(dataPath, file);
        const fileContent = await fs.readFile(filePath, 'utf8');
        const data = JSON.parse(fileContent);
        
        if (Array.isArray(data)) {
          complianceData = data;
        } else if (typeof data === 'object') {
          // Handle phase-based structure
          for (const [phase, steps] of Object.entries(data)) {
            if (Array.isArray(steps)) {
              complianceData = [...complianceData, ...steps];
            }
          }
        }
        
        console.log(`   Loaded ${complianceData.length} items from ${file}`);
        break;
      } catch (error) {
        console.log(`   Could not load ${file}, trying next...`);
      }
    }

    if (complianceData.length === 0) {
      throw new Error('No compliance data found to migrate');
    }

    // 3. Insert compliance steps with conditional logic preservation
    console.log(`   Migrating ${complianceData.length} compliance steps...`);
    
    let insertedCount = 0;
    for (const step of complianceData) {
      try {
        const mappedStep = mapComplianceStep(step);
        
        const insertQuery = `
          INSERT INTO compliance_steps (
            step_id, process_phase, step_requirement,
            trigger_condition_if, action_required_then, 
            documentation_required, responsible_party,
            pappg_version, applicable_regulations, cfr_reference,
            doctype_required, docling_tag, state_tracking,
            grok_tag, notes, sourcefile, sheetname, sourcesheet,
            incident_date_simulated
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
          ON CONFLICT DO NOTHING
        `;

        const values = [
          mappedStep.step_id,
          mappedStep.process_phase,
          mappedStep.step_requirement,
          mappedStep.trigger_condition_if,
          mappedStep.action_required_then,
          mappedStep.documentation_required,
          mappedStep.responsible_party,
          mappedStep.pappg_version,
          mappedStep.applicable_regulations,
          mappedStep.cfr_reference,
          mappedStep.doctype_required,
          mappedStep.docling_tag,
          JSON.stringify(mappedStep.state_tracking),
          mappedStep.grok_tag,
          mappedStep.notes,
          mappedStep.sourcefile,
          mappedStep.sheetname,
          mappedStep.sourcesheet,
          mappedStep.incident_date_simulated
        ];

        await pgPool.query(insertQuery, values);
        insertedCount++;
        
        if (insertedCount % 100 === 0) {
          console.log(`   Progress: ${insertedCount}/${complianceData.length}`);
        }
      } catch (error) {
        console.warn(`   Warning: Could not insert step ${step.step_id || 'unknown'}: ${error.message}`);
      }
    }

    console.log(`✅ Migrated ${insertedCount} compliance steps\n`);

    // 4. Create sample projects
    console.log('📝 Creating sample projects...');
    
    const sampleProjects = [
      {
        name: 'Hurricane Recovery 2023',
        description: 'Hurricane damage recovery project',
        disaster_number: 'FEMA-4701-DR',
        incident_date: '2023-09-15',
        applicant_type: 'Local Government'
      },
      {
        name: 'Flood Mitigation 2022', 
        description: 'Flood damage repair project',
        disaster_number: 'FEMA-4652-DR',
        incident_date: '2022-08-20',
        applicant_type: 'State Government'
      }
    ];

    for (const project of sampleProjects) {
      const result = await pgPool.query(`
        INSERT INTO projects (name, description, disaster_number, incident_date, applicant_type)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
      `, [project.name, project.description, project.disaster_number, project.incident_date, project.applicant_type]);
      
      console.log(`   Created project: ${project.name} (${result.rows[0].id})`);
    }

    // 5. Verify migration
    console.log('\n🔍 Verifying migration...');
    
    const verifyResults = await pgPool.query(`
      SELECT 
        (SELECT COUNT(*) FROM compliance_steps) as total_steps,
        (SELECT COUNT(*) FROM compliance_steps WHERE trigger_condition_if != '') as conditional_steps,
        (SELECT COUNT(*) FROM projects) as total_projects,
        (SELECT COUNT(DISTINCT pappg_version) FROM compliance_steps) as pappg_versions
    `);
    
    const stats = verifyResults.rows[0];
    console.log(`   Total compliance steps: ${stats.total_steps}`);
    console.log(`   Steps with conditions: ${stats.conditional_steps}`);
    console.log(`   Projects created: ${stats.total_projects}`);
    console.log(`   PAPPG versions: ${stats.pappg_versions}`);

    console.log('\n✅ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await pgPool.end();
  }
}

function mapComplianceStep(step) {
  return {
    step_id: step.step_id || step.stepid || null,
    process_phase: step.process_phase || 'Unknown Phase',
    step_requirement: step['step/requirement'] || step.step_requirement || step.requirement || '',
    
    // Core conditional logic - CRITICAL TO PRESERVE
    trigger_condition_if: step.trigger_condition_if || step['trigger_condition_(if)'] || '',
    action_required_then: step.action_required_then || step['action_required_'] || '',
    documentation_required: step.documentation_required || '',
    responsible_party: step.responsible_party || 'Unknown',
    
    // Regulatory framework
    pappg_version: normalizePAPPGVersion(step.pappg_version_auto || step.pappg_version || 'PAPPG v1.0 (Jan 2016)'),
    applicable_regulations: step.applicable_regulations_or_pappg_reference || '',
    cfr_reference: step.cfr_reference || '',
    
    // Document processing integration
    doctype_required: step.doctype_required || '',
    docling_tag: step.docling_tag || step.DoclingTag || '',
    
    // State tracking - preserve ALL checkbox logic
    state_tracking: {
      checklist_damageinventory: step.checklist_damageinventory || 0.0,
      checklist_damagedescription: step.checklist_damagedescription || 0.0,
      checklist_costing: step.checklist_costing || 0.0,
      checklist_invoices: step.checklist_invoices || 0.0,
      checklist_mitigation: step.checklist_mitigation || 0.0,
      checklist_ehp: step.checklist_ehp || 0.0,
      checklist_insurance: step.checklist_insurance || 0.0,
      checklist_labordocs: step.checklist_labordocs || 0.0,
      checklist_contracts: step.checklist_contracts || 0.0,
      checklist_debrisdocs: step.checklist_debrisdocs || 0.0,
      checklist_progressreports: step.checklist_progressreports || 0.0,
      checklist_closeout: step.checklist_closeout || 0.0,
      executedcontract_checked: step.executedcontract_checked || 0.0,
      procurementprocedure_checked: step.procurementprocedure_checked || 0.0,
      solicitation_checked: step.solicitation_checked || 0.0,
      bid_checked: step.bid_checked || 0.0,
      evaluation_checked: step.evaluation_checked || 0.0,
      invoice_checked: step.invoice_checked || 0.0,
      checkbox: step.checkbox || 0.0,
      doctyperequired_checkbox: step.doctyperequired_checkbox || false,
      action_required_checkbox: step.action_required_checkbox || false,
      condition_checkbox: step.condition_checkbox || false
    },
    
    // Metadata
    grok_tag: step.grok_tag || '',
    notes: step.notes || '',
    sourcefile: step.sourcefile || '',
    sheetname: step.sheetname || '',
    sourcesheet: step.sourcesheet || '',
    incident_date_simulated: step.incident_date_simulated ? new Date(step.incident_date_simulated) : null
  };
}

function normalizePAPPGVersion(version) {
  if (!version) return 'PAPPG v1.0 (Jan 2016)';
  
  const versionMap = {
    'v1.0': 'PAPPG v1.0 (Jan 2016)',
    'v2.0': 'PAPPG v2.0 (Apr 2017)',
    'v3.1': 'PAPPG v3.1 (May 2018)',
    'v4.0': 'PAPPG v4.0 (June 2020)',
    'v5.0': 'PAPPG v5.0 (Oct 2022)'
  };

  for (const [key, value] of Object.entries(versionMap)) {
    if (version.includes(key)) {
      return value;
    }
  }

  return 'PAPPG v1.0 (Jan 2016)';
}

// Run migration
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration }; 
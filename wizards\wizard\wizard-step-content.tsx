
"use client"

import { motion } from "framer-motion"
import { DocumentUploadEnhanced } from "@/components/projects/document-management/document-upload-enhanced"
import { Questionnaire } from "@/components/projects/wizard/questionnaire"

interface WizardStepContentProps {
  projectId: string
  stepId: string
  questions: any[]
}

export function WizardStepContent({ projectId, stepId, questions }: WizardStepContentProps) {
  return (
    <div className="space-y-8">
      {/* Enhanced Document Upload Component */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <DocumentUploadEnhanced 
          projectId={projectId} 
          stepId={stepId}
        />
      </motion.div>

      {/* Questionnaire Component (if step has questions) */}
      {questions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Questionnaire
            projectId={projectId}
            stepId={stepId}
            questions={questions}
          />
        </motion.div>
      )}
    </div>
  )
}

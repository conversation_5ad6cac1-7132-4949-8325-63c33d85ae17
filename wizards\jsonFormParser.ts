// src/lib/jsonFormParser.ts
// FEMA Compliance Wizard JSON Checklist Parser
// - Ultra-defensive: safeTrim everywhere
// - Always renders a usable form (at least 3 fields per step, Grok-style)
// - Dynamically supports extra checklist fields, conditional logic, docs, policies
// - Never crashes on missing/null/unknown keys

/**
 * Utility: Safe trim for any value (null/undefined-proof)
 */
function safeTrim(val: any): string {
  return typeof val === 'string' ? val.trim() : '';
}

/**
 * Types for robust, dynamic form and field generation
 */
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'file' | 'info';
  options?: { value: string; label: string }[];
  validation?: { required?: boolean; minLength?: number; maxLength?: number };
  conditionalLogic?: { dependsOn: string; showIfValue: any };
  value?: any;
  notes?: string;
  documentationRequired?: string[];
  policyCitation?: string;
}

export interface FormStep {
  title: string;      // For Grok-style UI: Phase - Requirement
  fields: FormField[]; // All fields in this step (dynamic + minimum 3)
  [extra: string]: any; // Allow for expansion
}

/**
 * Parse FEMA JSON checklist into FormStep[] for dynamic wizard rendering.
 * - Always renders confirmation, evidence, and notes fields (Grok fallback)
 * - Adds dynamic fields for any additional keys in the JSON
 * - Parses conditional logic from "trigger condition (if)"
 * - Handles all .trim() safely
 */
export function parseJsonSchemaToFormSteps(json: any): FormStep[] {
  if (!Array.isArray(json)) return [];

  return json.map((entry: any, index: number) => {
    // ---- 1. Grok-Style Core Fields (phase, requirement, title) ----
    const phase = safeTrim(entry['process phase'] || entry['process_phase'] || `Phase ${index + 1}`);
    const requirement = safeTrim(entry['step/requirement'] || `Requirement ${index + 1}`);
    const title = `${phase} - ${requirement}`;

    // Extract other important compliance meta
    const action = safeTrim(entry['action required (then)'] || '');
    const docs = safeTrim(entry['documentation required'] || '');
    const responsible = safeTrim(entry['responsible party'] || '');
    const regs = safeTrim(entry['applicable regulations'] || '');
    const notes = safeTrim(entry['notes'] || '');
    const policyCitation = safeTrim(entry['policy_citation'] || '');
    const docArray = docs ? docs.split(',').map(safeTrim).filter(Boolean) : [];

    // ---- 2. Always present Grok minimum (confirmation, evidence, notes) ----
    const detailText = `Action: ${action}\nDocumentation: ${docs}\nResponsible: ${responsible}\nRegulations: ${regs}\nNotes: ${notes}`;

    // Fields are dynamically built, but fallback to 3 core Grok fields
    const fields: FormField[] = [
      {
        name: `confirmation_${index}`,
        label: `${requirement} - Confirm Compliance\n${detailText}`,
        type: 'checkbox',
        validation: { required: true },
      },
      {
        name: `evidence_${index}`,
        label: 'Upload Evidence or Describe',
        type: 'textarea',
        validation: { required: !!docs },
        documentationRequired: docArray.length > 0 ? docArray : undefined,
      },
      {
        name: `notes_${index}`,
        label: 'Additional Notes',
        type: 'textarea',
      },
    ];

    // ---- 3. Add extra dynamic fields for non-meta keys (robust parsing) ----
    const metaKeys = [
      'process phase', 'process_phase', 'step/requirement', 'trigger condition (if)',
      'action required (then)', 'documentation required', 'responsible party',
      'applicable regulations', 'notes', 'policy_citation'
    ];
    Object.keys(entry).forEach((key) => {
      if (metaKeys.includes(key)) return;
      const rawValue = entry[key];
      if (rawValue !== undefined && rawValue !== null && safeTrim(rawValue) !== '') {
        // Add as extra info field (will not overwrite core fields)
        fields.push({
          name: `${safeTrim(key).replace(/\s+/g, '_').toLowerCase()}_${index}`,
          label: safeTrim(key),
          type: 'info',
          value: safeTrim(rawValue),
        });
      }
    });

    // ---- 4. Conditional logic (trigger condition parsing, Grok & robust) ----
    const trigger = safeTrim(entry['trigger condition (if)']);
    if (trigger) {
      // Try to parse: field operator value
      const match = trigger.match(/^(.+?)\s*(==|!=|>|<|>=|<=)\s*(.+)$/);
      let conditionalLogic: { dependsOn: string; showIfValue: any };
      if (match) {
        const dependsOn = safeTrim(match[1]).replace(/\s+/g, '_').toLowerCase();
        let showIfValue: any = safeTrim(match[3]).replace(/^"|"$/g, '');
        // Try to coerce to boolean/number if applicable
        if (showIfValue.toLowerCase() === 'true') showIfValue = true;
        else if (showIfValue.toLowerCase() === 'false') showIfValue = false;
        else if (!isNaN(Number(showIfValue))) showIfValue = Number(showIfValue);
        conditionalLogic = { dependsOn, showIfValue };
      } else {
        conditionalLogic = {
          dependsOn: trigger.replace(/\s+/g, '_').toLowerCase(),
          showIfValue: true,
        };
      }
      // Apply condition to all fields, or selectively if needed later
      fields.forEach(field => {
        field.conditionalLogic = conditionalLogic;
      });
    }

    // ---- 5. File upload field if docs required (robust) ----
    if (docs) {
      fields.push({
        name: `file_upload_${index}`,
        label: `Upload Documentation: ${docs}`,
        type: 'file',
        validation: { required: true },
        documentationRequired: docArray,
        notes: 'Attach all required documentation for compliance.',
      });
    }

    // ---- 6. Policy citation/info if present ----
    if (policyCitation) {
      fields.push({
        name: `policy_citation_${index}`,
        label: 'Policy Citation',
        type: 'info',
        value: policyCitation,
      });
    }

    // ---- 7. Return the FormStep for this checklist entry ----
    return {
      title,
      fields,
      originalNotes: notes,
      phase,
      requirement,
      docs,
      policyCitation,
    };
  });
}

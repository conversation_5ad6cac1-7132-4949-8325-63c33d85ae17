<%_ if (style !== 'none') { _%>
  <%_ if (styledModule && (styledModule === 'styled-jsx' || styledModule === 'styled-components'))  { _%>
'use client';
  <%_ } _%> 
  <%_ if (styledModule && styledModule !== 'styled-jsx') { var wrapper = 'StyledPage'; _%>
import styled from '<%= styledModule %>';
<%_ } else { var wrapper = 'div'; _%>
<%- (style !== 'styled-jsx' && style !== 'tailwind') ? `import styles from './page.module.${style}';` : '' %>
  <%_ } _%>

  <%_ if (styledModule && styledModule !== 'styled-jsx') { _%>
const StyledPage = styled.div`<%- pageStyleContent %>`;
  <%_ } _%>
<% } %>
export default function Index() {
  /*
   * Replace the elements below with your own.
   *
   * Note: The corresponding styles are in the ./<%= fileName %>.<%= style %> file.
   */
  return (
    <<%= wrapper %><% if (!styledModule && style !== 'tailwind') {%> className={styles.page}<% } %>>
      <%- styledModule === 'styled-jsx' && style !== 'none' ? `<style jsx>{\`${pageStyleContent}\`}</style>` : `` %>
      <%- appContent %>
    </<%= wrapper %>>
  );
};

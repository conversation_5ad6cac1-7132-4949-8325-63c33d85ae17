
"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { QAReview } from "@/components/projects/qa-review"
import { ReportGenerator } from "@/components/projects/report-generator"
import { formatDate, getStatusColor } from "@/lib/utils"
import { 
  ArrowRight, 
  CheckCircle2, 
  FileText
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import Link from "next/link"

interface ProjectTabsProps {
  project: any
  complianceSteps: any[]
  documents: any[]
  qaReviews: any[]
  latestReview: any | null
}

export function ProjectTabs({ 
  project, 
  complianceSteps, 
  documents, 
  qaReviews,
  latestReview
}: ProjectTabsProps) {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="mb-8">
      <TabsList className="grid grid-cols-3 w-full max-w-md mb-6">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="documents">Documents</TabsTrigger>
        <TabsTrigger value="qa">QA Review</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-heading">
                Description
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {project.description || "No description provided."}
              </p>
            </div>

            {/* Progress */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white font-heading">
                  Compliance Progress
                </h2>
                <span className="text-sm font-medium">
                  {complianceSteps.filter(step => step.isCompleted).length} of {complianceSteps.length} steps completed
                </span>
              </div>
              <Progress value={project.progress} className="h-2 mb-6" />
              
              <div className="space-y-4">
                {complianceSteps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className="flex-shrink-0 mr-4">
                      {step.isCompleted ? (
                        <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                          <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                      ) : (
                        <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {index + 1}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex justify-between items-center">
                        <p className={`text-sm font-medium ${
                          step.isCompleted 
                            ? "text-green-600 dark:text-green-400" 
                            : "text-gray-900 dark:text-white"
                        } font-heading`}>
                          {step.title}
                        </p>
                        <Button asChild variant="ghost" size="sm" className="ml-2">
                          <Link href={`/projects/${project.id}/wizard/${step.id}`}>
                            {step.isCompleted ? "Review" : "Start"}
                            <ArrowRight className="h-4 w-4 ml-1" />
                          </Link>
                        </Button>
                      </div>
                      {step.description && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {step.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-8">
            {/* Documents */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white font-heading">
                  Documents
                </h2>
                <span className="text-sm font-medium">
                  {documents.length} total
                </span>
              </div>
              
              <div className="space-y-3">
                {documents.slice(0, 3).map((doc) => (
                  <div key={doc.id} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-3">
                      <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate font-heading">
                        {doc.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Uploaded on {formatDate(doc.uploadedAt)}
                      </p>
                    </div>
                  </div>
                ))}
                
                {documents.length > 3 && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-3"
                    onClick={() => setActiveTab("documents")}
                  >
                    View All Documents
                  </Button>
                )}
              </div>
            </div>

            {/* Latest QA Review */}
            {latestReview && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-heading">
                  Latest QA Review
                </h2>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        latestReview.status
                      )}`}
                    >
                      {latestReview.status.replace("_", " ")}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                      {formatDate(latestReview.createdAt)}
                    </span>
                  </div>
                  
                  {latestReview.comments && (
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md text-sm text-gray-600 dark:text-gray-300">
                      {latestReview.comments}
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Reviewed by: {latestReview.reviewer.name}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="documents">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 font-heading">
            Project Documents
          </h2>
          
          <div className="space-y-4">
            {documents.map((doc) => (
              <div key={doc.id} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <div className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-md mr-4">
                  <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                    <div>
                      <p className="text-base font-medium text-gray-900 dark:text-white font-heading">
                        {doc.name}
                      </p>
                      <div className="mt-1 flex flex-wrap gap-x-4 gap-y-1 text-xs text-gray-500 dark:text-gray-400">
                        <span>Uploaded: {formatDate(doc.uploadedAt)}</span>
                        <span>Size: {(doc.size / 1000000).toFixed(1)} MB</span>
                        {doc.category && <span>Category: {doc.category.replace("-", " ")}</span>}
                      </div>
                    </div>
                    <div className="mt-2 sm:mt-0 flex space-x-2">
                      <Button size="sm" variant="outline">
                        Preview
                      </Button>
                      <Button size="sm" variant="ghost">
                        Download
                      </Button>
                    </div>
                  </div>
                  {doc.stepName && (
                    <div className="mt-2">
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                        {doc.stepName}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 flex justify-center">
            <Button asChild>
              <Link href={`/projects/${project.id}/documents`}>
                <FileText className="h-4 w-4 mr-2" />
                Manage Documents
              </Link>
            </Button>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="qa">
        <div>
          <QAReview projectId={project.id} />
          
          <div className="mt-8">
            <ReportGenerator projectId={project.id} />
          </div>
        </div>
      </TabsContent>
    </Tabs>
  )
}

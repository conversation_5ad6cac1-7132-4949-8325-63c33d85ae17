import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Chip,
  Box
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { useReview } from '../../hooks/useReview';

interface MitigationReviewProps {
  projectId: number;
}

export const MitigationReview: React.FC<MitigationReviewProps> = ({ projectId }) => {
  const { createReview, reviews, loading } = useReview(projectId);
  const [formData, setFormData] = useState({
    costEstimate: '',
    recommendation: '',
    recommendations: [] as string[],
    implementationTimeline: 0
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createReview('mitigation', {
        cost_estimate: parseFloat(formData.costEstimate),
        recommendations: formData.recommendations,
        implementation_timeline: formData.implementationTimeline
      });
      // Reset form
      setFormData({
        costEstimate: '',
        recommendation: '',
        recommendations: [],
        implementationTimeline: 0
      });
    } catch (error) {
      console.error('Error creating mitigation review:', error);
    }
  };

  const addRecommendation = () => {
    if (formData.recommendation.trim()) {
      setFormData({
        ...formData,
        recommendations: [...formData.recommendations, formData.recommendation.trim()],
        recommendation: ''
      });
    }
  };

  const mitigationReviews = reviews.filter(review => review.type === 'mitigation');

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Create Mitigation Review
            </Typography>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Cost Estimate"
                    type="number"
                    value={formData.costEstimate}
                    onChange={(e) => setFormData({
                      ...formData,
                      costEstimate: e.target.value
                    })}
                    required
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Implementation Timeline (days)"
                    type="number"
                    value={formData.implementationTimeline}
                    onChange={(e) => setFormData({
                      ...formData,
                      implementationTimeline: parseInt(e.target.value)
                    })}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box display="flex" gap={1}>
                    <TextField
                      fullWidth
                      label="Recommendation"
                      value={formData.recommendation}
                      onChange={(e) => setFormData({
                        ...formData,
                        recommendation: e.target.value
                      })}
                    />
                    <Button
                      variant="contained"
                      onClick={addRecommendation}
                      disabled={!formData.recommendation.trim()}
                    >
                      Add
                    </Button>
                  </Box>
                </Grid>
                {formData.recommendations.length > 0 && (
                  <Grid item xs={12}>
                    <List>
                      {formData.recommendations.map((rec, index) => (
                        <ListItem key={index}>
                          <ListItemText primary={rec} />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={loading}
                  >
                    Create Review
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Grid>

      {mitigationReviews.length > 0 && (
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Existing Mitigation Reviews
              </Typography>
              <List>
                {mitigationReviews.map((review) => (
                  <ListItem key={review.id} divider>
                    <ListItemText
                      primary={`Cost Estimate: $${review.cost_estimate}`}
                      secondary={
                        <>
                          <Typography component="span" variant="body2">
                            Timeline: {review.implementation_timeline} days
                          </Typography>
                          <br />
                          <Typography component="span" variant="body2">
                            Created: {new Date(review.created_at).toLocaleDateString()}
                          </Typography>
                        </>
                      }
                    />
                    <Chip
                      label={review.status}
                      color={
                        review.status === 'completed' ? 'success' :
                        review.status === 'rejected' ? 'error' :
                        review.status === 'in_progress' ? 'warning' : 'default'
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      )}
    </Grid>
  );
};

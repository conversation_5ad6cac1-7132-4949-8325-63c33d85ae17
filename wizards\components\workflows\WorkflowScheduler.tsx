import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Workflow } from '../../types/workflow';
import { useApi } from '../../contexts/ApiContext';

interface Schedule {
  id: string;
  workflowId: string;
  name: string;
  description: string;
  startTime: string;
  endTime?: string;
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    daysOfWeek?: number[];
    dayOfMonth?: number;
  };
  enabled: boolean;
}

interface WorkflowSchedulerProps {
  workflow: Workflow;
  onClose?: () => void;
}

const WorkflowScheduler: React.FC<WorkflowSchedulerProps> = ({ workflow, onClose }) => {
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<Schedule | null>(null);
  const [formData, setFormData] = useState<Partial<Schedule>>({
    name: '',
    description: '',
    startTime: new Date().toISOString(),
    enabled: true,
  });

  const api = useApi();

  useEffect(() => {
    fetchSchedules();
  }, [workflow.id]);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/workflows/${workflow.id}/schedules`);
      setSchedules(response.data);
    } catch (err) {
      setError('Failed to fetch schedules');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSchedule = async () => {
    try {
      setLoading(true);
      await api.post(`/workflows/${workflow.id}/schedules`, formData);
      await fetchSchedules();
      setOpenDialog(false);
      setFormData({
        name: '',
        description: '',
        startTime: new Date().toISOString(),
        enabled: true,
      });
    } catch (err) {
      setError('Failed to create schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateSchedule = async () => {
    if (!editingSchedule) return;

    try {
      setLoading(true);
      await api.put(`/workflows/${workflow.id}/schedules/${editingSchedule.id}`, formData);
      await fetchSchedules();
      setOpenDialog(false);
      setEditingSchedule(null);
      setFormData({
        name: '',
        description: '',
        startTime: new Date().toISOString(),
        enabled: true,
      });
    } catch (err) {
      setError('Failed to update schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSchedule = async (scheduleId: string) => {
    try {
      setLoading(true);
      await api.delete(`/workflows/${workflow.id}/schedules/${scheduleId}`);
      await fetchSchedules();
    } catch (err) {
      setError('Failed to delete schedule');
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = (schedule: Schedule) => {
    setEditingSchedule(schedule);
    setFormData(schedule);
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
    setEditingSchedule(null);
    setFormData({
      name: '',
      description: '',
      startTime: new Date().toISOString(),
      enabled: true,
    });
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">Workflow Schedules</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
        >
          New Schedule
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Start Time</TableCell>
                <TableCell>Recurrence</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {schedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell>{schedule.name}</TableCell>
                  <TableCell>{schedule.description}</TableCell>
                  <TableCell>{new Date(schedule.startTime).toLocaleString()}</TableCell>
                  <TableCell>
                    {schedule.recurrence
                      ? `${schedule.recurrence.frequency} (every ${schedule.recurrence.interval} ${
                          schedule.recurrence.frequency
                        })`
                      : 'One-time'}
                  </TableCell>
                  <TableCell>
                    {schedule.enabled ? 'Enabled' : 'Disabled'}
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditClick(schedule)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteSchedule(schedule.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingSchedule ? 'Edit Schedule' : 'New Schedule'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
            />
            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DateTimePicker
                label="Start Time"
                value={new Date(formData.startTime || '')}
                onChange={(date) =>
                  setFormData({ ...formData, startTime: date?.toISOString() || '' })
                }
              />
            </LocalizationProvider>
            <FormControl fullWidth>
              <InputLabel>Recurrence</InputLabel>
              <Select
                value={formData.recurrence?.frequency || 'none'}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    recurrence: e.target.value === 'none' ? undefined : {
                      frequency: e.target.value as 'daily' | 'weekly' | 'monthly',
                      interval: 1,
                    },
                  })
                }
              >
                <MenuItem value="none">One-time</MenuItem>
                <MenuItem value="daily">Daily</MenuItem>
                <MenuItem value="weekly">Weekly</MenuItem>
                <MenuItem value="monthly">Monthly</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>Cancel</Button>
          <Button
            onClick={editingSchedule ? handleUpdateSchedule : handleCreateSchedule}
            variant="contained"
          >
            {editingSchedule ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WorkflowScheduler; 
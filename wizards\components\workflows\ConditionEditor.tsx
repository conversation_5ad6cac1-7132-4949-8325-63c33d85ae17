import React, { useState } from 'react';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Typography,
  IconButton,
  Divider,
  Button,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { Condition } from '../../types/workflow';

interface ConditionEditorProps {
  condition: Condition;
  onUpdate: (condition: Condition) => void;
}

const ConditionEditor: React.FC<ConditionEditorProps> = ({ condition, onUpdate }) => {
  const [params, setParams] = useState(condition.params);

  const handleTypeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onUpdate({
      ...condition,
      type: event.target.value as string,
      params: {},
    });
  };

  const handleOperatorChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    onUpdate({
      ...condition,
      operator: event.target.value as 'AND' | 'OR',
    });
  };

  const handleParamsChange = (field: string, value: any) => {
    const newParams = { ...params, [field]: value };
    setParams(newParams);
    onUpdate({ ...condition, params: newParams });
  };

  const handleAddSubCondition = () => {
    const newCondition: Condition = {
      id: Date.now().toString(),
      type: 'basic',
      operator: 'AND',
      conditions: [],
      params: {},
    };
    onUpdate({
      ...condition,
      conditions: [...condition.conditions, newCondition],
    });
  };

  const handleUpdateSubCondition = (updatedCondition: Condition) => {
    onUpdate({
      ...condition,
      conditions: condition.conditions.map(c =>
        c.id === updatedCondition.id ? updatedCondition : c
      ),
    });
  };

  const handleDeleteSubCondition = (subConditionId: string) => {
    onUpdate({
      ...condition,
      conditions: condition.conditions.filter(c => c.id !== subConditionId),
    });
  };

  const renderBasicCondition = () => {
    return (
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Condition Type</InputLabel>
            <Select
              value={condition.type}
              onChange={handleTypeChange}
            >
              <MenuItem value="comparison">Comparison</MenuItem>
              <MenuItem value="regex">Regular Expression</MenuItem>
              <MenuItem value="custom">Custom</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        {condition.type && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Parameters"
              multiline
              rows={4}
              value={JSON.stringify(params, null, 2)}
              onChange={(e) => {
                try {
                  const newParams = JSON.parse(e.target.value);
                  setParams(newParams);
                  onUpdate({ ...condition, params: newParams });
                } catch (error) {
                  // Invalid JSON, keep the current value
                }
              }}
            />
          </Grid>
        )}
      </Grid>
    );
  };

  const renderCompositeCondition = () => {
    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Operator</InputLabel>
              <Select
                value={condition.operator}
                onChange={handleOperatorChange}
              >
                <MenuItem value="AND">AND</MenuItem>
                <MenuItem value="OR">OR</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box sx={{ mt: 2 }}>
          {condition.conditions.map((subCondition) => (
            <Box key={subCondition.id} sx={{ mb: 2, pl: 2, borderLeft: '2px solid #ccc' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="subtitle2">Sub-condition</Typography>
                <IconButton
                  size="small"
                  onClick={() => handleDeleteSubCondition(subCondition.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
              <ConditionEditor
                condition={subCondition}
                onUpdate={handleUpdateSubCondition}
              />
            </Box>
          ))}
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddSubCondition}
            sx={{ mt: 1 }}
          >
            Add Sub-condition
          </Button>
        </Box>
      </Box>
    );
  };

  return (
    <Box>
      {condition.type === 'composite' ? (
        renderCompositeCondition()
      ) : (
        renderBasicCondition()
      )}
    </Box>
  );
};

export default ConditionEditor; 
"""
Enhanced Policy Matcher Service for ComplianceMax

This module implements an improved policy matching service that integrates the
advanced features from the refactored policy matcher implementation.
"""

import logging
import time
from typing import Dict, List, Tuple, Any, Optional
import os
import asyncio
from concurrent.futures import ProcessPoolExecutor
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
from fastapi import HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

# Configuration constants
MAX_WORKERS = 4
CACHE_TTL = 3600  # 1 hour
MATCH_THRESHOLD = 0.5
TOP_MATCHES_COUNT = 5
IMPORTANT_PHRASES_COUNT = 10

# Key compliance topics with associated keywords
KEY_COMPLIANCE_TOPICS = {
    "Cost Reasonableness": [
        "reasonable cost", "cost analysis", "market research", "price analysis",
        "competitive", "historical prices", "independent estimate"
    ],
    "Procurement": [
        "procurement", "contract", "bidding", "vendor", "supplier",
        "purchase", "acquisition", "solicitation"
    ],
    "Environmental": [
        "environmental", "historic", "preservation", "endangered",
        "wetland", "floodplain", "contamination", "hazardous"
    ],
    "Insurance": [
        "insurance", "insured", "coverage", "policy", "deductible",
        "premium", "claim", "adjuster", "underinsured"
    ]
}

# Priority policies that should be given extra weight
PRIORITY_POLICIES = [
    "Stafford Act",
    "44 CFR",
    "2 CFR 200",
    "FEMA Policy",
    "Public Assistance Program and Policy Guide"
]


class PolicyMatcherService:
    """
    Enhanced service for matching requirements to policies using advanced NLP techniques.
    """
    
    def __init__(self, db: AsyncSession = None):
        """Initialize the policy matcher service."""
        self.db = db
        
        # Initialize NLP models
        self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Create cache directory if needed
        os.makedirs("cache", exist_ok=True)
    
    async def match_requirements_to_policies(
        self,
        requirement_text: str,
        dr_number: str,
        incident_date: str,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Match requirements to relevant policies using advanced matching techniques.
        
        Args:
            requirement_text: Text containing requirements to match
            dr_number: Disaster declaration number
            incident_date: Date of the incident
            use_cache: Whether to use cached results
            
        Returns:
            Dictionary with matching results
        """
        start_time = time.time()
        logger.info(f"Starting requirement-policy matching for DR-{dr_number}")
        
        try:
            # Extract requirements from text
            requirements = await self._extract_requirements(requirement_text)
            if not requirements:
                raise HTTPException(
                    status_code=400,
                    detail="No requirements could be extracted from the provided text"
                )
            
            # Get applicable policies
            policies = await self._get_applicable_policies(dr_number, incident_date)
            if not policies:
                raise HTTPException(
                    status_code=404,
                    detail=f"No applicable policies found for DR-{dr_number} on {incident_date}"
                )
            
            # Process requirements and policies in parallel
            matches = await self._match_with_multiple_techniques(requirements, policies)
            
            # Format results
            results = self._format_matching_results(matches, requirements, policies)
            
            total_time = time.time() - start_time
            logger.info(f"Completed policy matching in {total_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in policy matching: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Policy matching error: {str(e)}")
    
    async def _extract_requirements(self, text: str) -> Dict[str, str]:
        """
        Extract requirements from text using advanced parsing techniques.
        
        Args:
            text: Text to extract requirements from
            
        Returns:
            Dictionary mapping requirement IDs to requirement text
        """
        try:
            # For demonstration, we'll use a simple approach to extract requirements
            # In a real implementation, this would use the document_processor
            
            # Split text into paragraphs
            paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
            
            # Extract requirements (paragraphs containing requirement indicators)
            requirements = {}
            requirement_indicators = ["must", "shall", "required", "necessary", "should"]
            
            for i, para in enumerate(paragraphs):
                for indicator in requirement_indicators:
                    if indicator in para.lower():
                        req_id = f"REQ-{i+1:03d}"
                        requirements[req_id] = para
                        break
            
            # If no requirements were found, use paragraphs as sections
            if not requirements:
                requirements = {f"SEC-{i+1:03d}": para for i, para in enumerate(paragraphs)}
            
            logger.info(f"Extracted {len(requirements)} requirements/sections")
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting requirements: {str(e)}")
            return {"General": text}
    
    async def _get_applicable_policies(self, dr_number: str, incident_date: str) -> Dict[str, str]:
        """
        Get policies applicable to a specific disaster declaration.
        
        Args:
            dr_number: Disaster declaration number
            incident_date: Date of the incident
            
        Returns:
            Dictionary mapping policy IDs to policy text
        """
        try:
            # For demonstration, we'll use sample policies
            # In a real implementation, this would query the database
            
            sample_policies = {
                "POL-1": """
                Public Assistance Program and Policy Guide
                Chapter 7: Debris Removal
                Debris removal from public property is eligible when it:
                - Eliminates immediate threats to lives, public health, and safety;
                - Eliminates immediate threats of significant damage to improved public or private property; or
                - Ensures economic recovery of the affected community to the benefit of the community at large.
                Applicants must complete debris removal within 180 days of the disaster declaration date.
                All debris removal operations must be documented with photographs and load tickets.
                """,
                "POL-2": """
                2 CFR 200 - Uniform Administrative Requirements, Cost Principles, and Audit Requirements for Federal Awards
                §200.404 Reasonable costs.
                A cost is reasonable if, in its nature and amount, it does not exceed that which would be incurred by a prudent person under the circumstances prevailing at the time the decision was made to incur the cost. The question of reasonableness is particularly important when the non-Federal entity is predominantly federally-funded. In determining reasonableness of a given cost, consideration must be given to:
                (a) Whether the cost is of a type generally recognized as ordinary and necessary for the operation of the non-Federal entity or the proper and efficient performance of the Federal award.
                (b) The restraints or requirements imposed by such factors as: sound business practices; arm's-length bargaining; Federal, state, local, tribal, and other laws and regulations; and terms and conditions of the Federal award.
                (c) Market prices for comparable goods or services for the geographic area.
                (d) Whether the individuals concerned acted with prudence in the circumstances considering their responsibilities to the non-Federal entity, its employees, where applicable its students or membership, the public at large, and the Federal Government.
                (e) Whether the non-Federal entity significantly deviates from its established practices and policies regarding the incurrence of costs, which may unjustifiably increase the Federal award's cost.
                """
            }
            
            logger.info(f"Retrieved {len(sample_policies)} applicable policies for DR-{dr_number}")
            return sample_policies
            
        except Exception as e:
            logger.error(f"Error fetching applicable policies: {str(e)}")
            raise
    
    async def _match_with_multiple_techniques(
        self,
        requirements: Dict[str, str],
        policies: Dict[str, str]
    ) -> Dict[str, List[Tuple[str, float]]]:
        """
        Match requirements to policies using multiple matching techniques.
        
        Args:
            requirements: Dictionary mapping requirement IDs to requirement text
            policies: Dictionary mapping policy IDs to policy text
            
        Returns:
            Dictionary mapping requirement IDs to lists of (policy_id, score) tuples
        """
        # Preprocess all texts
        processed_requirements = {
            req_id: self._preprocess_text(req_text)
            for req_id, req_text in requirements.items()
        }
        
        processed_policies = {
            policy_id: self._preprocess_text(policy_text)
            for policy_id, policy_text in policies.items()
        }
        
        # Extract key phrases from requirements
        requirement_phrases = {}
        for req_id, processed_text in processed_requirements.items():
            requirement_phrases[req_id] = self._extract_key_phrases(processed_text)
        
        # Perform matching using multiple techniques
        matches = {}
        
        # Use ProcessPoolExecutor for parallel processing
        with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Prepare tasks
            tasks = []
            for req_id, req_text in requirements.items():
                task = asyncio.get_event_loop().run_in_executor(
                    executor,
                    self._match_requirement,
                    req_id,
                    req_text,
                    processed_requirements[req_id],
                    requirement_phrases[req_id],
                    policies,
                    processed_policies
                )
                tasks.append(task)
            
            # Execute tasks in parallel
            results = await asyncio.gather(*tasks)
            
            # Collect results
            for req_id, req_matches in results:
                matches[req_id] = req_matches
        
        return matches
    
    def _match_requirement(
        self,
        req_id: str,
        req_text: str,
        processed_req: str,
        key_phrases: List[str],
        policies: Dict[str, str],
        processed_policies: Dict[str, str]
    ) -> Tuple[str, List[Tuple[str, float]]]:
        """
        Match a single requirement to all policies.
        
        Args:
            req_id: Requirement ID
            req_text: Original requirement text
            processed_req: Preprocessed requirement text
            key_phrases: Key phrases extracted from the requirement
            policies: Dictionary mapping policy IDs to policy text
            processed_policies: Dictionary mapping policy IDs to preprocessed policy text
            
        Returns:
            Tuple of (req_id, list of (policy_id, score) tuples)
        """
        # Check if this is a special compliance topic
        is_special_topic = False
        special_topic_keywords = []
        for topic, keywords in KEY_COMPLIANCE_TOPICS.items():
            if topic.lower() in req_text.lower() or any(kw.lower() in processed_req for kw in keywords):
                is_special_topic = True
                special_topic_keywords.extend(keywords)
        
        # 1. Keyword frequency matching
        keyword_matches = self._match_with_keyword_frequency(
            key_phrases,
            processed_policies,
            is_special_topic,
            special_topic_keywords
        )
        
        # 2. TF-IDF and cosine similarity matching
        tfidf_matches = self._match_with_tfidf(
            req_text,
            processed_req,
            processed_policies
        )
        
        # Combine results with weighted average
        combined_matches = []
        all_policy_ids = set(m[0] for m in keyword_matches + tfidf_matches)
        
        for policy_id in all_policy_ids:
            # Get scores from each method (default to 0 if not present)
            keyword_score = next((score for p, score in keyword_matches if p == policy_id), 0)
            tfidf_score = next((score for p, score in tfidf_matches if p == policy_id), 0)
            
            # Calculate weighted average
            if is_special_topic:
                # For special topics, favor keyword matching
                combined_score = (keyword_score * 0.7) + (tfidf_score * 0.3)
            else:
                # Otherwise, favor TF-IDF
                combined_score = (keyword_score * 0.3) + (tfidf_score * 0.7)
            
            # Apply boosts
            final_score = self._apply_score_boosts(
                policy_id,
                combined_score,
                req_text,
                is_special_topic,
                special_topic_keywords,
                policies[policy_id]
            )
            
            if final_score > MATCH_THRESHOLD:
                combined_matches.append((policy_id, final_score))
        
        # Sort by score in descending order
        combined_matches.sort(key=lambda x: x[1], reverse=True)
        
        # Return top matches
        return (req_id, combined_matches[:TOP_MATCHES_COUNT])
    
    def _match_with_keyword_frequency(
        self,
        key_phrases: List[str],
        processed_policies: Dict[str, str],
        is_special_topic: bool,
        special_topic_keywords: List[str]
    ) -> List[Tuple[str, float]]:
        """
        Match using keyword frequency.
        
        Args:
            key_phrases: Key phrases from the requirement
            processed_policies: Dictionary of preprocessed policy texts
            is_special_topic: Whether this is a special compliance topic
            special_topic_keywords: Keywords for special compliance topics
            
        Returns:
            List of (policy_id, score) tuples
        """
        matches = []
        
        for policy_id, processed_policy in processed_policies.items():
            # Simple matching using keyword frequency
            score = 0
            for phrase in key_phrases:
                if phrase in processed_policy:
                    occurrences = processed_policy.count(phrase)
                    score += occurrences
            
            # Normalize score
            score = score / len(key_phrases) if key_phrases else 0
            
            # For special topics, check for exact matches of special keywords
            if is_special_topic:
                for keyword in special_topic_keywords:
                    keyword_lower = keyword.lower()
                    if keyword_lower in processed_policy:
                        # Add extra weight for exact matches of special keywords
                        score += 0.1 * processed_policy.count(keyword_lower)
            
            if score > 0:
                matches.append((policy_id, score))
        
        return matches
    
    def _match_with_tfidf(
        self,
        req_text: str,
        processed_req: str,
        processed_policies: Dict[str, str]
    ) -> List[Tuple[str, float]]:
        """
        Match using TF-IDF and cosine similarity.
        
        Args:
            req_text: Original requirement text
            processed_req: Preprocessed requirement text
            processed_policies: Dictionary of preprocessed policy texts
            
        Returns:
            List of (policy_id, score) tuples
        """
        # Prepare documents for TF-IDF
        policy_ids = list(processed_policies.keys())
        documents = [processed_policies[pid] for pid in policy_ids]
        
        # Add the requirement text as the last document
        documents.append(processed_req)
        
        # Create TF-IDF matrix
        vectorizer = TfidfVectorizer(
            max_features=10000,  # Limit features for performance
            stop_words='english',
            ngram_range=(1, 2)  # Include bigrams
        )
        
        try:
            tfidf_matrix = vectorizer.fit_transform(documents)
            
            # Get the requirement vector (last row)
            req_vector = tfidf_matrix[-1]
            
            # Get policy vectors (all rows except the last)
            policy_vectors = tfidf_matrix[:-1]
            
            # Calculate cosine similarity between requirement and each policy
            similarities = cosine_similarity(req_vector, policy_vectors)[0]
            
            # Create list of (policy_id, score) tuples
            matches = [(policy_ids[i], float(similarities[i])) for i in range(len(policy_ids))]
            
            # Filter out low scores and sort by score in descending order
            matches = [(pid, score) for pid, score in matches if score > 0]
            matches.sort(key=lambda x: x[1], reverse=True)
            
            return matches
            
        except Exception as e:
            logger.error(f"Error in TF-IDF matching: {str(e)}")
            return []
    
    def _apply_score_boosts(
        self,
        policy_id: str,
        score: float,
        req_text: str,
        is_special_topic: bool,
        special_topic_keywords: List[str],
        policy_text: str
    ) -> float:
        """
        Apply boosts to matching scores based on various factors.
        
        Args:
            policy_id: Policy ID
            score: Current matching score
            req_text: Requirement text
            is_special_topic: Whether this is a special compliance topic
            special_topic_keywords: Keywords for special compliance topics
            policy_text: Policy text
            
        Returns:
            Boosted score
        """
        # Boost for priority policies
        for priority_policy in PRIORITY_POLICIES:
            if priority_policy.lower() in policy_text.lower():
                score *= 1.2
                break
        
        # Boost for exact phrase matches
        for phrase in special_topic_keywords:
            if len(phrase) > 5 and phrase.lower() in policy_text.lower() and phrase.lower() in req_text.lower():
                score *= 1.1
        
        # Cap score at 1.0
        return min(score, 1.0)
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for matching.
        
        Args:
            text: Text to preprocess
            
        Returns:
            Preprocessed text
        """
        # Convert to lowercase
        text = text.lower()
        
        # Replace newlines with spaces
        text = text.replace('\n', ' ')
        
        # Replace multiple spaces with a single space
        import re
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _extract_key_phrases(self, text: str, max_phrases: int = 10) -> List[str]:
        """
        Extract key phrases from text.
        
        Args:
            text: Text to extract phrases from
            max_phrases: Maximum number of phrases to extract
            
        Returns:
            List of key phrases
        """
        # Split into sentences
        import re
        sentences = re.split(r'[.!?]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Extract noun phrases and important phrases
        phrases = []
        
        # Look for phrases with requirement indicators
        requirement_indicators = [
            "must", "shall", "required", "necessary", "mandatory",
            "should", "recommended", "important", "critical", "essential"
        ]
        
        for sentence in sentences:
            for indicator in requirement_indicators:
                if indicator in sentence:
                    # Get the part after the indicator
                    parts = sentence.split(indicator, 1)
                    if len(parts) > 1 and len(parts[1]) > 5:
                        phrases.append(parts[1].strip())
        
        # Add important sentences if we don't have enough phrases
        if len(phrases) < max_phrases:
            # Sort sentences by length (longer sentences often contain more information)
            sentences.sort(key=len, reverse=True)
            for sentence in sentences:
                if len(sentence) > 10 and sentence not in phrases:
                    phrases.append(sentence)
                if len(phrases) >= max_phrases:
                    break
        
        # Truncate to max_phrases
        return phrases[:max_phrases]
    
    def _format_matching_results(
        self,
        matches: Dict[str, List[Tuple[str, float]]],
        requirements: Dict[str, str],
        policies: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        Format matching results for API response.
        
        Args:
            matches: Dictionary mapping requirement IDs to lists of (policy_id, score) tuples
            requirements: Dictionary mapping requirement IDs to requirement text
            policies: Dictionary mapping policy IDs to policy text
            
        Returns:
            Formatted results dictionary
        """
        results = {
            "total_requirements": len(requirements),
            "total_policies": len(policies),
            "match_count": sum(1 for req_matches in matches.values() if req_matches),
            "requirements": []
        }
        
        for req_id, req_text in requirements.items():
            req_matches = matches.get(req_id, [])
            
            requirement_result = {
                "id": req_id,
                "text": req_text,
                "matches": []
            }
            
            for policy_id, score in req_matches:
                policy_text = policies.get(policy_id, "")
                
                # Extract a relevant excerpt from the policy
                excerpt = self._extract_relevant_excerpt(policy_text, req_text)
                
                requirement_result["matches"].append({
                    "policy_id": policy_id,
                    "confidence": score,
                    "excerpt": excerpt
                })
            
            results["requirements"].append(requirement_result)
        
        return results
    
    def _extract_relevant_excerpt(self, policy_text: str, req_text: str, max_length: int = 300) -> str:
        """
        Extract a relevant excerpt from policy text based on requirement text.
        
        Args:
            policy_text: Full policy text
            req_text: Requirement text to match against
            max_length: Maximum length of the excerpt
            
        Returns:
            Relevant excerpt from the policy
        """
        # If policy text is short enough, return it all
        if len(policy_text) <= max_length:
            return policy_text
        
        # Try to find the most relevant part using key phrases
        key_phrases = self._extract_key_phrases(req_text, max_phrases=5)
        
        best_start = 0
        best_score = 0
        
        # Check different starting positions in the policy text
        for start in range(0, len(policy_text) - max_length, max_length // 2):
            excerpt = policy_text[start:start + max_length]
            
            # Score this excerpt based on key phrase matches
            score = 0
            for phrase in key_phrases:
                if phrase.lower() in excerpt.lower():
                    score += 1
            
            if score > best_score:
                best_score = score
                best_start = start
        
        # If no good match found, return the beginning of the policy
        if best_score == 0:
            return policy_text[:max_length]
        
        # Return the best excerpt
        return policy_text[best_start:best_start + max_length]

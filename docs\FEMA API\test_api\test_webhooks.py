import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from src.models.user import User
from src.models.webhook import Webhook, WebhookDelivery
from src.core.auth import create_access_token
from datetime import datetime, timedelta

def test_create_webhook(client: TestClient, db_session: Session, test_user: User):
    """Test creating a new webhook."""
    access_token = create_access_token(data={"sub": test_user.email})
    
    webhook_data = {
        "url": "https://example.com/webhook",
        "events": ["document.created", "document.updated"],
        "secret": "test_secret"
    }
    
    response = client.post(
        "/api/v1/webhooks/",
        json=webhook_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["url"] == webhook_data["url"]
    assert set(data["events"]) == set(webhook_data["events"])
    assert data["is_active"] is True
    assert "id" in data
    assert "created_at" in data

def test_create_webhook_invalid_url(client: TestClient, test_user: User):
    """Test creating webhook with invalid URL."""
    access_token = create_access_token(data={"sub": test_user.email})
    
    webhook_data = {
        "url": "not-a-url",
        "events": ["document.created"],
        "secret": "test_secret"
    }
    
    response = client.post(
        "/api/v1/webhooks/",
        json=webhook_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 422
    assert "url" in response.json()["detail"].lower()

def test_list_webhooks(client: TestClient, db_session: Session, test_user: User):
    """Test listing webhooks."""
    # Create multiple webhooks
    webhooks = [
        Webhook(
            url=f"https://example.com/webhook_{i}",
            events=["document.created"],
            secret="test_secret",
            created_by=test_user.id
        )
        for i in range(3)
    ]
    db_session.add_all(webhooks)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        "/api/v1/webhooks/",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 3
    assert all(isinstance(webhook["id"], int) for webhook in data)
    assert all(isinstance(webhook["url"], str) for webhook in data)

def test_get_webhook(client: TestClient, db_session: Session, test_user: User):
    """Test retrieving a webhook."""
    # Create webhook
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="test_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        f"/api/v1/webhooks/{webhook.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == webhook.id
    assert data["url"] == webhook.url
    assert data["events"] == webhook.events

def test_update_webhook(client: TestClient, db_session: Session, test_user: User):
    """Test updating a webhook."""
    # Create webhook
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="test_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    update_data = {
        "url": "https://example.com/updated-webhook",
        "events": ["document.created", "document.deleted"],
        "is_active": False
    }
    
    response = client.patch(
        f"/api/v1/webhooks/{webhook.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["url"] == update_data["url"]
    assert set(data["events"]) == set(update_data["events"])
    assert data["is_active"] == update_data["is_active"]

def test_delete_webhook(client: TestClient, db_session: Session, test_user: User):
    """Test deleting a webhook."""
    # Create webhook
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="test_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.delete(
        f"/api/v1/webhooks/{webhook.id}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify webhook is deleted
    deleted_webhook = db_session.query(Webhook).filter(Webhook.id == webhook.id).first()
    assert deleted_webhook is None

def test_webhook_deliveries(client: TestClient, db_session: Session, test_user: User):
    """Test retrieving webhook deliveries."""
    # Create webhook and deliveries
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="test_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    deliveries = [
        WebhookDelivery(
            webhook_id=webhook.id,
            event="document.created",
            payload={"test": "data"},
            status="success",
            response_code=200,
            created_at=datetime.utcnow() - timedelta(hours=i)
        )
        for i in range(3)
    ]
    db_session.add_all(deliveries)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.get(
        f"/api/v1/webhooks/{webhook.id}/deliveries",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    assert all(isinstance(delivery["id"], int) for delivery in data)
    assert all(isinstance(delivery["status"], str) for delivery in data)

def test_retry_webhook_delivery(client: TestClient, db_session: Session, test_user: User):
    """Test retrying a failed webhook delivery."""
    # Create webhook and failed delivery
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="test_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    delivery = WebhookDelivery(
        webhook_id=webhook.id,
        event="document.created",
        payload={"test": "data"},
        status="failed",
        response_code=500,
        error_message="Connection timeout"
    )
    db_session.add(delivery)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.post(
        f"/api/v1/webhooks/deliveries/{delivery.id}/retry",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "pending"
    assert "retry_attempt" in data

def test_webhook_secret_rotation(client: TestClient, db_session: Session, test_user: User):
    """Test rotating webhook secret."""
    # Create webhook
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="old_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    response = client.post(
        f"/api/v1/webhooks/{webhook.id}/rotate-secret",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "secret" in data
    assert data["secret"] != "old_secret"
    
    # Verify secret was updated in database
    updated_webhook = db_session.query(Webhook).filter(Webhook.id == webhook.id).first()
    assert updated_webhook.secret != "old_secret"

def test_webhook_event_subscription(client: TestClient, db_session: Session, test_user: User):
    """Test subscribing to webhook events."""
    # Create webhook
    webhook = Webhook(
        url="https://example.com/webhook",
        events=["document.created"],
        secret="test_secret",
        created_by=test_user.id
    )
    db_session.add(webhook)
    db_session.commit()
    
    access_token = create_access_token(data={"sub": test_user.email})
    subscription_data = {
        "events": ["document.created", "document.updated", "document.deleted"]
    }
    
    response = client.post(
        f"/api/v1/webhooks/{webhook.id}/subscribe",
        json=subscription_data,
        headers={"Authorization": f"Bearer {access_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert set(data["events"]) == set(subscription_data["events"])
    
    # Verify events were updated in database
    updated_webhook = db_session.query(Webhook).filter(Webhook.id == webhook.id).first()
    assert set(updated_webhook.events) == set(subscription_data["events"]) 
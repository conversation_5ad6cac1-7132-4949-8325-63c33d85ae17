"""
Enhanced FEMA Policy Matcher

This script analyzes FEMA policy documents and matches them to requirements.
Enhanced to include processing for all document types including:
- Building codes & standards documents
- Flood information
- Insurance information
- Mitigation documents
- Environmental and Historic Preservation (EHP) documents
- Excel forms and spreadsheets
- Memos, job aids, and guidance documents

Prepared for future integration with AI API for real-time information retrieval.
"""

import os
import re
import string
import logging
import pandas as pd
import docx
import PyPDF2
import requests
from bs4 import BeautifulSoup
from collections import defaultdict, Counter
from datetime import datetime
import openpyxl
import numpy as np
import csv
import io
from pathlib import Path

# Set up logging with enhanced detail
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("policy_matcher_enhanced.log"),
        logging.StreamHandler()
    ]
)

class EnhancedPolicyMatcher:
    """Enhanced Policy Matcher with improved document processing capabilities"""
    
    def __init__(self, govstar_file, policies_dir, output_file=None):
        """Initialize the policy matcher with enhanced capabilities"""
        self.govstar_file = govstar_file
        self.policies_dir = policies_dir
        self.output_file = output_file or "enhanced_policy_requirements_report.html"
        self.requirements = {}
        self.policies = {}
        self.matches = {}
        self.policy_categories = {}
        self.document_types = {}
        
        # Track processing statistics
        self.processed_files = []
        self.skipped_files = []
        self.error_files = []
        
        # Priority policies to ensure they're included in analysis
        self.priority_policies = [
            "PAPPG v4.pdf", 
            "PAPPG v3.1.pdf", 
            "PAPPG v2.pdf",
            "PAPPG v1.pdf",
            "2 CFR Part 200.pdf",
            "44 CFR Emergency Management.pdf",
            "44 CFR Part 13.pdf",
            "44 CFR 206 Subpart H.pdf",
            "2 CFR 200 Uniform Guidance.pdf"
        ]
        
        # Document type classification using filename patterns
        self.document_classifications = {
            "Building Code": [
                "building_code", "building-code", "building_codes", "building-codes", 
                "codes_and_standards", "building_standard", "ibc", "asce", "nfpa", 
                "seismic", "model-code", "model_code", "code-coordinated", 
                "code_coordinated", "building_safety", "floodplain_management"
            ],
            "Flood Information": [
                "flood", "floodplain", "nfip", "base_flood", "flood_insurance", 
                "flood_provision", "flood_hazard", "flood_zone", "flood_map", 
                "flood_proof", "flood_resistant", "flood_damage"
            ],
            "Insurance": [
                "insurance", "policy_coverage", "policy_provision", "coverage_limit", 
                "insured", "premium", "policy_clause", "claim", "adjuster", "underwriting"
            ],
            "Mitigation": [
                "mitigation", "hazard_mitigation", "mitigation_guide", "mitigation_project", 
                "mit", "mitigation_plan", "mitigation_strategy", "risk_reduction", 
                "reduce_risk", "resilience", "disaster_resilience"
            ],
            "Environmental & Historic Preservation": [
                "environmental", "historic", "ehp", "preservation", "historic_property", 
                "section_106", "nepa", "environmental_assessment", "environmental_impact", 
                "cultural_resource", "environmental_review"
            ],
            "Cost Reasonableness": [
                "reasonable_cost", "reasonableness", "cost_comparison", "cost_analysis", 
                "price_analysis", "market_price", "cost_estimate", "reasonable_and_necessary", 
                "cost_principle", "cost_documentation"
            ],
            "Form": [
                "form", "worksheet", "spreadsheet", "application", "checklist", 
                "questionnaire", "ff-104", "tracker", "template", "calculator", "estimation_tool"
            ],
            "Memo": [
                "memo", "memorandum", "guidance_memo", "policy_memo", 
                "memo_10", "directive", "bulletin", "circular", "sop"
            ],
            "Job Aid": [
                "job_aid", "job-aid", "aid", "quick_reference", "quick_guide", 
                "field_guide", "fieldguide", "desk_reference", "toolkit", "tool-kit"
            ],
            "Guidance": [
                "guide", "guidance", "guideline", "instruction", "procedure", 
                "advisory", "best_practice", "recommended_practice", "procedure", "manual"
            ],
            "OIG Reports & Audits": [
                "oig", "audit", "inspector", "investigation", "review", 
                "finding", "recommendation", "report", "assessment", "evaluation"
            ]
        }
        
        # Key compliance topics to emphasize in matching
        self.key_compliance_topics = {
            "Cost Reasonableness": [
                "reasonable cost", "cost reasonableness", "reasonable and necessary", 
                "cost analysis", "price analysis", "competitive bidding", "cost comparison",
                "market price", "fair market value", "cost principle", "allowable cost",
                "procurement", "procurement method", "cost documentation"
            ],
            "Costing Protocols": [
                "costing protocol", "cost estimation", "cost documentation", "direct costs", 
                "indirect costs", "force account labor", "force account equipment", "CEF", 
                "cost estimating format", "unit cost", "labor cost", "equipment cost",
                "material cost", "construction cost", "cost worksheet", "cost calculation"
            ],
            "Consensus-Based Codes and Standards": [
                "consensus-based", "codes and standards", "building code", "substantial damage", 
                "substantial improvement", "code compliance", "ASCE", "NFPA", "IBC", "IRC",
                "flood resistant", "flood proof", "flood insurance", "floodplain", "base flood elevation",
                "building standard", "seismic", "wind load", "load resistance", "resilient construction"
            ],
            "Environmental & Historic Preservation": [
                "environmental review", "historic preservation", "environmental assessment",
                "section 106", "NEPA", "environmental impact", "cultural resource", 
                "endangered species", "wetland", "floodplain", "EHP", "environmental compliance",
                "environmental consideration", "historic property", "historic structure"
            ],
            "Management Costs (Category Z)": [
                "management cost", "category z", "direct administrative cost", "indirect cost",
                "administrative expense", "project management", "grant management", "program management",
                "subrecipient management", "recipient management", "management funding", "administration"
            ],
            "Case Management File": [
                "case management", "case file", "documentation requirements", "case processing",
                "project file", "documentation retention", "case records", "file organization",
                "record keeping", "document organization", "filing system", "documentation standards"
            ],
            "Project Formulation": [
                "project formulation", "formulating projects", "project development", "scope development",
                "small projects", "large projects", "alternative procedures", "project approval",
                "improved project", "alternate project", "cost effective", "project eligibility"
            ],
            "Site Inspection Process": [
                "site inspection", "field assessment", "damage assessment", "preliminary damage assessment",
                "site visit", "field inspection", "damage verification", "validation", "site documentation",
                "photo documentation", "inspection report", "site observation", "disaster assessment"
            ],
            "Project Worksheet Preparation": [
                "project worksheet", "pw preparation", "worksheet preparation", "pw development",
                "damage description", "dimensions", "scope of work", "facilitated discussion",
                "pw review", "pw approval", "pw processing", "worksheet documentation"
            ],
            "Damage Description and Dimensions": [
                "damage description", "damage dimensions", "quantifying damage", "documenting damage",
                "detailed damage", "dimensional analysis", "measure damage", "damage inventory",
                "quantity take-off", "quantification", "dimension calculation", "measurement protocol"
            ],
            "Scope of Work": [
                "scope of work", "sow", "work scope", "eligible work", "work eligibility",
                "repair scope", "replacement scope", "hazard mitigation scope", "work description",
                "eligible activity", "eligible repair", "construction scope", "repair method"
            ],
            "Cost Estimates": [
                "cost estimate", "estimate preparation", "unit cost", "cost development",
                "completed work", "work to be completed", "zero dollar", "cef", "cost calculation",
                "estimate methodology", "cost derivation", "cost justification", "pricing"
            ],
            "Insurance": [
                "insurance", "insurance requirement", "insurance coverage", "insurance policy",
                "insurance settlement", "flood insurance", "property insurance", "deductible",
                "insurance claim", "insurance adjustment", "insurance recovery", "insured loss"
            ],
            "Direct Administrative Costs": [
                "direct administrative", "dac", "administrative costs", "grant administration",
                "project administration", "administrative expenses", "administrative activities",
                "administrative management", "management activities", "administrative task"
            ],
            "Procurement & Contracting": [
                "procurement", "contracting", "contract", "agreement", "vendor",
                "bidding", "competitive", "sole source", "emergency procurement",
                "contract clause", "contract provision", "competitive requirement"
            ],
            "Flood Resilience": [
                "flood resilience", "flood mitigation", "flood hazard", "flood risk",
                "floodplain", "base flood elevation", "special flood hazard area",
                "flood resistant", "flood protection", "flood proofing", "flood insurance"
            ],
            "Mitigation and Resilience": [
                "mitigation", "resilience", "hazard mitigation", "risk reduction",
                "build back better", "406 mitigation", "404 mitigation", "pre-disaster mitigation",
                "mitigation planning", "resilient recovery", "resilient infrastructure"
            ]
        }
        
        # Initialize the database
        self.load_requirements()
        self.load_and_classify_policies()
    
    def extract_text_from_docx(self, file_path):
        """Extract text from DOCX files with enhanced error handling"""
        try:
            doc = docx.Document(file_path)
            # Extract text from paragraphs
            text = "\n".join([para.text for para in doc.paragraphs])
            
            # Also extract from tables if present
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += "\n" + cell.text
            
            return text
        except Exception as e:
            logging.error(f"Error extracting text from DOCX {file_path}: {e}")
            return ""
    
    def extract_text_from_html(self, file_path):
        """Extract text from HTML files with better content filtering"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
            soup = BeautifulSoup(content, 'html.parser')
            
            # Remove script, style elements, and navigation
            for element in soup(["script", "style", "nav", "footer", "header"]):
                element.extract()
            
            # Extract main content
            if soup.main:
                main_content = soup.main.get_text(separator=' ')
            elif soup.find('div', {'id': 'content'}):
                main_content = soup.find('div', {'id': 'content'}).get_text(separator=' ')
            elif soup.find('div', {'class': 'content'}):
                main_content = soup.find('div', {'class': 'content'}).get_text(separator=' ')
            else:
                # Get all text if no main content area identified
                main_content = soup.get_text(separator=' ')
            
            # Clean up the text
            text = re.sub(r'\s+', ' ', main_content).strip()
            return text
        except Exception as e:
            logging.error(f"Error extracting text from HTML {file_path}: {e}")
            return ""
    
    def extract_text_from_pdf(self, file_path):
        """Extract text from PDF files with fallback methods"""
        text = ""
        
        # Method 1: PyPDF2
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num in range(len(pdf_reader.pages)):
                    try:
                        page = pdf_reader.pages[page_num]
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as page_error:
                        logging.warning(f"Error extracting text from page {page_num} in {file_path}: {page_error}")
                        continue
                
                # If we got no text with the standard method, try extracting raw content
                if not text.strip() and len(pdf_reader.pages) > 0:
                    logging.info(f"Using alternative PDF extraction method for {file_path}")
                    for page_num in range(len(pdf_reader.pages)):
                        try:
                            page = pdf_reader.pages[page_num]
                            content = page.extract_text(0)  # Extract raw content
                            if content:
                                text += content + "\n"
                        except:
                            pass
            
            return text
        except Exception as e:
            logging.error(f"Error extracting text from PDF {file_path}: {e}")
            return ""
    
    def extract_text_from_xlsx(self, file_path):
        """Extract text from Excel files with multiple methods for robustness"""
        try:
            logging.info(f"Extracting text from Excel file: {file_path}")
            
            # Try loading with openpyxl first (the preferred method)
            try:
                workbook = openpyxl.load_workbook(file_path, read_only=True, data_only=True)
                
                text = []
                for sheet_name in workbook.sheetnames:
                    sheet = workbook[sheet_name]
                    text.append(f"SHEET: {sheet_name}")
                    
                    # Process each row and column
                    for row in sheet.iter_rows(values_only=True):
                        row_text = " ".join([str(cell) if cell is not None else "" for cell in row])
                        if row_text.strip():  # Only add non-empty rows
                            text.append(row_text)
                
                return "\n".join(text)
            
            except Exception as openpyxl_error:
                logging.warning(f"Primary Excel extraction failed for {file_path}, trying fallback method: {openpyxl_error}")
                
                # Fallback method: try reading with pandas
                try:
                    # Read all sheets
                    sheet_names = pd.ExcelFile(file_path).sheet_names
                    all_text = []
                    
                    for sheet in sheet_names:
                        df = pd.read_excel(file_path, sheet_name=sheet)
                        all_text.append(f"SHEET: {sheet}")
                        
                        # Convert all cells to string and join
                        for _, row in df.iterrows():
                            row_text = " ".join([str(val) if not pd.isna(val) else "" for val in row])
                            if row_text.strip():
                                all_text.append(row_text)
                    
                    return "\n".join(all_text)
                
                except Exception as pandas_error:
                    logging.error(f"All Excel extraction methods failed for {file_path}: {pandas_error}")
                    return ""
        
        except Exception as e:
            logging.error(f"Error accessing Excel file {file_path}: {str(e)}")
            return ""
    
    def classify_document(self, filename, content=None):
        """Classify document into specialized categories"""
        filename_lower = filename.lower().replace(" ", "_").replace("-", "_")
        
        # First check filename for obvious classifications
        for doc_type, patterns in self.document_classifications.items():
            for pattern in patterns:
                if pattern.lower() in filename_lower:
                    return doc_type
        
        # If content is provided, do a content-based classification
        if content:
            # Count occurrences of classification keywords in content
            type_scores = {}
            content_lower = content.lower()
            
            for doc_type, patterns in self.document_classifications.items():
                score = 0
                for pattern in patterns:
                    score += content_lower.count(pattern.lower().replace("_", " "))
                type_scores[doc_type] = score
            
            # Find type with highest score
            if type_scores:
                max_score = max(type_scores.values())
                if max_score > 0:
                    # Get all types with the max score
                    max_types = [doc_type for doc_type, score in type_scores.items() if score == max_score]
                    return max_types[0]  # Return the first one if multiple match
        
        # Default categorization based on file extension
        ext = os.path.splitext(filename)[1].lower()
        if ext == '.xlsx' or ext == '.xls':
            return "Form"
        elif ext == '.pdf':
            # Check for common patterns in PDF filenames
            if "form" in filename_lower or "ff_104" in filename_lower:
                return "Form"
            elif "guide" in filename_lower or "guidance" in filename_lower:
                return "Guidance"
            elif "memo" in filename_lower:
                return "Memo"
            elif "job_aid" in filename_lower or "aid" in filename_lower:
                return "Job Aid"
            else:
                return "General Policy Document"
        else:
            return "General Policy Document"
    
    def load_and_classify_policies(self):
        """Load, process and classify all policy documents"""
        logging.info("Loading and classifying policy documents...")
        count = 0
        processed_count = 0
        skipped_count = 0
        error_count = 0
        
        # Track different file types and categories
        file_types = defaultdict(int)
        document_categories = defaultdict(int)
        file_sizes = defaultdict(float)
        
        # Get total file count for reporting progress
        total_files = sum(1 for _ in os.listdir(self.policies_dir) if os.path.isfile(os.path.join(self.policies_dir, _)))
        logging.info(f"Found {total_files} total files in policies directory")
        
        # First load priority policy documents to ensure they're included
        for priority_file in self.priority_policies:
            file_path = os.path.join(self.policies_dir, priority_file)
            if os.path.isfile(file_path):
                logging.info(f"Loading priority policy document: {priority_file}")
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
                file_sizes[priority_file] = file_size
                
                text = None
                if priority_file.lower().endswith('.pdf'):
                    text = self.extract_text_from_pdf(file_path)
                    file_types['pdf'] += 1
                elif priority_file.lower().endswith('.docx'):
                    text = self.extract_text_from_docx(file_path)
                    file_types['docx'] += 1
                elif priority_file.lower().endswith('.xlsx') or priority_file.lower().endswith('.xls'):
                    text = self.extract_text_from_xlsx(file_path)
                    file_types['excel'] += 1
                
                if text:
                    self.policies[priority_file] = text
                    # Classify the document
                    category = self.classify_document(priority_file, text)
                    self.document_types[priority_file] = category
                    document_categories[category] += 1
                    
                    count += 1
                    processed_count += 1
                    self.processed_files.append(priority_file)
                    logging.info(f"Successfully loaded priority policy: {priority_file} ({file_size:.2f} MB), type: {category}")
                else:
                    logging.warning(f"Could not extract text from priority policy: {priority_file}")
                    self.error_files.append(priority_file)
                    error_count += 1
        
        # Then load all other policy documents
        processed = 0
        for filename in os.listdir(self.policies_dir):
            # Progress reporting
            processed += 1
            if processed % 100 == 0:
                logging.info(f"Processing progress: {processed}/{total_files} files ({processed/total_files*100:.1f}%)")
            
            # Skip non-files (directories, etc.)
            file_path = os.path.join(self.policies_dir, filename)
            if not os.path.isfile(file_path):
                continue
            
            # Skip priority files we've already processed
            if filename in self.priority_policies:
                continue
            
            # Skip certain file types that aren't documents
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.ico', '.exe', '.dll', '.zip', '.rar')):
                logging.debug(f"Skipping non-document file: {filename}")
                self.skipped_files.append(filename)
                skipped_count += 1
                continue
                
            # Get file extension for tracking
            ext = os.path.splitext(filename)[1].lower().replace('.', '')
            if not ext:
                ext = 'no_extension'
            file_types[ext] += 1
            
            # Get file size
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
            file_sizes[filename] = file_size
            
            # Prioritize processing for specific document types we're interested in
            is_priority_doc = False
            for key_type in ['building', 'code', 'flood', 'insurance', 'mitigation', 'form', 'memo', 'aid', 'guide']:
                if key_type in filename.lower():
                    is_priority_doc = True
                    break
            
            text = None
            try:
                # Process different file types
                if filename.lower().endswith('.docx'):
                    text = self.extract_text_from_docx(file_path)
                elif filename.lower().endswith('.html') or filename.lower().endswith('.htm'):
                    text = self.extract_text_from_html(file_path)
                elif filename.lower().endswith('.pdf'):
                    text = self.extract_text_from_pdf(file_path)
                elif filename.lower().endswith('.xlsx') or filename.lower().endswith('.xls'):
                    text = self.extract_text_from_xlsx(file_path)
                elif filename.lower().endswith('.csv'):
                    # Handle CSV files
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            reader = csv.reader(f)
                            rows = list(reader)
                            text = "\n".join([",".join(row) for row in rows])
                    except Exception as csv_error:
                        logging.warning(f"Error reading CSV {filename}: {csv_error}")
                        text = ""
                else:
                    # Try to treat other files as text files
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            text = f.read()
                        logging.debug(f"Processed as text file: {filename}")
                    except Exception as e:
                        logging.warning(f"Unable to read {filename} as text: {str(e)}")
                        self.skipped_files.append(filename)
                        skipped_count += 1
                        continue
                
                if text:
                    self.policies[filename] = text
                    
                    # Classify the document
                    category = self.classify_document(filename, text)
                    self.document_types[filename] = category
                    document_categories[category] += 1
                    
                    count += 1
                    processed_count += 1
                    self.processed_files.append(filename)
                    
                    if file_size > 1 or is_priority_doc:  # Log large files (> 1MB) or priority doc types
                        log_msg = f"Processed {'large ' if file_size > 1 else ''}file: {filename} ({file_size:.2f} MB), type: {category}"
                        if is_priority_doc:
                            log_msg += " [PRIORITY DOCUMENT TYPE]"
                        logging.info(log_msg)
                else:
                    logging.warning(f"No text extracted from {filename}")
                    self.error_files.append(filename)
                    error_count += 1
            except Exception as e:
                logging.error(f"Error processing {filename}: {str(e)}")
                self.error_files.append(filename)
                error_count += 1
                
        # Log detailed statistics
        logging.info(f"Total files found: {total_files}")
        logging.info(f"Files successfully processed: {processed_count}")
        logging.info(f"Files skipped: {skipped_count}")
        logging.info(f"Files with errors: {error_count}")
        logging.info(f"File types found: {dict(file_types)}")
        logging.info(f"Document categories found: {dict(document_categories)}")
        
        # Log top 10 largest files
        largest_files = sorted(file_sizes.items(), key=lambda x: x[1], reverse=True)[:10]
        logging.info("Top 10 largest files processed:")
        for fname, size in largest_files:
            if fname in self.policies:
                status = f"Successfully processed as {self.document_types.get(fname, 'Unknown')}"
            else:
                status = "Failed to process"
            logging.info(f"{fname}: {size:.2f} MB - {status}")
            
        logging.info(f"Loaded and classified {count} policy documents for matching")
        
        # Write processing summary to a separate log file for reference
        with open("document_processing_summary.txt", "w") as f:
            f.write(f"Document Processing Summary\n")
            f.write(f"=========================\n\n")
            f.write(f"Total files found: {total_files}\n")
            f.write(f"Files successfully processed: {processed_count}\n")
            f.write(f"Files skipped: {skipped_count}\n")
            f.write(f"Files with errors: {error_count}\n\n")
            
            f.write(f"File Types Statistics\n")
            f.write(f"--------------------\n")
            for ftype, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{ftype}: {count}\n")
            
            f.write(f"\nDocument Categories\n")
            f.write(f"------------------\n")
            for category, count in sorted(document_categories.items(), key=lambda x: x[1], reverse=True):
                f.write(f"{category}: {count}\n")
            
            f.write(f"\nProcessed Files ({len(self.processed_files)})\n")
            f.write(f"----------------------------\n")
            for fname in sorted(self.processed_files):
                category = self.document_types.get(fname, "Unknown")
                f.write(f"{fname} - {category}\n")
            
            f.write(f"\nSkipped Files ({len(self.skipped_files)})\n")
            f.write(f"------------------------\n")
            for fname in sorted(self.skipped_files):
                f.write(f"{fname}\n")
            
            f.write(f"\nError Files ({len(self.error_files)})\n")
            f.write(f"---------------------\n")
            for fname in sorted(self.error_files):
                f.write(f"{fname}\n")
        
        return self.policies
    def load_requirements(self):
        """Load and parse requirements into sections with enhanced processing"""
        logging.info("Loading requirements document...")
        if self.govstar_file.lower().endswith('.docx'):
            text = self.extract_text_from_docx(self.govstar_file)
        elif self.govstar_file.lower().endswith('.html'):
            text = self.extract_text_from_html(self.govstar_file)
        elif self.govstar_file.lower().endswith('.pdf'):
            text = self.extract_text_from_pdf(self.govstar_file)
        else:
            logging.error(f"Unsupported file format for requirements: {self.govstar_file}")
            return {}
        
        # Parse requirements into sections using advanced pattern recognition
        categories = []
        emergency_measures = []
        compliance_topics = []
        other_sections = []
        
        # Extract PA categories (A-G and Z)
        category_pattern = r"Category ([A-G]|Z)[\s\-–:]+([^\n]+)"
        category_matches = re.finditer(category_pattern, text)
        for match in category_matches:
            category_letter = match.group(1)
            category_name = match.group(2).strip()
            category_text = f"Category {category_letter}: {category_name}"
            categories.append(category_text)
            self.requirements[category_text] = category_text
        
        # Extract emergency measures
        emergency_pattern = r"Common Emergency Protective Measure[\s\-–:]+([^\n]+)"
        emergency_matches = re.finditer(emergency_pattern, text)
        for match in emergency_matches:
            measure_name = match.group(1).strip()
            emergency_text = f"Common Emergency Protective Measure: {measure_name}"
            emergency_measures.append(emergency_text)
            self.requirements[emergency_text] = emergency_text
        
        # Extract compliance topics from the key compliance topics dictionary
        for topic in self.key_compliance_topics.keys():
            compliance_topics.append(topic)
            self.requirements[topic] = topic
        
        # Process PA process steps (additional requirements)
        pa_steps = [
            "Case Management File", "Project Formulation", "Site Inspection Process",
            "Project Worksheet Preparation", "Damage Description and Dimensions",
            "Scope of Work", "Cost Estimates", "Direct Administrative Costs"
        ]
        
        for step in pa_steps:
            if step not in self.requirements:
                self.requirements[step] = step
                other_sections.append(step)
        
        # Log what we've found
        logging.info(f"Parsed {len(self.requirements)} requirement sections")
        logging.info(f"Categories found: {len(categories)}")
        logging.info(f"Emergency measures found: {len(emergency_measures)}")
        logging.info(f"Compliance topics: {len(compliance_topics)}")
        logging.info(f"Other sections: {len(other_sections)}")
        
        return self.requirements
    
    def match_requirements_to_policies(self):
        """Match requirements to policies with enhanced algorithms for specialized document types"""
        logging.info("Matching requirements to policy documents...")
        if not self.requirements:
            self.load_requirements()
        if not self.policies:
            self.load_and_classify_policies()
        
        matches = {}
        
        # For each requirement, find matching policies
        for req_name, req_text in self.requirements.items():
            logging.info(f"Processing requirement: {req_name}")
            req_matches = []
            
            # Check if this is a compliance topic
            is_compliance_topic = req_name in self.key_compliance_topics
            topic_keywords = self.key_compliance_topics.get(req_name, [])
            
            # Create a specialized search pattern for this requirement
            search_terms = []
            
            # For categories, focus on the category letter
            if req_name.startswith("Category"):
                category_letter = req_name.split(":")[0].replace("Category", "").strip()
                search_terms = [
                    f"Category {category_letter}", f"category {category_letter.lower()}", 
                    f"Category {category_letter}:", f"category {category_letter.lower()}:"
                ]
                
                # For Category Z (Management Costs), add specific terms
                if category_letter == "Z":
                    search_terms.extend([
                        "management cost", "management costs", "direct administrative cost", 
                        "indirect cost", "program administration", "administrative expense"
                    ])
            
            # For emergency measures, extract the key terms
            elif req_name.startswith("Common Emergency"):
                measure_name = req_name.split(":")[1].strip()
                search_terms = [measure_name]
                # Add individual words from the measure name
                search_terms.extend([word for word in measure_name.split() if len(word) > 3])
            
            # For compliance topics, use the predefined keywords
            elif is_compliance_topic:
                search_terms = topic_keywords
            
            # For other requirements, use the requirement name
            else:
                search_terms = [req_name]
                # Add individual words from the requirement name
                search_terms.extend([word for word in req_name.split() if len(word) > 3])
            
            # Match against policies
            for policy_name, policy_text in self.policies.items():
                # Calculate match score based on keyword frequency and importance
                score = 0
                
                # Check for each search term in the policy text
                for term in search_terms:
                    # Count occurrences
                    term_count = policy_text.lower().count(term.lower())
                    score += term_count * 0.01  # Base score for each occurrence
                
                # Boost score for priority policies
                if policy_name in self.priority_policies:
                    score *= 1.5
                    logging.debug(f"Applied priority policy boost to {policy_name}, new score: {score:.2f}")
                
                # Boost score for documents that match the specialized category
                doc_type = self.document_types.get(policy_name, "General Policy Document")
                
                # Apply specialized boosts based on requirement type and document type
                if req_name.startswith("Category") and "Category" in doc_type:
                    score *= 1.3
                    logging.debug(f"Applied category document boost to {policy_name}, new score: {score:.2f}")
                
                elif is_compliance_topic:
                    # Boost relevant document types for compliance topics
                    if req_name == "Cost Reasonableness" and doc_type in ["Cost Reasonableness", "Procurement & Contracting"]:
                        score *= 1.5
                        logging.debug(f"Applied cost reasonableness boost to {policy_name}, new score: {score:.2f}")
                    
                    elif req_name == "Costing Protocols" and doc_type in ["Form", "Guidance"]:
                        score *= 1.4
                        logging.debug(f"Applied costing protocols boost to {policy_name}, new score: {score:.2f}")
                    
                    elif req_name == "Consensus-Based Codes and Standards" and doc_type in ["Building Code", "Guidance"]:
                        score *= 1.6
                        logging.debug(f"Applied building code boost to {policy_name}, new score: {score:.2f}")
                    
                    elif req_name == "Management Costs (Category Z)" and "management" in policy_name.lower():
                        score *= 1.5
                        logging.debug(f"Applied management costs boost to {policy_name}, new score: {score:.2f}")
                
                # Boost for Excel files (forms, worksheets, etc.) which are important for process steps
                if (policy_name.lower().endswith('.xlsx') or policy_name.lower().endswith('.xls')) and doc_type == "Form":
                    if req_name in ["Cost Estimates", "Project Worksheet Preparation", "Site Inspection Process"]:
                        score *= 1.7
                        logging.debug(f"Applied Excel form boost to {policy_name}, new score: {score:.2f}")
                
                # Boost for guidance documents and job aids
                if doc_type in ["Guidance", "Job Aid", "Memo"]:
                    score *= 1.2
                    logging.debug(f"Applied guidance document boost to {policy_name}, new score: {score:.2f}")
                
                # Additional boosts for specific document types
                if "flood" in req_name.lower() and doc_type == "Flood Information":
                    score *= 1.5
                
                if "insurance" in req_name.lower() and doc_type == "Insurance":
                    score *= 1.5
                
                if "mitigation" in req_name.lower() and doc_type == "Mitigation":
                    score *= 1.5
                
                if "building code" in req_name.lower() and doc_type == "Building Code":
                    score *= 1.5
                
                if "environmental" in req_name.lower() and doc_type == "Environmental & Historic Preservation":
                    score *= 1.5
                
                # Only include policies with a minimum score
                if score > 0.05:
                    req_matches.append((policy_name, score))
            
            # Sort matches by score in descending order
            req_matches.sort(key=lambda x: x[1], reverse=True)
            
            # Limit to top matches if there are too many
            if len(req_matches) > 30:
                req_matches = req_matches[:30]
            
            matches[req_name] = req_matches
        
        self.matches = matches
        logging.info(f"Completed matching {len(self.requirements)} requirements to policies")
        return matches
    def generate_report(self, output_file=None):
        """Generate a comprehensive report of requirement-policy matches with enhanced visualization"""
        output_file = output_file or self.output_file
        logging.info("Generating enhanced report...")
        
        if not self.matches:
            self.match_requirements_to_policies()
        
        # Create DataFrame for report
        rows = []
        
        for req_name, matches in self.matches.items():
            for policy_name, score in matches:
                # Get document category
                doc_type = self.document_types.get(policy_name, "General Policy Document")
                
                rows.append({
                    'Requirement': req_name,
                    'Policy': policy_name,
                    'Match Score': f"{score:.2f}",
                    'Document Type': doc_type,
                    'File Type': os.path.splitext(policy_name)[1].lower().replace('.', '')
                })
        
        df = pd.DataFrame(rows)
        
        # Group requirement categories for the report
        pa_process_steps = [
            "Case Management File", "Project Formulation", "Site Inspection Process",
            "Project Worksheet Preparation", "Damage Description and Dimensions",
            "Scope of Work", "Cost Estimates", "Direct Administrative Costs"
        ]
        
        categories = [req for req in self.matches.keys() if req.startswith("Category")]
        emergency_measures = [req for req in self.matches.keys() if req.startswith("Common Emergency")]
        compliance_topics = [req for req in self.matches.keys() if req in self.key_compliance_topics]
        process_steps = [req for req in self.matches.keys() if req in pa_process_steps]
        other_reqs = [req for req in self.matches.keys() if req not in categories + emergency_measures + compliance_topics + process_steps]
        
        # Generate HTML report with enhanced styling and organization
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>ComplianceMax Enhanced Policy-Requirement Matching Report</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    margin: 20px; 
                    color: #333;
                    line-height: 1.6;
                }
                h1 { color: #003366; margin-top: 20px; }
                h2 { 
                    color: #0066cc; 
                    margin-top: 30px; 
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ddd;
                }
                h3 { 
                    color: #0099cc; 
                    margin-top: 20px; 
                    padding: 5px 0;
                }
                h4 { 
                    color: #339966; 
                    margin-top: 15px; 
                }
                .section-intro {
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-left: 5px solid #0066cc;
                    margin-bottom: 20px;
                }
                table { 
                    border-collapse: collapse; 
                    width: 100%; 
                    margin-top: 20px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }
                th, td { 
                    padding: 12px 8px; 
                    text-align: left; 
                    border-bottom: 1px solid #ddd; 
                }
                th { 
                    background-color: #f2f2f2; 
                    color: #333; 
                    position: sticky;
                    top: 0;
                }
                tr:hover {background-color: #f5f5f5;}
                .score { font-weight: bold; }
                .high { color: green; }
                .medium { color: orange; }
                .low { color: red; }
                .category-group { 
                    margin-bottom: 40px; 
                    background-color: #fff;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 15px rgba(0,0,0,0.05);
                }
                .compliance-topic { 
                    background-color: #f9f9f9; 
                    padding: 15px; 
                    border-left: 5px solid #0066cc; 
                    margin-bottom: 20px; 
                }
                .process-step { 
                    background-color: #edf7ed; 
                    padding: 15px; 
                    border-left: 5px solid #339966; 
                    margin-bottom: 20px; 
                }
                .document-category { 
                    margin-top: 15px; 
                    border-bottom: 1px solid #ddd; 
                    padding-bottom: 5px; 
                }
                .file-type-badge {
                    display: inline-block;
                    padding: 3px 8px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    margin-left: 5px;
                    color: white;
                }
                .file-type-pdf { background-color: #d9534f; }
                .file-type-docx { background-color: #337ab7; }
                .file-type-xlsx { background-color: #5cb85c; }
                .file-type-html { background-color: #f0ad4e; }
                .stats-container {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 20px;
                    margin-bottom: 30px;
                }
                .stat-box {
                    flex: 1;
                    min-width: 200px;
                    background-color: #f9f9f9;
                    padding: 15px;
                    border-radius: 5px;
                    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
                    text-align: center;
                }
                .stat-number {
                    font-size: 24px;
                    font-weight: bold;
                    color: #0066cc;
                    margin: 10px 0;
                }
                .stat-label {
                    font-size: 14px;
                    color: #666;
                }
                .date {
                    color: #666;
                    font-style: italic;
                }
                .navbar {
                    position: sticky;
                    top: 0;
                    background-color: #003366;
                    padding: 15px;
                    color: white;
                    z-index: 1000;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .navbar a {
                    color: white;
                    text-decoration: none;
                    margin: 0 10px;
                    padding: 5px;
                }
                .navbar a:hover {
                    text-decoration: underline;
                }
                .expandable-section {
                    margin-bottom: 10px;
                }
                .expandable-header {
                    background-color: #f2f2f2;
                    padding: 10px;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .expandable-content {
                    display: none;
                    padding: 10px;
                    border: 1px solid #ddd;
                }
                #topBtn {
                    display: none;
                    position: fixed;
                    bottom: 20px;
                    right: 30px;
                    z-index: 99;
                    border: none;
                    outline: none;
                    background-color: #0066cc;
                    color: white;
                    cursor: pointer;
                    padding: 15px;
                    border-radius: 50%;
                    font-size: 18px;
                }
                #topBtn:hover {
                    background-color: #003366;
                }
                .category-nav {
                    background-color: #f9f9f9;
                    padding: 10px;
                    margin-bottom: 20px;
                    border-radius: 5px;
                    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
                }
                .category-nav a {
                    display: inline-block;
                    margin: 5px;
                    padding: 5px 10px;
                    background-color: #0066cc;
                    color: white;
                    text-decoration: none;
                    border-radius: 3px;
                }
                .category-nav a:hover {
                    background-color: #003366;
                }
            </style>
        </head>
        <body>
            <div class="navbar">
                <h1 style="margin: 0;">ComplianceMax Enhanced Policy Matcher</h1>
                <div>
                    <a href="#overview">Overview</a>
                    <a href="#process-steps">Process Steps</a>
                    <a href="#categories">PA Categories</a>
                    <a href="#emergency">Emergency Measures</a>
                    <a href="#compliance">Compliance Topics</a>
                    <a href="#summary">Summary</a>
                </div>
            </div>
            
            <h1 id="overview">Enhanced Policy-Requirement Matching Report</h1>
            <p class="date">Generated on: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
            
            <div class="section-intro">
                <p>This enhanced report identifies how policy documents, forms, and guidance materials apply to FEMA Public Assistance requirements, 
                including all PA categories (A-G and Z), critical compliance topics, process steps, and specialized topics like 
                Building Codes, Flood Information, Insurance, and Mitigation.</p>
            </div>
            
            <div class="stats-container">
                <div class="stat-box">
                    <div class="stat-label">Policy Documents</div>
                    <div class="stat-number">""" + str(len(self.policies)) + """</div>
                    <div class="stat-label">Analyzed</div>
                </div>
                <div class="stat-box">
                    <div class="stat-label">Requirements</div>
                    <div class="stat-number">""" + str(len(self.requirements)) + """</div>
                    <div class="stat-label">Matched</div>
                </div>
                <div class="stat-box">
                    <div class="stat-label">Document Types</div>
                    <div class="stat-number">""" + str(len(set(self.document_types.values()))) + """</div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-box">
                    <div class="stat-label">Compliance Topics</div>
                    <div class="stat-number">""" + str(len(self.key_compliance_topics)) + """</div>
                    <div class="stat-label">Covered</div>
                </div>
            </div>
        """
        
        # Add category navigation section
        html += """
            <div class="category-nav">
                <strong>Jump to Document Category:</strong><br>
        """
        
        # Get unique document categories
        unique_doc_types = sorted(set(self.document_types.values()))
        for doc_type in unique_doc_types:
            html += f'<a href="#cat-{doc_type.replace(" ", "-").lower()}">{doc_type}</a> '
        
        html += "</div>"
        
        # PA Process Steps section (given high priority)
        if process_steps:
            html += """
                <h2 id="process-steps">Public Assistance Process Steps</h2>
                <div class="category-group">
                    <p>These critical process steps form the core of effective Public Assistance program implementation.</p>
            """
            
            for step in process_steps:
                html += f'<div class="process-step"><h3>{step}</h3>'
                
                # Group policies by document type
                step_df = df[df['Requirement'] == step].copy()
                step_df = step_df.sort_values(['Document Type', 'Match Score'], ascending=[True, False])
                
                for doc_type, type_df in step_df.groupby('Document Type'):
                    if not type_df.empty:
                        html += f'<div class="document-category"><h4>{doc_type}</h4>'
                        html += "<table>"
                        html += "<tr><th>Policy Document</th><th>Relevance Score</th><th>File Type</th></tr>"
                        
                        for _, row in type_df.iterrows():
                            score = float(row['Match Score'])
                            score_class = "high" if score > 0.3 else "medium" if score > 0.15 else "low"
                            file_type = row['File Type'].lower()
                            file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
                            
                            html += f"""<tr>
                                <td>{row['Policy']}</td>
                                <td class='score {score_class}'>{row['Match Score']}</td>
                                <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
                            </tr>"""
                        
                        html += "</table></div>"
                
                html += "</div>"
            
            html += "</div>"
        
        # Categories (A-G and Z)
        html += """
            <h2 id="categories">PA Categories</h2>
            <div class="category-group">
                <p>FEMA Public Assistance categorizes work into Emergency Work (Categories A-B) and Permanent Work (Categories C-G), plus Category Z for Management Costs.</p>
        """
        
        for category in sorted(categories):
            html += f"<h3>{category}</h3>"
            
            # Group policies by document type
            cat_df = df[df['Requirement'] == category].copy()
            cat_df = cat_df.sort_values(['Document Type', 'Match Score'], ascending=[True, False])
            
            for doc_type, type_df in cat_df.groupby('Document Type'):
                if not type_df.empty:
                    html += f'<div class="document-category"><h4>{doc_type}</h4>'
                    html += "<table>"
                    html += "<tr><th>Policy Document</th><th>Relevance Score</th><th>File Type</th></tr>"
                    
                    for _, row in type_df.iterrows():
                        score = float(row['Match Score'])
                        score_class = "high" if score > 0.3 else "medium" if score > 0.15 else "low"
                        file_type = row['File Type'].lower()
                        file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
                        
                        html += f"""<tr>
                            <td>{row['Policy']}</td>
                            <td class='score {score_class}'>{row['Match Score']}</td>
                            <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
                        </tr>"""
                    
                    html += "</table></div>"
        
        html += "</div>"
        
        # Emergency Protective Measures
        if emergency_measures:
            html += """
                <h2 id="emergency">Emergency Protective Measures</h2>
                <div class="category-group">
                    <p>Common Emergency Protective Measures are activities undertaken by a community before, during, and after a disaster to eliminate or reduce immediate threats to life, public health, or safety.</p>
            """
            
            for measure in sorted(emergency_measures):
                html += f"<h3>{measure}</h3>"
                
                # Group policies by document type
                measure_df = df[df['Requirement'] == measure].copy()
                measure_df = measure_df.sort_values(['Document Type', 'Match Score'], ascending=[True, False])
                
                for doc_type, type_df in measure_df.groupby('Document Type'):
                    if not type_df.empty:
                        html += f'<div class="document-category"><h4>{doc_type}</h4>'
                        html += "<table>"
                        html += "<tr><th>Policy Document</th><th>Relevance Score</th><th>File Type</th></tr>"
                        
                        for _, row in type_df.iterrows():
                            score = float(row['Match Score'])
                            score_class = "high" if score > 0.3 else "medium" if score > 0.15 else "low"
                            file_type = row['File Type'].lower()
                            file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
                            
                            html += f"""<tr>
                                <td>{row['Policy']}</td>
                                <td class='score {score_class}'>{row['Match Score']}</td>
                                <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
                            </tr>"""
                        
                        html += "</table></div>"
            
            html += "</div>"
        
        # Compliance Topics
        if compliance_topics:
            html += """
                <h2 id="compliance">Critical Compliance Topics</h2>
                <div class="category-group">
                    <p>These key compliance areas are critical for ensuring proper documentation, cost controls, adherence to required codes and standards, and other compliance requirements.</p>
            """
            
            for topic in compliance_topics:
                html += f'<div class="compliance-topic"><h3>{topic}</h3>'
                
                # Group policies by document type
                topic_df = df[df['Requirement'] == topic].copy()
                topic_df = topic_df.sort_values(['Document Type', 'Match Score'], ascending=[True, False])
                
                for doc_type, type_df in topic_df.groupby('Document Type'):
                    if not type_df.empty:
                        html += f'<div class="document-category"><h4>{doc_type}</h4>'
                        html += "<table>"
                        html += "<tr><th>Policy Document</th><th>Relevance Score</th><th>File Type</th></tr>"
                        
                        for _, row in type_df.iterrows():
                            score = float(row['Match Score'])
                            score_class = "high" if score > 0.3 else "medium" if score > 0.15 else "low"
                            file_type = row['File Type'].lower()
                            file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
                            
                            html += f"""<tr>
                                <td>{row['Policy']}</td>
                                <td class='score {score_class}'>{row['Match Score']}</td>
                                <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
                            </tr>"""
                        
                        html += "</table></div>"
                
                html += "</div>"
            
            html += "</div>"
        
        # Other Requirements
        if other_reqs:
            html += """
                <h2>Other Requirements</h2>
                <div class="category-group">
            """
            
            for req in sorted(other_reqs):
                html += f"<h3>{req}</h3>"
                
                # Group policies by document type
                req_df = df[df['Requirement'] == req].copy()
                req_df = req_df.sort_values(['Document Type', 'Match Score'], ascending=[True, False])
                
                for doc_type, type_df in req_df.groupby('Document Type'):
                    if not type_df.empty:
                        html += f'<div class="document-category"><h4>{doc_type}</h4>'
                        html += "<table>"
                        html += "<tr><th>Policy Document</th><th>Relevance Score</th><th>File Type</th></tr>"
                        
                        for _, row in type_df.iterrows():
                            score = float(row['Match Score'])
                            score_class = "high" if score > 0.3 else "medium" if score > 0.15 else "low"
                            file_type = row['File Type'].lower()
                            file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
                            
                            html += f"""<tr>
                                <td>{row['Policy']}</td>
                                <td class='score {score_class}'>{row['Match Score']}</td>
                                <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
                            </tr>"""
                        
                        html += "</table></div>"
            
            html += "</div>"
        
        # Document categories summary
        html += """
            <h2 id="summary">Summary by Document Category</h2>
            <p>This summary shows how policy documents from different categories apply to your requirements:</p>
        """
        
        # For each document type, create an anchor and summary section
        for doc_type in sorted(set(self.document_types.values())):
            doc_anchor = f"cat-{doc_type.replace(' ', '-').lower()}"
            doc_count = list(self.document_types.values()).count(doc_type)
            
            html += f'<div class="expandable-section" id="{doc_anchor}">'
            html += f'<div class="expandable-header"><h3>{doc_type} ({doc_count} documents)</h3><span>Click to expand/collapse</span></div>'
            html += f'<div class="expandable-content">'
            
            # Get documents of this type
            doc_files = [fname for fname, dtype in self.document_types.items() if dtype == doc_type]
            doc_files.sort()
            
            # Count policy references by document
            doc_df = df[df['Document Type'] == doc_type].copy()
            policy_counts = doc_df.groupby('Policy')['Requirement'].count().reset_index()
            policy_counts.columns = ['Policy', 'Reference Count']
            policy_counts = policy_counts.sort_values('Reference Count', ascending=False)
            
            # Show table of top documents in this category
            html += "<table>"
            html += "<tr><th>Policy Document</th><th>References in Requirements</th><th>File Type</th></tr>"
            
            for _, row in policy_counts.iterrows():
                policy = row['Policy']
                count = row['Reference Count']
                file_type = os.path.splitext(policy)[1].lower().replace('.', '')
                file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
                
                html += f"""<tr>
                    <td>{policy}</td>
                    <td>{count}</td>
                    <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
                </tr>"""
            
            html += "</table>"
            html += "</div></div>"
        
        # Add policy summary section
        html += """
            <h2>Summary of Top Policy Documents</h2>
            <p>These policy documents match the most requirements and may require special attention:</p>
        """
        
        # Count policy references
        policy_counter = defaultdict(int)
        for req_name, matches in self.matches.items():
            for policy_name, _ in matches:
                policy_counter[policy_name] += 1
        
        html += "<table>"
        html += "<tr><th>Policy Document</th><th>Referenced in # Requirements</th><th>Document Type</th><th>File Type</th></tr>"
        
        # Sort policies by count and show top 15
        sorted_policies = sorted(policy_counter.items(), key=lambda x: x[1], reverse=True)[:15]
        for policy_name, count in sorted_policies:
            doc_type = self.document_types.get(policy_name, "General Policy Document")
            file_type = os.path.splitext(policy_name)[1].lower().replace('.', '')
            file_type_class = f"file-type-{file_type}" if file_type in ['pdf', 'docx', 'xlsx', 'html'] else ""
            
            html += f"""<tr>
                <td>{policy_name}</td>
                <td>{count}</td>
                <td>{doc_type}</td>
                <td><span class='file-type-badge {file_type_class}'>{file_type}</span></td>
            </tr>"""
        
        html += "</table>"
        
        # Add JavaScript for interaction
        html += """
            <button id="topBtn" title="Go to top">↑</button>
            
            <script>
                // Add functionality to expandable sections
                document.querySelectorAll('.expandable-header').forEach(function(header) {
                    header.addEventListener('click', function() {
                        const content = this.nextElementSibling;
                        if (content.style.display === "block") {
                            content.style.display = "none";
                        } else {
                            content.style.display = "block";
                        }
                    });
                });
                
                // Get the button
                let mybutton = document.getElementById("topBtn");
                
                // When the user scrolls down 20px from the top of the document, show the button
                window.onscroll = function() {scrollFunction()};
                
                function scrollFunction() {
                  if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    mybutton.style.display = "block";
                  } else {
                    mybutton.style.display = "none";
                  }
                }
                
                // When the user clicks on the button, scroll to the top of the document
                mybutton.addEventListener('click', function() {
                    document.body.scrollTop = 0; // For Safari
                    document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
                });
            </script>
        """
        
        html += "</body></html>"
        
        # Write the HTML report
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        # Also generate a CSV export for further analysis
        csv_output = output_file.replace('.html', '.csv')
        df.to_csv(csv_output, index=False)
        
        logging.info(f"Report generated at {output_file}")
        logging.info(f"CSV data exported to {csv_output}")
        print(f"Enhanced report generated successfully at: {output_file}")
        print(f"CSV data exported to: {csv_output}")
        
        return output_file
# Main execution
if __name__ == "__main__":
    govstar_file = r"C:\Users\<USER>\CascadeProjects\compliancemax_clean_new\GOVSTAR-SUMMARY W LINKS.docx"
    policies_dir = r"C:\Users\<USER>\CascadeProjects\compliancemax_clean_new\APRIL 1 2025-POLICIES"
    output_file = r"C:\Users\<USER>\CascadeProjects\compliancemax_clean_new\enhanced_policy_requirements_report.html"
    
    # Enable more detailed logging for troubleshooting
    logging.getLogger().setLevel(logging.INFO)
    
    print("Enhanced Policy Matcher - Starting processing...")
    print(f"Processing policies from: {policies_dir}")
    print(f"Using requirements from: {govstar_file}")
    print("This may take several minutes depending on the number of documents...")
    
    # Create and run the enhanced matcher
    matcher = EnhancedPolicyMatcher(govstar_file, policies_dir, output_file)
    matcher.generate_report(output_file)
    
    print("\nProcessing Statistics:")
    print(f"Total files processed: {len(matcher.processed_files)}")
    print(f"Files with errors: {len(matcher.error_files)}")
    print(f"Files skipped: {len(matcher.skipped_files)}")
    
    # Report on document types found
    doc_type_counts = defaultdict(int)
    for _, doc_type in matcher.document_types.items():
        doc_type_counts[doc_type] += 1
    
    print("\nDocument Types Found:")
    for doc_type, count in sorted(doc_type_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {doc_type}: {count} documents")
    
    print(f"\nReport generated successfully at: {output_file}")
    print(f"CSV data exported to: {output_file.replace('.html', '.csv')}")
    print("Document processing summary available in: document_processing_summary.txt")
    
    # Prepare for future API integration
    print("\nSetup for API Integration:")
    print("This enhanced policy matcher is designed for future integration with XaI API for real-time information retrieval.")
    print("The document classification and matching algorithm can be extended to work with API responses.")

const express = require('express');
const app = express();
const port = 5000;

// Serve static files
app.use(express.static('public'));

// Home page
app.get('/', (req, res) => {
  res.send(renderPage('Home', `
    <h1>ComplianceMax</h1>
    <p>Simplify Your FEMA Public Assistance Compliance</p>
    
    <div class="card">
      <h2>Get Started</h2>
      <p>Manage your FEMA Public Assistance projects with ease.</p>
      <div class="button-group">
        <a href="/dashboard" class="primary-button">Dashboard</a>
        <a href="/projects" class="secondary-button">View Projects</a>
      </div>
    </div>
  `));
});

// Dashboard page
app.get('/dashboard', (req, res) => {
  res.send(renderPage('Dashboard', `
    <h1>Dashboard</h1>
    <p>Monitor your compliance status</p>
    
    <div class="grid">
      <div class="card">
        <h3>Approved</h3>
        <div class="stat">42</div>
      </div>
      <div class="card">
        <h3>Pending</h3>
        <div class="stat">15</div>
      </div>
      <div class="card">
        <h3>Rejected</h3>
        <div class="stat">8</div>
      </div>
      <div class="card">
        <h3>Total</h3>
        <div class="stat">65</div>
      </div>
    </div>
  `));
});

// Projects page
app.get('/projects', (req, res) => {
  res.send(renderPage('Projects', `
    <h1>Projects</h1>
    <p>Manage your Public Assistance projects</p>
    
    <div class="grid">
      <div class="card">
        <h3>Hospital Repairs</h3>
        <p>Category E - Buildings</p>
        <div class="progress">
          <div class="progress-bar" style="width: 75%"></div>
        </div>
        <div style="text-align: right; margin-top: 5px;">75%</div>
      </div>
      <div class="card">
        <h3>Emergency Services</h3>
        <p>Category B - Emergency Work</p>
        <div class="progress">
          <div class="progress-bar" style="width: 90%"></div>
        </div>
        <div style="text-align: right; margin-top: 5px;">90%</div>
      </div>
      <div class="card">
        <h3>Road Reconstruction</h3>
        <p>Category C - Roads</p>
        <div class="progress">
          <div class="progress-bar" style="width: 45%"></div>
        </div>
        <div style="text-align: right; margin-top: 5px;">45%</div>
      </div>
    </div>
  `));
});

// Compliance Wizard page
app.get('/compliance-wizard', (req, res) => {
  res.redirect('/compliance-wizard/step1');
});

// Compliance Wizard - Step 1
app.get('/compliance-wizard/step1', (req, res) => {
  res.send(renderPage('Compliance Wizard - Step 1', `
    <h1>Compliance Wizard</h1>
    <p>Step-by-step guidance for FEMA compliance</p>
    
    <div class="card">
      <h3>Project Type Selection</h3>
      <div class="step-tracker">
        <div class="step active">Step 1</div>
        <div class="step">Step 2</div>
        <div class="step">Step 3</div>
        <div class="step">Step 4</div>
      </div>
      
      <div style="margin-top: 20px;">
        <form action="/compliance-wizard/step2" method="get">
          <div>
            <label for="projectType">Project Type</label>
            <select id="projectType" name="projectType" style="width: 100%; background-color: #141b2d; border: 1px solid #2e3651; color: white; padding: 8px; border-radius: 4px;">
              <option>Emergency Protective Measures</option>
              <option>Debris Removal</option>
              <option>Permanent Work</option>
              <option>Hazard Mitigation</option>
            </select>
          </div>
          
          <div style="margin-top: 15px;">
            <label for="damageType">Damage Type</label>
            <select id="damageType" name="damageType" style="width: 100%; background-color: #141b2d; border: 1px solid #2e3651; color: white; padding: 8px; border-radius: 4px;">
              <option>Flood</option>
              <option>Wind</option>
              <option>Fire</option>
              <option>Earthquake</option>
              <option>Other</option>
            </select>
          </div>
          
          <div class="button-group">
            <a href="/" class="secondary-button">Cancel</a>
            <button type="submit" class="primary-button">Next</button>
          </div>
        </form>
      </div>
    </div>
  `));
});

// Compliance Wizard - Step 2
app.get('/compliance-wizard/step2', (req, res) => {
  // Get query params from step 1 (would store in a real application)
  const projectType = req.query.projectType || 'Not selected';
  const damageType = req.query.damageType || 'Not selected';
  
  res.send(renderPage('Compliance Wizard - Step 2', `
    <h1>Compliance Wizard</h1>
    <p>Step-by-step guidance for FEMA compliance</p>
    
    <div class="breadcrumb">
      Selected: ${projectType} / ${damageType}
    </div>
    
    <div class="card">
      <h3>Project Information</h3>
      <div class="step-tracker">
        <div class="step completed">Step 1</div>
        <div class="step active">Step 2</div>
        <div class="step">Step 3</div>
        <div class="step">Step 4</div>
      </div>
      
      <div style="margin-top: 20px;">
        <form action="/compliance-wizard/step3" method="get">
          <input type="hidden" name="projectType" value="${projectType}">
          <input type="hidden" name="damageType" value="${damageType}">
          
          <div>
            <label for="projectName">Project Name</label>
            <input type="text" id="projectName" name="projectName" style="width: 100%">
          </div>
          
          <div style="margin-top: 15px;">
            <label for="category">Project Category</label>
            <select id="category" name="category" style="width: 100%; background-color: #141b2d; border: 1px solid #2e3651; color: white; padding: 8px; border-radius: 4px;">
              <option>Category A - Debris Removal</option>
              <option>Category B - Emergency Work</option>
              <option>Category C - Roads and Bridges</option>
              <option>Category D - Water Control</option>
              <option>Category E - Buildings and Equipment</option>
              <option>Category F - Utilities</option>
              <option>Category G - Parks and Recreation</option>
            </select>
          </div>
          
          <div class="button-group">
            <a href="/compliance-wizard/step1" class="secondary-button">Previous</a>
            <button type="submit" class="primary-button">Next</button>
          </div>
        </form>
      </div>
    </div>
  `));
});

// Compliance Wizard - Step 3
app.get('/compliance-wizard/step3', (req, res) => {
  // Get query params from previous steps
  const projectType = req.query.projectType || 'Not selected';
  const damageType = req.query.damageType || 'Not selected';
  const projectName = req.query.projectName || 'Not named';
  const category = req.query.category || 'Not categorized';
  
  res.send(renderPage('Compliance Wizard - Step 3', `
    <h1>Compliance Wizard</h1>
    <p>Step-by-step guidance for FEMA compliance</p>
    
    <div class="breadcrumb">
      Selected: ${projectType} / ${damageType} / ${projectName} / ${category}
    </div>
    
    <div class="card">
      <h3>Document Requirements</h3>
      <div class="step-tracker">
        <div class="step completed">Step 1</div>
        <div class="step completed">Step 2</div>
        <div class="step active">Step 3</div>
        <div class="step">Step 4</div>
      </div>
      
      <div style="margin-top: 20px;">
        <form action="/compliance-wizard/step4" method="get">
          <input type="hidden" name="projectType" value="${projectType}">
          <input type="hidden" name="damageType" value="${damageType}">
          <input type="hidden" name="projectName" value="${projectName}">
          <input type="hidden" name="category" value="${category}">
          
          <p>Based on your selections, you'll need the following documents:</p>
          
          <div style="margin-top: 15px;">
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <input type="checkbox" id="doc1" name="documents" value="Damage inventory" style="margin-right: 10px;">
              <label for="doc1" style="margin: 0;">Damage inventory</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <input type="checkbox" id="doc2" name="documents" value="Project cost estimate" style="margin-right: 10px;">
              <label for="doc2" style="margin: 0;">Project cost estimate</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <input type="checkbox" id="doc3" name="documents" value="Procurement documentation" style="margin-right: 10px;">
              <label for="doc3" style="margin: 0;">Procurement documentation</label>
            </div>
            
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <input type="checkbox" id="doc4" name="documents" value="Photographs" style="margin-right: 10px;">
              <label for="doc4" style="margin: 0;">Photographs</label>
            </div>
          </div>
          
          <div class="button-group">
            <a href="/compliance-wizard/step2" class="secondary-button">Previous</a>
            <button type="submit" class="primary-button">Next</button>
          </div>
        </form>
      </div>
    </div>
  `));
});

// Compliance Wizard - Step 4 (Final)
app.get('/compliance-wizard/step4', (req, res) => {
  // Get query params from previous steps
  const projectName = req.query.projectName || 'Not named';
  const category = req.query.category || 'Not categorized';
  const documents = req.query.documents || [];
  
  // Create a formatted document list
  let documentList = '';
  if (Array.isArray(documents)) {
    documentList = documents.map(doc => `<li>${doc}</li>`).join('');
  } else if (documents) {
    documentList = `<li>${documents}</li>`;
  }
  
  res.send(renderPage('Compliance Wizard - Complete', `
    <h1>Compliance Wizard</h1>
    <p>Step-by-step guidance for FEMA compliance</p>
    
    <div class="step-tracker">
      <div class="step completed">Step 1</div>
      <div class="step completed">Step 2</div>
      <div class="step completed">Step 3</div>
      <div class="step completed">Step 4</div>
    </div>
    
    <div class="card">
      <h3>Compliance Summary</h3>
      
      <div style="margin-top: 20px;">
        <p><strong>Project Name:</strong> ${projectName}</p>
        <p><strong>Category:</strong> ${category}</p>
        
        <div style="margin-top: 15px;">
          <p><strong>Required Documents:</strong></p>
          <ul>
            ${documentList}
          </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background-color: rgba(62, 67, 150, 0.2); border-radius: 8px;">
          <p><strong>Your project has been created successfully!</strong></p>
          <p>You will receive updates on your compliance status as your project progresses.</p>
        </div>
        
        <div class="button-group">
          <a href="/compliance-wizard/step3" class="secondary-button">Previous</a>
          <a href="/projects" class="primary-button">View All Projects</a>
        </div>
      </div>
    </div>
  `));
});

// Documents page
app.get('/documents', (req, res) => {
  res.send(renderPage('Documents', `
    <h1>Documents</h1>
    <p>Manage your compliance documentation</p>
    
    <div class="card">
      <div class="document-item">
        <div class="document-icon">📄</div>
        <div>
          <h3>Project Worksheet.pdf</h3>
          <p>Uploaded 3 days ago</p>
        </div>
      </div>
      <div class="document-item">
        <div class="document-icon">📄</div>
        <div>
          <h3>FEMA Form 90-91.pdf</h3>
          <p>Uploaded 1 week ago</p>
        </div>
      </div>
      <div class="document-item">
        <div class="document-icon">📄</div>
        <div>
          <h3>Damage Assessment Report.pdf</h3>
          <p>Uploaded 2 weeks ago</p>
        </div>
      </div>
      
      <div style="margin-top: 20px;">
        <button class="primary-button">Upload Document</button>
      </div>
    </div>
  `));
});

// Settings page
app.get('/settings', (req, res) => {
  res.send(renderPage('Settings', `
    <h1>Settings</h1>
    <p>Configure your account settings</p>
    
    <div class="card">
      <h3>Profile Information</h3>
      <div style="margin-top: 15px;">
        <div>
          <label for="name">Name</label>
          <input type="text" id="name" value="John Smith" style="width: 100%">
        </div>
        <div style="margin-top: 15px;">
          <label for="email">Email</label>
          <input type="email" id="email" value="<EMAIL>" style="width: 100%">
        </div>
        <div style="margin-top: 15px;">
          <label for="organization">Organization</label>
          <input type="text" id="organization" value="County Emergency Management" style="width: 100%">
        </div>
        
        <div style="margin-top: 20px;">
          <button class="primary-button">Save Changes</button>
        </div>
      </div>
    </div>
  `));
});

// Helper function to render the full page with layout
function renderPage(title, content) {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>ComplianceMax - ${title}</title>
        <style>
          body {
            background-color: #141b2d;
            color: white;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
          }
          
          nav {
            width: 220px;
            background-color: #1a2332;
            height: 100vh;
            position: fixed;
            padding: 20px;
          }
          
          nav h2 {
            margin-bottom: 20px;
          }
          
          nav ul {
            list-style: none;
            padding: 0;
          }
          
          nav li {
            margin-bottom: 10px;
          }
          
          nav a {
            color: white;
            text-decoration: none;
            display: block;
            padding: 8px;
            border-radius: 4px;
          }
          
          nav a:hover {
            background-color: #2e3651;
          }
          
          main {
            margin-left: 260px;
            padding: 20px;
            flex: 1;
          }
          
          .card {
            background-color: #1f2940;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #2e3651;
          }
          
          .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
          }
          
          .stat {
            font-size: 32px;
            font-weight: bold;
            margin-top: 10px;
          }
          
          .progress {
            height: 10px;
            background-color: #141b2d;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
          }
          
          .progress-bar {
            height: 100%;
            background-color: #3e4396;
          }
          
          button, .primary-button, .secondary-button {
            background-color: #1f2940;
            color: white;
            border: 1px solid #2e3651;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
          }
          
          button:hover, .secondary-button:hover {
            background-color: #2e3651;
          }
          
          .primary-button {
            background-color: #3e4396;
            border: none;
          }
          
          .primary-button:hover {
            background-color: #2e325b;
          }
          
          .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
          }
          
          input[type="text"], input[type="email"] {
            background-color: #141b2d;
            border: 1px solid #2e3651;
            color: white;
            padding: 8px;
            border-radius: 4px;
          }
          
          label {
            display: block;
            margin-bottom: 5px;
            color: #aaa;
          }
          
          .document-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #2e3651;
          }
          
          .document-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(66, 99, 235, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            margin-right: 15px;
          }
          
          .step-tracker {
            display: flex;
            margin-top: 20px;
          }
          
          .step {
            flex: 1;
            padding: 10px;
            background-color: #141b2d;
            margin-right: 5px;
            border-radius: 4px;
            font-size: 14px;
            position: relative;
          }
          
          .step.completed {
            background-color: #3e4396;
          }
          
          .step.active {
            border: 1px solid #3e4396;
          }
        </style>
      </head>
      <body>
        <nav>
          <h2>ComplianceMax</h2>
          <ul>
            <li><a href="/">Home</a></li>
            <li><a href="/dashboard">Dashboard</a></li>
            <li><a href="/projects">Projects</a></li>
            <li><a href="/compliance-wizard">Compliance Wizard</a></li>
            <li><a href="/documents">Documents</a></li>
            <li><a href="/settings">Settings</a></li>
          </ul>
        </nav>
        <main>${content}</main>
      </body>
    </html>
  `;
}

app.listen(port, () => {
  console.log(`ComplianceMax running at http://localhost:${port}`);
}); 
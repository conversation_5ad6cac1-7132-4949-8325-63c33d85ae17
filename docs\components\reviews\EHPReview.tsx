import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Chip,
  Box,
  IconButton,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { DocumentProcessor } from '../documents/DocumentProcessor';
import { ProcessedDocument, DocumentType } from '../../types/document';
import { documentService } from '../../services/document';
import { useReview } from '../../hooks/useReview';

interface EHPReviewProps {
  projectId: number;
}

interface Impact {
  category: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
}

interface Consideration {
  type: string;
  description: string;
  requirements: string[];
}

interface EnvironmentalImpact {
  category: string;
  description: string;
  severity: string;
  mitigation: string;
  relatedDocument?: string;
}

export const EHPReview: React.FC<EHPReviewProps> = ({ projectId }) => {
  const { createReview, reviews, loading } = useReview(projectId);
  const [formData, setFormData] = useState({
    impact: {
      category: '',
      description: '',
      severity: 'low' as const
    },
    impacts: [] as Impact[],
    consideration: {
      type: '',
      description: '',
      requirements: [] as string[]
    },
    considerations: [] as Consideration[],
    permit: '',
    permits: [] as string[]
  });

  const [impacts, setImpacts] = useState<EnvironmentalImpact[]>([]);
  const [processedDocuments, setProcessedDocuments] = useState<ProcessedDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<ProcessedDocument | null>(null);
  const [documentText, setDocumentText] = useState<string>('');
  const [loadingDocument, setLoadingDocument] = useState(false);

  useEffect(() => {
    loadProcessedDocuments();
  }, [projectId]);

  const loadProcessedDocuments = async () => {
    try {
      const docs = await documentService.getProcessedDocuments();
      setProcessedDocuments(docs);
    } catch (error) {
      console.error('Failed to load processed documents:', error);
    }
  };

  const handleDocumentProcessed = (document: ProcessedDocument) => {
    setProcessedDocuments(prev => [...prev, document]);
  };

  const handleDocumentSelect = async (documentId: string) => {
    setLoadingDocument(true);
    try {
      const doc = processedDocuments.find(d => d.id === documentId);
      setSelectedDocument(doc || null);
      
      if (doc) {
        const text = await documentService.getDocumentText(documentId);
        setDocumentText(text);
      }
    } catch (error) {
      console.error('Failed to load document text:', error);
    } finally {
      setLoadingDocument(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createReview('ehp', {
        environmental_impact: { impacts: formData.impacts },
        historical_considerations: { considerations: formData.considerations },
        required_permits: formData.permits
      });
      // Reset form
      setFormData({
        impact: { category: '', description: '', severity: 'low' },
        impacts: [],
        consideration: { type: '', description: '', requirements: [] },
        considerations: [],
        permit: '',
        permits: []
      });
    } catch (error) {
      console.error('Error creating EHP review:', error);
    }
  };

  const addImpact = () => {
    if (formData.impact.category && formData.impact.description) {
      setFormData({
        ...formData,
        impacts: [...formData.impacts, formData.impact],
        impact: { category: '', description: '', severity: 'low' }
      });
    }
  };

  const addConsideration = () => {
    if (formData.consideration.type && formData.consideration.description) {
      setFormData({
        ...formData,
        considerations: [...formData.considerations, formData.consideration],
        consideration: { type: '', description: '', requirements: [] }
      });
    }
  };

  const addPermit = () => {
    if (formData.permit.trim()) {
      setFormData({
        ...formData,
        permits: [...formData.permits, formData.permit.trim()],
        permit: ''
      });
    }
  };

  const ehpReviews = reviews.filter(review => review.type === 'ehp');

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Document Processing</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <DocumentProcessor
              documentType={DocumentType.APPLICANT_UPLOAD}
              onDocumentProcessed={handleDocumentProcessed}
            />
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item xs={12}>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Environmental Documents</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              {processedDocuments.map((doc) => (
                <ListItem
                  key={doc.id}
                  button
                  onClick={() => handleDocumentSelect(doc.id)}
                  selected={selectedDocument?.id === doc.id}
                >
                  <ListItemText
                    primary={doc.filename}
                    secondary={`Uploaded: ${new Date(doc.upload_date).toLocaleDateString()}`}
                  />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      </Grid>

      {selectedDocument && (
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Document Content
            </Typography>
            {loadingDocument ? (
              <CircularProgress />
            ) : (
              <Box
                sx={{
                  maxHeight: 400,
                  overflow: 'auto',
                  bgcolor: 'background.paper',
                  p: 2,
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Typography
                  component="pre"
                  sx={{
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    fontFamily: 'monospace'
                  }}
                >
                  {documentText}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      )}

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Create EHP Review
            </Typography>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Environmental Impacts</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            label="Impact Category"
                            value={formData.impact.category}
                            onChange={(e) => setFormData({
                              ...formData,
                              impact: { ...formData.impact, category: e.target.value }
                            })}
                          />
                        </Grid>
                        <Grid item xs={12} md={8}>
                          <TextField
                            fullWidth
                            label="Impact Description"
                            value={formData.impact.description}
                            onChange={(e) => setFormData({
                              ...formData,
                              impact: { ...formData.impact, description: e.target.value }
                            })}
                            multiline
                            rows={2}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            variant="outlined"
                            onClick={addImpact}
                            disabled={!formData.impact.category || !formData.impact.description}
                          >
                            Add Impact
                          </Button>
                        </Grid>
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                </Grid>

                <Grid item xs={12}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Historical Considerations</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            label="Consideration Type"
                            value={formData.consideration.type}
                            onChange={(e) => setFormData({
                              ...formData,
                              consideration: { ...formData.consideration, type: e.target.value }
                            })}
                          />
                        </Grid>
                        <Grid item xs={12} md={8}>
                          <TextField
                            fullWidth
                            label="Consideration Description"
                            value={formData.consideration.description}
                            onChange={(e) => setFormData({
                              ...formData,
                              consideration: { ...formData.consideration, description: e.target.value }
                            })}
                            multiline
                            rows={2}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            variant="outlined"
                            onClick={addConsideration}
                            disabled={!formData.consideration.type || !formData.consideration.description}
                          >
                            Add Consideration
                          </Button>
                        </Grid>
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                </Grid>

                <Grid item xs={12}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Required Permits</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Box display="flex" gap={1}>
                            <TextField
                              fullWidth
                              label="Permit"
                              value={formData.permit}
                              onChange={(e) => setFormData({
                                ...formData,
                                permit: e.target.value
                              })}
                            />
                            <Button
                              variant="outlined"
                              onClick={addPermit}
                              disabled={!formData.permit.trim()}
                            >
                              Add Permit
                            </Button>
                          </Box>
                        </Grid>
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={loading}
                  >
                    Create Review
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Grid>

      {ehpReviews.length > 0 && (
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Existing EHP Reviews
              </Typography>
              <List>
                {ehpReviews.map((review) => (
                  <ListItem key={review.id} divider>
                    <ListItemText
                      primary={`Review #${review.id}`}
                      secondary={
                        <>
                          <Typography component="span" variant="body2">
                            Created: {new Date(review.created_at).toLocaleDateString()}
                          </Typography>
                          <br />
                          <Typography component="span" variant="body2">
                            Impacts: {review.environmental_impact?.impacts?.length || 0}
                          </Typography>
                        </>
                      }
                    />
                    <Chip
                      label={review.status}
                      color={
                        review.status === 'completed' ? 'success' :
                        review.status === 'rejected' ? 'error' :
                        review.status === 'in_progress' ? 'warning' : 'default'
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      )}
    </Grid>
  );
};

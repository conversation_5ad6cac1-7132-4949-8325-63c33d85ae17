<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.8, maximum-scale=1.5">
    <title>Emergency Work - ComplianceMax V74</title>
    <style>
        /* UNIVERSAL SCALING FIX */
        * { box-sizing: border-box; margin: 0; padding: 0; }
        html { font-size: 16px; zoom: 1.0; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            min-height: 100vh;
            color: white;
            zoom: 1.0 !important;
            transform: scale(1.0);
            overflow-x: hidden;
        }

        /* FIXED NAVIGATION */
        .nav-header {
            background: rgba(0, 0, 0, 0.9);
            height: 60px;
            position: fixed;
            top: 0; left: 0; right: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .nav-logo {
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
        }

        .nav-menu {
            display: flex;
            gap: 16px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        /* MAIN CONTENT */
        .main-content {
            margin-top: 60px;
            padding: 32px 24px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .page-title {
            font-size: 48px;
            font-weight: 800;
            text-align: center;
            margin-bottom: 16px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-subtitle {
            font-size: 20px;
            text-align: center;
            opacity: 0.9;
            margin-bottom: 48px;
        }

        .emergency-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .category-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
        }

        .category-icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }

        .category-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .category-desc {
            font-size: 16px;
            opacity: 0.9;
        }

        /* NOTIFICATION SYSTEM */
        .notification-container {
            position: fixed;
            top: 80px;
            right: 24px;
            z-index: 1100;
            max-width: 400px;
        }

        .notification {
            background: rgba(255, 255, 255, 0.95);
            color: #1f2937;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        /* RESPONSIVE */
        @media (max-width: 768px) {
            .nav-menu { display: none; }
            .main-content { padding: 16px; }
            .page-title { font-size: 32px; }
            .category-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- Fixed Navigation -->
    <nav class="nav-header">
        <a href="/" class="nav-logo">🚨 ComplianceMax V74 - Emergency Work</a>
        <div class="nav-menu">
            <a href="/" class="nav-item">🏠 Home</a>
            <a href="/dashboard" class="nav-item">📊 Dashboard</a>
            <a href="/emergency" class="nav-item active">🚨 Emergency</a>
            <a href="/cbcs" class="nav-item">🏗️ CBCS</a>
            <a href="/professional-intake" class="nav-item">📋 Professional</a>
            <a href="/logout" class="nav-item">🚪 Logout</a>
        </div>
    </nav>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Main Content -->
    <main class="main-content">
        <h1 class="page-title">Emergency Work</h1>
        <p class="page-subtitle">Categories A & B - Debris Removal and Emergency Protective Measures</p>

        <div class="emergency-container">
            <div class="category-grid">
                <div class="category-card" onclick="window.location.href='/cbcs'">
                    <span class="category-icon">🗑️</span>
                    <h3 class="category-title">Category A</h3>
                    <p class="category-desc">Debris removal from public roads, bridges, and facilities</p>
                </div>

                <div class="category-card" onclick="window.location.href='/cbcs'">
                    <span class="category-icon">🛡️</span>
                    <h3 class="category-title">Category B</h3>
                    <p class="category-desc">Emergency protective measures to save lives and protect property</p>
                </div>
            </div>

            <div style="text-align: center; margin-top: 32px;">
                <p style="font-size: 18px; margin-bottom: 24px;">
                    Click on a category above to begin emergency work documentation
                </p>
                <p style="font-size: 14px; opacity: 0.8;">
                    For permanent work (Categories C-G), use the <a href="/cbcs" style="color: #fbbf24; text-decoration: underline;">CBCS Work module</a>
                </p>
            </div>
        </div>
    </main>

    <script>
        // Notification system
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 4px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                <div>${message}</div>
            `;
            
            container.appendChild(notification);
            setTimeout(() => notification.classList.add('show'), 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => container.removeChild(notification), 300);
            }, 5000);
        }

        // Force viewport consistency on load
        window.addEventListener('load', function() {
            let viewport = document.querySelector('meta[name=viewport]');
            if (viewport) {
                viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.8, maximum-scale=1.5';
            }
            
            document.body.style.zoom = '1.0';
            document.documentElement.style.zoom = '1.0';
            
            showNotification('Emergency Work module loaded successfully!', 'success');
        });
    </script>
</body>
</html> 
// nav.js - Robust navigation loader with relative path support
(async function loadNav(){
  try{
    // Resolve path relative to current page (works in localhost and GitHub Pages)
    const here = location.pathname.replace(/[^/]*$/,'');
    const res = await fetch(`${here}partials/nav.html`);
    if (!res.ok) {
      console.warn('nav.html not found, trying absolute path');
      const fallback = await fetch('/partials/nav.html');
      if (!fallback.ok) return;
      const html = await fallback.text();
      const host = document.getElementById('universal-nav');
      if (host) {
        // Secure DOM insertion - parse and validate before insertion
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const navContent = doc.body.firstElementChild;

        if (navContent && navContent.tagName === 'NAV') {
          host.replaceChildren(navContent);
        } else {
          console.error('Invalid navigation HTML structure');
        }

        // Initialize scroll behavior for ComplianceMax header
        initScrollBehavior();
      }
      return;
    }
    const html = await res.text();
    const host = document.getElementById('universal-nav');
    if (host) {
      // Secure DOM insertion - parse and validate before insertion
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      const navContent = doc.body.firstElementChild;

      if (navContent && navContent.tagName === 'NAV') {
        host.replaceChildren(navContent);
      } else {
        console.error('Invalid navigation HTML structure');
      }

      // Initialize scroll behavior for ComplianceMax header
      initScrollBehavior();
    }
  }catch(e){
    console.warn('nav load failed', e);
  }
})();

// Scroll behavior for ComplianceMax header
function initScrollBehavior() {
  const header = document.querySelector('.cmx-header');
  if (!header) return;

  let lastScrollY = window.scrollY;
  let ticking = false;

  function updateHeader() {
    const scrollY = window.scrollY;

    if (scrollY > 50) {
      header.classList.add('scrolled');
    } else {
      header.classList.remove('scrolled');
    }

    lastScrollY = scrollY;
    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateHeader);
      ticking = true;
    }
  }

  window.addEventListener('scroll', requestTick, { passive: true });
}

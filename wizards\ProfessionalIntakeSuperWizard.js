"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var jsonFormParser_1 = require("../lib/jsonFormParser");
var femaApiMock_1 = require("../lib/femaApiMock");
var Unified_Compliance_Checklist_TAGGED_json_1 = require("../data/json/Unified_Compliance_Checklist_TAGGED.json");
var ProfessionalIntakeSuperWizard = function () {
    var _a = (0, react_1.useState)(0), currentStepIndex = _a[0], setCurrentStepIndex = _a[1];
    var _b = (0, react_1.useState)({}), formData = _b[0], setFormData = _b[1];
    var _c = (0, react_1.useState)([]), formSteps = _c[0], setFormSteps = _c[1];
    var _d = (0, react_1.useState)({}), errors = _d[0], setErrors = _d[1];
    var _e = (0, react_1.useState)(null), parseError = _e[0], setParseError = _e[1];
    var _f = (0, react_1.useState)(false), isSubmitting = _f[0], setIsSubmitting = _f[1];
    var _g = (0, react_1.useState)(null), femaData = _g[0], setFemaData = _g[1];
    var _h = (0, react_1.useState)(null), apiWarning = _h[0], setApiWarning = _h[1];
    var _j = (0, react_1.useState)(false), showSMEModal = _j[0], setShowSMEModal = _j[1];
    var _k = (0, react_1.useState)({}), overrides = _k[0], setOverrides = _k[1];
    var _l = (0, react_1.useTransition)(), startTransition = _l[1];
    (0, react_1.useEffect)(function () {
        (0, femaApiMock_1.fetchFemaData)()
            .then(function (data) { return setFemaData(data); })
            .catch(function () {
            setApiWarning('Failed to fetch latest FEMA data; using defaults.');
        });
    }, []);
    (0, react_1.useEffect)(function () {
        try {
            var parsedSteps = (0, jsonFormParser_1.parseJsonSchemaToFormSteps)([Unified_Compliance_Checklist_TAGGED_json_1.default]);
            parsedSteps = parsedSteps.map(function (step) { return (__assign(__assign({}, step), { fields: step.fields.map(function (field) { return (__assign(__assign({}, field), { conditionalLogic: overrides[field.name] || field.conditionalLogic })); }) })); });
            setFormSteps(parsedSteps);
            if (jsonFormParser_1.unstructuredConditionalLogicFields.length > 0) {
                console.warn('Unstructured Fields:', jsonFormParser_1.unstructuredConditionalLogicFields);
                setShowSMEModal(true);
            }
        }
        catch (error) {
            setParseError(error.message);
        }
    }, [overrides]);
    (0, react_1.useEffect)(function () {
        if (formSteps.length > 0) {
            validateCurrentStep();
        }
    }, [currentStepIndex, formData, formSteps, femaData]);
    var currentStep = formSteps[currentStepIndex];
    var isFieldVisible = (0, react_1.useMemo)(function () { return function (field) {
        var logic = field.conditionalLogic;
        if (!logic)
            return true;
        if ('conditions' in logic) {
            var _a = logic.logic_operator, logic_operator = _a === void 0 ? 'AND' : _a, conditions = logic.conditions;
            var results = conditions.map(function (cond) {
                var depValue = formData[cond.dependsOn];
                if (depValue === undefined)
                    return false;
                switch (cond.operator || '==') {
                    case '==': return depValue === cond.showIfValue;
                    case '!=': return depValue !== cond.showIfValue;
                    case '>': return depValue > cond.showIfValue;
                    case '<': return depValue < cond.showIfValue;
                    case '>=': return depValue >= cond.showIfValue;
                    case '<=': return depValue <= cond.showIfValue;
                    default: return false;
                }
            });
            return logic_operator === 'AND' ? results.every(function (r) { return r; }) : results.some(function (r) { return r; });
        }
        else {
            var dependsOn = logic.dependsOn, _b = logic.operator, operator = _b === void 0 ? '==' : _b, showIfValue = logic.showIfValue;
            var depValue = formData[dependsOn];
            if (depValue === undefined)
                return false;
            switch (operator) {
                case '==': return depValue === showIfValue;
                case '!=': return depValue !== showIfValue;
                case '>': return depValue > showIfValue;
                case '<': return depValue < showIfValue;
                case '>=': return depValue >= showIfValue;
                case '<=': return depValue <= showIfValue;
                default: return false;
            }
        }
    }; }, [formData]);
    var isStepVisible = (0, react_1.useMemo)(function () { return function (step) {
        if (!step.conditionalLogic)
            return true;
        return isFieldVisible(__assign(__assign({}, step), { conditionalLogic: step.conditionalLogic }));
    }; }, [isFieldVisible]);
    var visibleStepsCount = (0, react_1.useMemo)(function () { return formSteps.filter(isStepVisible).length; }, [formSteps, isStepVisible]);
    var progress = ((currentStepIndex + 1) / visibleStepsCount) * 100;
    var getNextVisibleStepIndex = function (start, direction) {
        var index = start + direction;
        while (index >= 0 && index < formSteps.length) {
            if (isStepVisible(formSteps[index]))
                return index;
            index += direction;
        }
        return start;
    };
    var largeThreshold = (femaData === null || femaData === void 0 ? void 0 : femaData.largeProjectThreshold) || 1062900;
    var validateField = function (field, value) {
        if (!isFieldVisible(field))
            return '';
        var error = '';
        var validation = field.validation, label = field.label, type = field.type;
        if ((validation === null || validation === void 0 ? void 0 : validation.required) && (value == null || value === '' || (Array.isArray(value) && !value.length))) {
            error = "".concat(label, " is required.");
        }
        else if (value != null && value !== '') {
            if (type === 'number') {
                var numValue = parseFloat(value);
                if (isNaN(numValue))
                    error = "".concat(label, " must be a number.");
                else if ((validation === null || validation === void 0 ? void 0 : validation.min) && numValue < validation.min)
                    error = "".concat(label, " must be at least ").concat(validation.min, " (PAPPG v5.0 reasonableness).");
                else if ((validation === null || validation === void 0 ? void 0 : validation.max) && numValue > validation.max)
                    error = "".concat(label, " must not exceed ").concat(validation.max, ".");
                if (field.label.toLowerCase().includes('cost') && numValue > largeThreshold) {
                    error += " [Exceeds Large Project Threshold ($".concat(largeThreshold.toLocaleString(), ") - SME Review Required (PAPPG v5.0)]");
                }
            }
            else if (typeof value === 'string') {
                if ((validation === null || validation === void 0 ? void 0 : validation.minLength) && value.length < validation.minLength)
                    error = "".concat(label, " min ").concat(validation.minLength, " chars.");
                if ((validation === null || validation === void 0 ? void 0 : validation.maxLength) && value.length > validation.maxLength)
                    error = "".concat(label, " max ").concat(validation.maxLength, " chars.");
                if ((validation === null || validation === void 0 ? void 0 : validation.pattern) && !new RegExp(validation.pattern).test(value))
                    error = "".concat(label, " invalid format.");
            }
        }
        return error;
    };
    var validateCurrentStep = function () {
        if (!currentStep || !isStepVisible(currentStep))
            return true;
        var newErrors = {};
        var isValid = true;
        currentStep.fields.forEach(function (field) {
            if (field.type === 'info')
                return;
            var error = validateField(field, formData[field.name]);
            if (error) {
                newErrors[field.name] = error;
                isValid = false;
            }
        });
        setErrors(newErrors);
        return isValid;
    };
    var handleNext = function () {
        if (validateCurrentStep()) {
            startTransition(function () {
                var nextIndex = getNextVisibleStepIndex(currentStepIndex, 1);
                if (nextIndex !== currentStepIndex)
                    setCurrentStepIndex(nextIndex);
            });
        }
    };
    var handlePrevious = function () {
        startTransition(function () {
            var prevIndex = getNextVisibleStepIndex(currentStepIndex, -1);
            if (prevIndex !== currentStepIndex)
                setCurrentStepIndex(prevIndex);
        });
    };
    var handleChange = function (e) {
        var _a = e.target, name = _a.name, type = _a.type, value = _a.value, checked = _a.checked, files = _a.files;
        var newValue;
        if (type === 'file') {
            newValue = e.target.multiple ? Array.from(files || []) : files === null || files === void 0 ? void 0 : files[0];
        }
        else if (type === 'checkbox') {
            newValue = checked;
        }
        else if (e.target instanceof HTMLSelectElement && e.target.multiple) {
            newValue = Array.from(e.target.selectedOptions, function (opt) { return opt.value; });
        }
        else {
            newValue = value;
        }
        setFormData(function (prev) {
            var _a;
            return (__assign(__assign({}, prev), (_a = {}, _a[name] = newValue, _a)));
        });
        validateCurrentStep();
    };
    var handleSubmit = function () { return __awaiter(void 0, void 0, void 0, function () {
        var allValid, textData_1, filesToUpload_1, complianceDetails, submissionData, body_1, response, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (isSubmitting)
                        return [2 /*return*/];
                    allValid = formSteps.every(function (step, idx) {
                        if (!isStepVisible(step))
                            return true;
                        setCurrentStepIndex(idx);
                        return validateCurrentStep();
                    });
                    if (!allValid)
                        return [2 /*return*/];
                    setIsSubmitting(true);
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 5]);
                    textData_1 = __assign({}, formData);
                    filesToUpload_1 = [];
                    formSteps.flatMap(function (s) { return s.fields; }).forEach(function (f) {
                        if (f.type === 'file') {
                            var val = textData_1[f.name];
                            if (val) {
                                filesToUpload_1.push({ name: f.name, files: Array.isArray(val) ? val : [val] });
                                delete textData_1[f.name];
                            }
                        }
                    });
                    complianceDetails = formSteps.flatMap(function (s) { return s.fields.filter(function (f) { return f.policyCitation || f.documentationRequired; }).map(function (f) {
                        var _a;
                        return ({
                            fieldName: f.name,
                            policyCitation: f.policyCitation,
                            documentationRequired: f.documentationRequired || [],
                            value: f.type === 'file' ? ((_a = formData[f.name]) === null || _a === void 0 ? void 0 : _a.map(function (file) { return file.name; }).join(', ')) || '' : textData_1[f.name],
                            pappgVersion: f.pappgVersion,
                            notes: f.notes,
                        });
                    }); });
                    submissionData = __assign(__assign({}, textData_1), { complianceDetails: complianceDetails });
                    body_1 = new FormData();
                    body_1.append('data', JSON.stringify(submissionData));
                    filesToUpload_1.forEach(function (_a) {
                        var name = _a.name, files = _a.files;
                        files.forEach(function (file, i) { return body_1.append("".concat(name, "[").concat(i, "]"), file); });
                    });
                    return [4 /*yield*/, fetch('/api/submit-professional-intake', {
                            method: 'POST',
                            body: body_1,
                        })];
                case 2:
                    response = _a.sent();
                    if (response.ok)
                        console.log('Submitted!');
                    else
                        console.error('Failed:', response.statusText);
                    return [3 /*break*/, 5];
                case 3:
                    error_1 = _a.sent();
                    console.error('Error:', error_1);
                    return [3 /*break*/, 5];
                case 4:
                    setIsSubmitting(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); };
    var handleSMEOverride = function (fieldName, newLogic) {
        setOverrides(function (prev) {
            var _a;
            return (__assign(__assign({}, prev), (_a = {}, _a[fieldName] = newLogic, _a)));
        });
    };
    if (parseError)
        return <div role="alert">Parse error: {parseError}</div>;
    if (!formSteps.length)
        return <div aria-live="polite">Loading...</div>;
    return (<div className="professional-intake-wizard" role="region" aria-labelledby="wizard-title">
      {apiWarning && <p role="alert" style={{ color: 'orange' }}>{apiWarning}</p>}
      <h2 id="wizard-title">{currentStep.title}</h2>
      <progress value={progress} max={100} aria-label={"Progress: ".concat(Math.round(progress), "%")}/>
      <p aria-live="polite">Step {currentStepIndex + 1} of {visibleStepsCount}</p>
      <button onClick={function () { return setShowSMEModal(true); }} disabled={!jsonFormParser_1.unstructuredConditionalLogicFields.length}>Review Flagged Items</button>
      <form onSubmit={function (e) { return e.preventDefault(); }}>
        {currentStep.fields.filter(isFieldVisible).map(function (field) {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
            return (<div key={field.name} className="form-field" style={jsonFormParser_1.unstructuredConditionalLogicFields.some(function (u) { return u.field === field.label; }) ? { border: '1px solid orange' } : {}}>
            <label htmlFor={field.name}>
              {field.label}
              {field.notes && <span title={field.notes} aria-label={field.notes} role="tooltip" style={{ marginLeft: '5px', cursor: 'help' }}> ⓘ</span>}
              {jsonFormParser_1.unstructuredConditionalLogicFields.some(function (u) { return u.field === field.label; }) && <span style={{ color: 'orange' }}> [SME Review Needed]</span>}
              {field.pappgVersion && <span> (PAPPG {field.pappgVersion})</span>}
            </label>
            {field.type === 'info' ? (<p style={{ fontStyle: 'italic', color: '#555' }} aria-label={field.value}>{field.value}</p>) : field.type === 'file' ? (<input type="file" id={field.name} name={field.name} onChange={handleChange} required={(_a = field.validation) === null || _a === void 0 ? void 0 : _a.required} aria-required={!!((_b = field.validation) === null || _b === void 0 ? void 0 : _b.required)} multiple={!!field.multiple}/>) : field.type === 'checkbox' ? (<input type="checkbox" id={field.name} name={field.name} checked={!!formData[field.name]} onChange={handleChange} required={(_c = field.validation) === null || _c === void 0 ? void 0 : _c.required} aria-required={!!((_d = field.validation) === null || _d === void 0 ? void 0 : _d.required)}/>) : field.type === 'select' || field.type === 'radio' ? (field.type === 'select' ? (<select id={field.name} name={field.name} value={formData[field.name] || ''} onChange={handleChange} required={(_e = field.validation) === null || _e === void 0 ? void 0 : _e.required} aria-required={!!((_f = field.validation) === null || _f === void 0 ? void 0 : _f.required)}>
                  {(_g = field.options) === null || _g === void 0 ? void 0 : _g.map(function (opt) { return <option key={opt.value} value={opt.value}>{opt.label}</option>; })}
                </select>) : (<div className="radio-group">
                  {(_h = field.options) === null || _h === void 0 ? void 0 : _h.map(function (opt) {
                        var _a;
                        return (<label key={opt.value}>
                      <input type="radio" name={field.name} value={opt.value} checked={formData[field.name] === opt.value} onChange={handleChange} required={(_a = field.validation) === null || _a === void 0 ? void 0 : _a.required}/>
                      {opt.label}
                    </label>);
                    })}
                </div>)) : (<input type={field.type} id={field.name} name={field.name} value={formData[field.name] || ''} onChange={handleChange} required={(_j = field.validation) === null || _j === void 0 ? void 0 : _j.required} aria-required={!!((_k = field.validation) === null || _k === void 0 ? void 0 : _k.required)}/>)}
            {errors[field.name] && <span role="alert" style={{ color: 'red' }}>{errors[field.name]}</span>}
            {field.documentationRequired && field.documentationRequired.length > 0 && field.type !== 'file' && (<p style={{ fontSize: '0.8em', color: '#777' }}>Required Docs: {field.documentationRequired.join(', ')}</p>)}
            {field.policyCitation && <p style={{ fontSize: '0.8em', color: '#777' }}>Citation: {field.policyCitation}</p>}
          </div>);
        })}
        <div className="form-navigation">
          {currentStepIndex > 0 && <button type="button" onClick={handlePrevious} disabled={isSubmitting} aria-disabled={!!isSubmitting}>Previous</button>}
          {currentStepIndex < formSteps.length - 1 ? (<button type="button" onClick={handleNext} disabled={isSubmitting} aria-disabled={!!isSubmitting}>Next</button>) : (<button type="button" onClick={handleSubmit} disabled={isSubmitting} aria-disabled={!!isSubmitting}>Submit</button>)}
        </div>
      </form>
      {showSMEModal && <SMEReviewModal onClose={function () { return setShowSMEModal(false); }} onOverride={handleSMEOverride}/>}
    </div>);
};
// SMEReviewModal with bulletproof operator select
var SMEReviewModal = function (_a) {
    var onClose = _a.onClose, onOverride = _a.onOverride;
    var _b = (0, react_1.useState)(''), selectedField = _b[0], setSelectedField = _b[1];
    var _c = (0, react_1.useState)('single'), logicType = _c[0], setLogicType = _c[1];
    var _d = (0, react_1.useState)(''), dependsOn = _d[0], setDependsOn = _d[1];
    var _e = (0, react_1.useState)('=='), operator = _e[0], setOperator = _e[1];
    var _f = (0, react_1.useState)(''), showIfValue = _f[0], setShowIfValue = _f[1];
    var handleApply = function () {
        if (logicType === 'single') {
            onOverride(selectedField, { dependsOn: dependsOn, operator: operator, showIfValue: showIfValue });
        }
        else {
            // For compound, extend as needed
        }
        onClose();
    };
    return (<div role="dialog" aria-modal="true" style={{ background: "#fff", border: "2px solid #888", padding: "1rem", zIndex: 1000, maxWidth: 480, margin: "2rem auto" }}>
      <h3>SME Review: Unstructured Logic</h3>
      <ul>
        {jsonFormParser_1.unstructuredConditionalLogicFields.map(function (item, i) { return (<li key={i} onClick={function () { return setSelectedField(item.field); }} style={{ cursor: "pointer", fontWeight: selectedField === item.field ? "bold" : "normal" }}>
            {item.field}: {item.value}
          </li>); })}
      </ul>
      {selectedField && (<>
          <label>
            Depends On: <input value={dependsOn} onChange={function (e) { return setDependsOn(e.target.value); }}/>
          </label>
          <label>
            Operator:
            <select value={operator} onChange={function (e) { return setOperator(e.target.value); }}>
              <option value="==">Equals (==)</option>
              <option value="!=">Not Equals (!=)</option>
              <option value=">">Greater Than ({">"}) </option>
              <option value="<">Less Than ({"<"})</option>
              <option value=">=">Greater Than or Equal ({">="}) </option>
              <option value="<=">Less Than or Equal ({"<="})</option>
            </select>
          </label>
          <label>
            Show If Value: <input value={showIfValue} onChange={function (e) { return setShowIfValue(e.target.value); }}/>
          </label>
          <button onClick={handleApply}>Apply Override</button>
        </>)}
      <button onClick={onClose} style={{ marginTop: "1rem" }}>Close</button>
    </div>);
};
exports.default = ProfessionalIntakeSuperWizard;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Work Intake - ComplianceMax V74</title>
    <link rel="stylesheet" href="/static/compliancemax-unified.css">
    <style>
        :root {
            --emergency-primary: #ef4444;
            --emergency-secondary: #dc2626;
            --emergency-light: #fef2f2;
            --emergency-dark: #991b1b;
        }

        body {
            background: linear-gradient(135deg, var(--emergency-primary) 0%, var(--emergency-secondary) 100%);
            min-height: 100vh;
            font-family: var(--font-family);
        }

        .cm-header {
            background: var(--emergency-light);
            padding: var(--spacing-sm);
            box-shadow: var(--shadow-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cm-header h1 {
            color: var(--emergency-primary);
            font-size: var(--font-xl);
            font-weight: 700;
        }

        .nav-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }

        .cm-btn-nav {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            text-decoration: none;
            font-size: var(--font-sm);
            font-weight: 600;
            transition: background-color 0.2s;
        }

        .cm-btn-nav:hover {
            background: var(--color-primary-dark);
        }

        .emergency-form {
            max-width: 1200px;
            margin: var(--spacing-lg) auto;
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .form-header {
            background: var(--emergency-primary);
            color: white;
            padding: var(--spacing-md);
            text-align: center;
        }

        .form-content {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: var(--spacing-md);
            padding: var(--spacing-lg);
        }

        .form-section h3 {
            color: var(--emergency-primary);
            font-size: var(--font-lg);
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-xs);
            border-bottom: 2px solid var(--emergency-primary);
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
            color: var(--color-text);
        }

        .form-control {
            width: 100%;
            padding: var(--spacing-sm);
            border: 2px solid var(--color-border);
            border-radius: var(--border-radius);
            font-size: var(--font-base);
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--emergency-primary);
        }

        .urgency-grid {
            display: grid;
            gap: var(--spacing-xs);
        }

        .urgency-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm);
            border: 2px solid var(--color-border);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.2s;
        }

        .urgency-item:hover {
            background: var(--emergency-light);
            border-color: var(--emergency-primary);
        }

        .urgency-item.selected {
            background: var(--emergency-light);
            border-color: var(--emergency-primary);
        }

        .urgency-item input {
            margin-right: var(--spacing-xs);
        }

        .checkbox-grid {
            display: grid;
            gap: var(--spacing-xs);
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            font-size: var(--font-sm);
        }

        .checkbox-item input {
            margin-right: var(--spacing-xs);
        }

        .form-footer {
            background: var(--color-background-secondary);
            padding: var(--spacing-md);
            text-align: center;
            border-top: 1px solid var(--color-border);
        }

        .emergency-submit {
            background: var(--emergency-primary);
            color: white;
            border: none;
            padding: var(--spacing-md) var(--spacing-xl);
            border-radius: var(--border-radius);
            font-size: var(--font-lg);
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .emergency-submit:hover {
            background: var(--emergency-secondary);
        }

        .status-badges {
            display: flex;
            gap: var(--spacing-xs);
        }

        .badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-full);
            font-size: var(--font-sm);
            font-weight: 600;
        }

        .badge-emergency {
            background: var(--emergency-primary);
            color: white;
        }

        .badge-processing {
            background: var(--color-warning);
            color: white;
        }

        @media (max-width: 768px) {
            .form-content {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="cm-header">
        <h1>🚨 Emergency Work Intake</h1>
        <div class="nav-buttons">
            <a href="/dashboard" class="cm-btn-nav">🏠 Home</a>
            <a href="/cbcs" class="cm-btn-nav">📊 CBCS</a>
        </div>
    </div>

    <div class="emergency-form">
        <div class="form-header">
            <h2>Emergency Work Categories A & B</h2>
            <div class="status-badges">
                <span class="badge badge-emergency">24/7 Processing</span>
                <span class="badge badge-processing">Immediate Response</span>
            </div>
        </div>

        <div class="form-content">
            <!-- Project Information -->
            <div class="form-section">
                <h3>🏗️ Project Information</h3>
                
                <div class="form-group">
                    <label class="form-label">Project Name *</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Emergency project name" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Work Category *</label>
                    <div class="urgency-grid">
                        <div class="urgency-item">
                            <input type="radio" name="workCategory" value="A" required>
                            <span>Category A - Debris Removal</span>
                        </div>
                        <div class="urgency-item">
                            <input type="radio" name="workCategory" value="B" required>
                            <span>Category B - Emergency Protective Measures</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Incident Date *</label>
                    <input type="date" class="form-control" id="incidentDate" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Disaster Number</label>
                    <input type="text" class="form-control" id="disasterNumber" placeholder="FEMA-XXXX-DR">
                </div>

                <div class="form-group">
                    <label class="form-label">Estimated Cost *</label>
                    <input type="number" class="form-control" id="estimatedCost" placeholder="Enter cost in USD" required>
                </div>
            </div>

            <!-- Emergency Details -->
            <div class="form-section">
                <h3>⚡ Emergency Details</h3>
                
                <div class="form-group">
                    <label class="form-label">Urgency Level *</label>
                    <div class="urgency-grid">
                        <div class="urgency-item">
                            <input type="radio" name="urgencyLevel" value="immediate" required>
                            <span>🔴 Immediate (Life safety threat)</span>
                        </div>
                        <div class="urgency-item">
                            <input type="radio" name="urgencyLevel" value="critical" required>
                            <span>🚨 Critical (Infrastructure failure imminent)</span>
                        </div>
                        <div class="urgency-item">
                            <input type="radio" name="urgencyLevel" value="high" required>
                            <span>🟠 High (Public health/safety risk)</span>
                        </div>
                        <div class="urgency-item">
                            <input type="radio" name="urgencyLevel" value="medium" required>
                            <span>🟡 Medium (Property protection)</span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Emergency Description *</label>
                    <textarea class="form-control" id="emergencyDescription" rows="6" placeholder="Describe the emergency situation, immediate threats, and required actions" required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Location Details *</label>
                    <textarea class="form-control" id="locationDetails" rows="3" placeholder="Specific location, access routes, and site conditions" required></textarea>
                </div>
            </div>

            <!-- Response Requirements -->
            <div class="form-section">
                <h3>🛠️ Response Requirements</h3>
                
                <div class="form-group">
                    <label class="form-label">Required Actions</label>
                    <div class="checkbox-grid">
                        <label class="checkbox-item">
                            <input type="checkbox" name="actions" value="debris_removal">
                            Debris removal from roadways
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="actions" value="sandbagging">
                            Sandbagging/flood protection
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="actions" value="road_closure">
                            Road closure/barricades
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="actions" value="emergency_repairs">
                            Emergency infrastructure repairs
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="actions" value="evacuation_support">
                            Evacuation route support
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="actions" value="utility_protection">
                            Utility system protection
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Resources Needed</label>
                    <div class="checkbox-grid">
                        <label class="checkbox-item">
                            <input type="checkbox" name="resources" value="heavy_equipment">
                            Heavy equipment (excavators, loaders)
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resources" value="personnel">
                            Emergency response personnel
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resources" value="materials">
                            Emergency materials (sand, barriers)
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resources" value="contractors">
                            Emergency contractors
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Timeline *</label>
                    <select class="form-control" id="timeline" required>
                        <option value="">Select required timeline</option>
                        <option value="immediate">Immediate (within hours)</option>
                        <option value="24hours">Within 24 hours</option>
                        <option value="72hours">Within 72 hours</option>
                        <option value="1week">Within 1 week</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Emergency Contact *</label>
                    <input type="text" class="form-control" id="contactName" placeholder="Contact name" required style="margin-bottom: 8px;">
                    <input type="tel" class="form-control" id="contactPhone" placeholder="Contact phone" required style="margin-bottom: 8px;">
                    <input type="email" class="form-control" id="contactEmail" placeholder="Contact email">
                </div>
            </div>
        </div>
        
        <div class="form-footer">
            <div style="margin-bottom: var(--spacing-sm); color: var(--color-text-secondary);">
                Emergency Response System • 24/7 Processing • Immediate FEMA Coordination
            </div>
            <button type="button" class="emergency-submit" onclick="submitEmergencyIntake()">
                🚨 Submit Emergency Request
            </button>
        </div>
    </div>

    <script>
        // Unified notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `cm-notification cm-notification-${type}`;
            notification.textContent = message;
            
            // Position in top-right corner
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: 600;
                max-width: 400px;
                animation: slideIn 0.3s ease-out;
            `;
            
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Handle radio button selections
        document.addEventListener('change', function(e) {
            if (e.target.type === 'radio') {
                const groupName = e.target.name;
                document.querySelectorAll(`input[name="${groupName}"]`).forEach(radio => {
                    radio.closest('.urgency-item').classList.remove('selected');
                });
                e.target.closest('.urgency-item').classList.add('selected');
            }
        });
        
        async function submitEmergencyIntake() {
            const formData = {
                projectName: document.getElementById('projectName').value,
                workCategory: document.querySelector('input[name="workCategory"]:checked')?.value,
                incidentDate: document.getElementById('incidentDate').value,
                disasterNumber: document.getElementById('disasterNumber').value,
                estimatedCost: document.getElementById('estimatedCost').value,
                urgencyLevel: document.querySelector('input[name="urgencyLevel"]:checked')?.value,
                emergencyDescription: document.getElementById('emergencyDescription').value,
                locationDetails: document.getElementById('locationDetails').value,
                timeline: document.getElementById('timeline').value,
                contactName: document.getElementById('contactName').value,
                contactPhone: document.getElementById('contactPhone').value,
                contactEmail: document.getElementById('contactEmail').value,
                actions: Array.from(document.querySelectorAll('input[name="actions"]:checked')).map(cb => cb.value),
                resources: Array.from(document.querySelectorAll('input[name="resources"]:checked')).map(cb => cb.value),
                start_wizard: true
            };
            
            // Validate required fields
            if (!formData.projectName || !formData.workCategory || !formData.incidentDate || 
                !formData.estimatedCost || !formData.urgencyLevel || !formData.emergencyDescription ||
                !formData.locationDetails || !formData.timeline || !formData.contactName || !formData.contactPhone) {
                showNotification('Please fill in all required fields marked with *', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/intake/emergency', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    showNotification(`Emergency intake submitted successfully! ID: ${data.intake_id}`, 'success');
                    setTimeout(() => window.location.href = '/dashboard', 2000);
                } else {
                    showNotification('Error submitting emergency intake: ' + data.message, 'error');
                }
            } catch (error) {
                console.error('Submit error:', error);
                showNotification('Error submitting emergency intake. Please try again.', 'error');
            }
        }
        
        // Auto-set incident date to today
        document.addEventListener('DOMContentLoaded', function() {
            const incidentDate = document.getElementById('incidentDate');
            if (!incidentDate.value) {
                const today = new Date().toISOString().split('T')[0];
                incidentDate.value = today;
            }
        });
    </script>
</body>
</html> 
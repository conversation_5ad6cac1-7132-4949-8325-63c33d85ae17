import React from 'react';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  useTheme,
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Gavel as GavelIcon,
  Code as CodeIcon,
} from '@mui/icons-material';

const Navbar: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  const navItems = [
    {
      text: 'Quick Check',
      path: '/quick-check',
      icon: <SpeedIcon />,
    },
    {
      text: 'Compliance Review',
      path: '/compliance',
      icon: <GavelIcon />,
    },
    {
      text: 'Technical',
      path: '/technical',
      icon: <CodeIcon />,
    },
  ];

  return (
    <AppBar
      position="fixed"
      sx={{
        background: 'rgba(26, 31, 53, 0.8)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
      }}
    >
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          <Typography
            variant="h6"
            component={RouterLink}
            to="/"
            sx={{
              mr: 4,
              fontWeight: 700,
              color: 'white',
              textDecoration: 'none',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            ComplianceMax
          </Typography>

          <Box sx={{ flexGrow: 1, display: 'flex', gap: 2 }}>
            {navItems.map((item) => (
              <Button
                key={item.path}
                component={RouterLink}
                to={item.path}
                startIcon={item.icon}
                sx={{
                  color: isActive(item.path) ? theme.palette.primary.main : 'white',
                  borderBottom: isActive(item.path)
                    ? `2px solid ${theme.palette.primary.main}`
                    : '2px solid transparent',
                  borderRadius: 0,
                  px: 2,
                  py: 2,
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                {item.text}
              </Button>
            ))}
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Navbar;

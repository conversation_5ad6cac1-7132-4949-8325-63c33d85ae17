import React, { useState } from 'react';
import {
  <PERSON>,
  Paper,
  Tabs,
  Tab,
  <PERSON>po<PERSON>,
  Dialog,
  DialogTitle,
  <PERSON>alogContent,
  IconButton,
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import WorkflowExecution from './WorkflowExecution';
import WorkflowExecutionHistory from './WorkflowExecutionHistory';
import { Workflow, WorkflowExecution as WorkflowExecutionType } from '../../types/workflow';

interface WorkflowExecutionViewProps {
  workflow: Workflow;
  onClose?: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`execution-tabpanel-${index}`}
      aria-labelledby={`execution-tab-${index}`}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const WorkflowExecutionView: React.FC<WorkflowExecutionViewProps> = ({
  workflow,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecutionType | null>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleViewExecution = (execution: WorkflowExecutionType) => {
    setSelectedExecution(execution);
    setActiveTab(0); // Switch to execution tab
  };

  const handleExecutionComplete = (execution: WorkflowExecutionType) => {
    setSelectedExecution(execution);
  };

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '80vh',
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {workflow.name} - Execution Management
          </Typography>
          {onClose && (
            <IconButton onClick={onClose} size="small">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Current Execution" />
            <Tab label="Execution History" />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <WorkflowExecution
            workflow={workflow}
            onExecutionComplete={handleExecutionComplete}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <WorkflowExecutionHistory
            workflow={workflow}
            onViewExecution={handleViewExecution}
          />
        </TabPanel>
      </DialogContent>
    </Dialog>
  );
};

export default WorkflowExecutionView; 
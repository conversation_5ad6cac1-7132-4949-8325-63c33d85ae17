/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Ccomponents%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Ccomponents%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTWF4JTVDRG9jdW1lbnRzJTVDQ0JDUyU1Q0pVTkUlMjAyMDI1JTIwUFJPSkVDVCUyMENPTVBMSUFOQ0VNQVgtVjc0JTVDSnVuZV8yMDI1LUNvbXBsaWFuY2VNYXglNUNBTEwlMjBORVclMjBBUFAlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q01heCU1Q0RvY3VtZW50cyU1Q0NCQ1MlNUNKVU5FJTIwMjAyNSUyMFBST0pFQ1QlMjBDT01QTElBTkNFTUFYLVY3NCU1Q0p1bmVfMjAyNS1Db21wbGlhbmNlTWF4JTVDQUxMJTIwTkVXJTIwQVBQJTVDY29tcG9uZW50cyU1Q3RoZW1lLXByb3ZpZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q01heCU1Q0RvY3VtZW50cyU1Q0NCQ1MlNUNKVU5FJTIwMjAyNSUyMFBST0pFQ1QlMjBDT01QTElBTkNFTUFYLVY3NCU1Q0p1bmVfMjAyNS1Db21wbGlhbmNlTWF4JTVDQUxMJTIwTkVXJTIwQVBQJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBK0s7QUFDL0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wbGlhbmNlbWF4Lz9lMGJhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTWF4XFxcXERvY3VtZW50c1xcXFxDQkNTXFxcXEpVTkUgMjAyNSBQUk9KRUNUIENPTVBMSUFOQ0VNQVgtVjc0XFxcXEp1bmVfMjAyNS1Db21wbGlhbmNlTWF4XFxcXEFMTCBORVcgQVBQXFxcXGNvbXBvbmVudHNcXFxcdGhlbWUtcHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxNYXhcXFxcRG9jdW1lbnRzXFxcXENCQ1NcXFxcSlVORSAyMDI1IFBST0pFQ1QgQ09NUExJQU5DRU1BWC1WNzRcXFxcSnVuZV8yMDI1LUNvbXBsaWFuY2VNYXhcXFxcQUxMIE5FVyBBUFBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Ccomponents%5Ctheme-provider.tsx&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp%5Cpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp%5Cpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDTWF4JTVDRG9jdW1lbnRzJTVDQ0JDUyU1Q0pVTkUlMjAyMDI1JTIwUFJPSkVDVCUyMENPTVBMSUFOQ0VNQVgtVjc0JTVDSnVuZV8yMDI1LUNvbXBsaWFuY2VNYXglNUNBTEwlMjBORVclMjBBUFAlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wbGlhbmNlbWF4Lz82ZGU4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcTWF4XFxcXERvY3VtZW50c1xcXFxDQkNTXFxcXEpVTkUgMjAyNSBQUk9KRUNUIENPTVBMSUFOQ0VNQVgtVjc0XFxcXEp1bmVfMjAyNS1Db21wbGlhbmNlTWF4XFxcXEFMTCBORVcgQVBQXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Dashboard */ \"(ssr)/./components/Dashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        frontend: \"checking\",\n        backend: \"checking\",\n        data: \"checking\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Frontend is obviously working if this renders\n        setConnectionStatus((prev)=>({\n                ...prev,\n                frontend: \"connected\"\n            }));\n        // Test backend connection\n        fetch(\"http://localhost:8000/health\").then((response)=>response.json()).then((data)=>{\n            setConnectionStatus((prev)=>({\n                    ...prev,\n                    backend: \"connected\"\n                }));\n        }).catch((error)=>{\n            setConnectionStatus((prev)=>({\n                    ...prev,\n                    backend: \"error\"\n                }));\n        });\n        // Test data availability\n        fetch(\"http://localhost:8000/api/v1/checklist/stats\").then((response)=>response.json()).then((data)=>{\n            if (data.total_items > 0) {\n                setConnectionStatus((prev)=>({\n                        ...prev,\n                        data: \"loaded\"\n                    }));\n            } else {\n                setConnectionStatus((prev)=>({\n                        ...prev,\n                        data: \"empty\"\n                    }));\n            }\n        }).catch((error)=>{\n            setConnectionStatus((prev)=>({\n                    ...prev,\n                    data: \"error\"\n                }));\n        });\n    }, []);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"connected\":\n            case \"loaded\":\n                return \"text-green-600\";\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"error\":\n            case \"empty\":\n                return \"text-red-600\";\n            default:\n                return \"text-gray-600\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"connected\":\n            case \"loaded\":\n                return \"✅\";\n            case \"checking\":\n                return \"\\uD83D\\uDD04\";\n            case \"error\":\n            case \"empty\":\n                return \"❌\";\n            default:\n                return \"⚪\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: \"\\uD83D\\uDE80 ComplianceMax V74 - LIVE TESTING DASHBOARD\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center gap-2 ${getStatusColor(connectionStatus.frontend)}`,\n                                    children: [\n                                        getStatusIcon(connectionStatus.frontend),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Frontend (Next.js):\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        connectionStatus.frontend\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center gap-2 ${getStatusColor(connectionStatus.backend)}`,\n                                    children: [\n                                        getStatusIcon(connectionStatus.backend),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Backend (FastAPI):\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        connectionStatus.backend\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center gap-2 ${getStatusColor(connectionStatus.data)}`,\n                                    children: [\n                                        getStatusIcon(connectionStatus.data),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Compliance Data:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        connectionStatus.data\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 m-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"\\uD83E\\uDDEA INTEGRATION TEST MODE:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this),\n                                \" This dashboard is testing real-time connections between:\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                \"• Next.js Frontend (Port 3333) ↔ FastAPI Backend (Port 8000) ↔ Processed Compliance Data (194 items)\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                \"• Path resolution fixes ✅ | Import script working ✅ | Real FEMA data loading ✅\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"m-6 p-6 bg-gray-50 rounded-lg border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"\\uD83C\\uDFD7️ System Architecture Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"✅ Working Components\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-1 text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Next.js frontend running (Port 3333)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• FastAPI backend API (Port 8000)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Data import scripts (194 items processed)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Path resolution across directories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• IF-THEN conditional logic processing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• PAPPG version determination\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• TypeScript API client integration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• React dashboard components\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"\\uD83D\\uDD27 Integration Points\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-1 text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• DOCS folder data ↔ Import scripts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Processed JSON ↔ FastAPI endpoints\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• API responses ↔ React components\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• FEMA compliance rules ↔ UI display\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Multi-directory path resolution\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Real-time health monitoring\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Error handling & fallbacks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• GROK AI tag integration ready\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"m-6 p-6 bg-green-50 rounded-lg border border-green-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4 text-green-800\",\n                        children: \"\\uD83D\\uDD27 DEVELOPMENT ENGINEERING STATUS\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-green-700 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"TEST, TEST, TEST Philosophy in Action:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 14\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc ml-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Multi-tier policy integration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" - PAPPG v1.0-v5.0, DRRA, CFR 200, NFIP frameworks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Event date-driven policy determination\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" - Incident date determines applicable regulations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Conditional logic mapping\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" - Enhanced IF-THEN rules for complex compliance scenarios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Phase 1 integration complete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" - 7 policy frameworks integrated with existing structure\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Ready for advanced processing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" - Docling document analysis, xAI Grok integration\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-3 font-medium\",\n                                children: [\n                                    \"\\uD83C\\uDFAF \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Continuing Engineering Excellence:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" Real-time system monitoring, comprehensive testing protocols, and continuous integration validation.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\page.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Dashboard.tsx":
/*!**********************************!*\
  !*** ./components/Dashboard.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Dashboard() {\n    const { health, loading: healthLoading, error: healthError } = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.useApiHealth)();\n    const { stats, loading: statsLoading, error: statsError } = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.useComplianceStats)();\n    const { items, loading: itemsLoading, error: itemsError } = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.useChecklistItems)({\n        limit: 5\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-blue-600 mb-2\",\n                        children: \"ComplianceMax V74 Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"FEMA Public Assistance Compliance Management System\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-md border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Backend Connection Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    healthLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-blue-600\",\n                        children: \"\\uD83D\\uDD04 Connecting to API...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    healthError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600\",\n                        children: [\n                            \"❌ API Offline: \",\n                            healthError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    health && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-lg font-medium ${health.status === \"healthy\" ? \"text-green-600\" : \"text-yellow-600\"}`,\n                                children: [\n                                    \"✅ API Status: \",\n                                    health.status\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    \"Version: \",\n                                    health.version,\n                                    \" | Database: \",\n                                    health.database\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 mt-2\",\n                                children: Object.entries(health.services).map(([service, status])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `px-2 py-1 rounded text-xs ${status.includes(\"operational\") || status.includes(\"connected\") ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                        children: [\n                                            service,\n                                            \": \",\n                                            status\n                                        ]\n                                    }, service, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-md border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Compliance Statistics\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    statsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-blue-600\",\n                        children: \"\\uD83D\\uDCCA Loading statistics...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    statsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600\",\n                        children: [\n                            \"❌ Stats Error: \",\n                            statsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: stats.total_items\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Total Items\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-green-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: Object.keys(stats.by_category).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-purple-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: Object.keys(stats.by_phase).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Phases\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4 bg-orange-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-orange-600\",\n                                        children: stats.conditional_logic_items\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"IF-THEN Rules\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-md border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Category Breakdown\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: Object.entries(stats.by_category).sort(([, a], [, b])=>b - a).slice(0, 10).map(([category, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm\",\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, category, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-md border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Recent Compliance Items\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    itemsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-blue-600\",\n                        children: \"\\uD83D\\uDCCB Loading items...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    itemsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600\",\n                        children: [\n                            \"❌ Items Error: \",\n                            itemsError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    items && items.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-600\",\n                                        children: item.title || \"Untitled Item\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm mt-1\",\n                                        children: item.description || \"No description available\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 mt-2\",\n                                        children: [\n                                            item.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs\",\n                                                children: item.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.pappg_version && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded text-xs\",\n                                                children: item.pappg_version\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.phase && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs\",\n                                                children: [\n                                                    \"Phase \",\n                                                    item.phase\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.trigger_condition_if && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IF:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" \",\n                                            item.trigger_condition_if.slice(0, 100),\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500\",\n                        children: \"No items loaded yet.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-md border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"System Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Frontend:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" Next.js (Running on :3333)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Backend:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" FastAPI \",\n                                    health ? `(v${health.version})` : \"(Checking...)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Data Processing:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" \",\n                                    stats ? \"✅ Active\" : \"⏳ Loading\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbXBsaWFuY2VtYXgvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeD85Mjg5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5pbXBvcnQgeyB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gXCJuZXh0LXRoZW1lcy9kaXN0L3R5cGVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufSJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatApiError: () => (/* binding */ formatApiError),\n/* harmony export */   isApiAvailable: () => (/* binding */ isApiAvailable),\n/* harmony export */   useApiHealth: () => (/* binding */ useApiHealth),\n/* harmony export */   useChecklistItems: () => (/* binding */ useChecklistItems),\n/* harmony export */   useComplianceStats: () => (/* binding */ useComplianceStats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * ComplianceMax V74 - API Client\r\n * ===============================\r\n * TypeScript client for connecting Next.js frontend to FastAPI backend\r\n */ const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n// ================= API Client Class =================\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const response = await fetch(url, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: \"Unknown error\"\n                }));\n            throw new Error(`API Error ${response.status}: ${errorData.error || response.statusText}`);\n        }\n        return response.json();\n    }\n    // ================= Health & Status =================\n    async getHealth() {\n        return this.request(\"/health\");\n    }\n    async getApiInfo() {\n        return this.request(\"/\");\n    }\n    // ================= Compliance Data =================\n    async getComplianceStats() {\n        return this.request(\"/api/v1/checklist/stats\");\n    }\n    async getChecklistItems(params = {}) {\n        const searchParams = new URLSearchParams();\n        if (params.limit) searchParams.append(\"limit\", params.limit.toString());\n        if (params.offset) searchParams.append(\"offset\", params.offset.toString());\n        if (params.category) searchParams.append(\"category\", params.category);\n        if (params.phase) searchParams.append(\"phase\", params.phase.toString());\n        if (params.search) searchParams.append(\"search\", params.search);\n        const endpoint = `/api/v1/checklist/items${searchParams.toString() ? `?${searchParams.toString()}` : \"\"}`;\n        return this.request(endpoint);\n    }\n    async getFEMACategories() {\n        return this.request(\"/api/v1/checklist/categories\");\n    }\n}\n// ================= Singleton Instance =================\nconst apiClient = new ApiClient();\n// ================= React Hooks =================\n\nfunction useApiHealth() {\n    const [health, setHealth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        async function fetchHealth() {\n            try {\n                setLoading(true);\n                const healthData = await apiClient.getHealth();\n                setHealth(healthData);\n                setError(null);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Unknown error\");\n                setHealth(null);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchHealth();\n    }, []);\n    return {\n        health,\n        loading,\n        error\n    };\n}\nfunction useComplianceStats() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        async function fetchStats1() {\n            try {\n                setLoading(true);\n                const statsData = await apiClient.getComplianceStats();\n                setStats(statsData);\n                setError(null);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Unknown error\");\n                setStats(null);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchStats1();\n    }, []);\n    return {\n        stats,\n        loading,\n        error,\n        refetch: ()=>fetchStats()\n    };\n}\nfunction useChecklistItems(params = {}) {\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        async function fetchItems() {\n            try {\n                setLoading(true);\n                const itemsData = await apiClient.getChecklistItems(params);\n                setItems(itemsData);\n                setError(null);\n            } catch (err) {\n                setError(err instanceof Error ? err.message : \"Unknown error\");\n                setItems([]);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchItems();\n    }, [\n        params.limit,\n        params.offset,\n        params.category,\n        params.phase,\n        params.search\n    ]);\n    return {\n        items,\n        loading,\n        error\n    };\n}\n// ================= Utility Functions =================\nfunction formatApiError(error) {\n    if (error instanceof Error) {\n        return error.message;\n    }\n    return \"An unknown error occurred\";\n}\nfunction isApiAvailable() {\n    return apiClient.getHealth().then(()=>true).catch(()=>false);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"30bc71fa7ec3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb21wbGlhbmNlbWF4Ly4vYXBwL2dsb2JhbHMuY3NzP2I4ZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzMGJjNzFmYTdlYzNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/navigation */ \"(rsc)/./components/navigation.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ComplianceMax - FEMA Compliance Management\",\n    description: \"Streamline your FEMA Public Assistance compliance processes\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex min-h-screen w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 md:ml-56 min-h-screen\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\CBCS\JUNE 2025 PROJECT COMPLIANCEMAX-V74\June_2025-ComplianceMax\ALL NEW APP\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,FileText,Home,Settings,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/home.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,FileText,Home,Settings,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,FileText,Home,Settings,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,FileText,Home,Settings,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/check-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,FileText,Home,Settings,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/file-text.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,FileText,Home,Settings,Shield,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/settings.mjs\");\n\n\n\nfunction Navigation() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-56 bg-[#141b2d] text-white p-4 fixed h-full hidden md:block\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-bold text-xl mb-6\",\n                    children: \"ComplianceMax\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/projects\",\n                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/compliance-wizard\",\n                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Compliance Wizard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/documents\",\n                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Documents\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/settings\",\n                            className: \"flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_FileText_Home_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CBCS\\\\JUNE 2025 PROJECT COMPLIANCEMAX-V74\\\\June_2025-ComplianceMax\\\\ALL NEW APP\\\\components\\\\navigation.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/navigation.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\CBCS\JUNE 2025 PROJECT COMPLIANCEMAX-V74\June_2025-ComplianceMax\ALL NEW APP\components\theme-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["ThemeProvider"];


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/next-themes"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMax%5CDocuments%5CCBCS%5CJUNE%202025%20PROJECT%20COMPLIANCEMAX-V74%5CJune_2025-ComplianceMax%5CALL%20NEW%20APP&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
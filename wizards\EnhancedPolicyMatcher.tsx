import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, Button, TextField,
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Chip, CircularProgress, Card, CardContent,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent,
  Alert, Snackbar, Tabs, Tab, IconButton, Dialog, DialogTitle,
  DialogContent, DialogContentText, DialogActions
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  ViewList as ViewListIcon
} from '@mui/icons-material';
import { policyMatcherService } from '../services/policyMatcherService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`policy-matcher-tabpanel-${index}`}
      aria-labelledby={`policy-matcher-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

interface MatchResult {
  id: number;
  requirement: string;
  policy: string;
  section: string;
  confidence: number;
}

interface Job {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  file_name: string;
  results?: MatchResult[];
}

const PolicyMatcher: React.FC = () => {
  // State for file upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [confidence, setConfidence] = useState('medium');
  const [algorithm, setAlgorithm] = useState('default');
  
  // State for job processing
  const [currentJob, setCurrentJob] = useState<Job | null>(null);
  const [jobHistory, setJobHistory] = useState<Job[]>([]);
  const [loading, setLoading] = useState(false);
  const [polling, setPolling] = useState<NodeJS.Timeout | null>(null);
  
  // UI state
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);
  
  // Load job history on component mount
  useEffect(() => {
    loadJobHistory();
    
    // Cleanup polling on unmount
    return () => {
      if (polling) {
        clearInterval(polling);
      }
    };
  }, []);
  
  // Poll for job status updates when there's an active job
  useEffect(() => {
    if (currentJob && (currentJob.status === 'pending' || currentJob.status === 'processing')) {
      const interval = setInterval(() => {
        checkJobStatus(currentJob.id);
      }, 3000);
      
      setPolling(interval);
      
      return () => {
        clearInterval(interval);
      };
    } else if (polling) {
      clearInterval(polling);
      setPolling(null);
    }
  }, [currentJob]);
  
  const loadJobHistory = async () => {
    try {
      setLoading(true);
      const response = await policyMatcherService.getMatchingHistory();
      setJobHistory(response.data.jobs);
      
      // If there's an active job, set it as current
      const activeJob = response.data.jobs.find(
        (job: Job) => job.status === 'pending' || job.status === 'processing'
      );
      
      if (activeJob) {
        setCurrentJob(activeJob);
      }
    } catch (err) {
      setError('Failed to load job history');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const checkJobStatus = async (jobId: string) => {
    try {
      const response = await policyMatcherService.getJobStatus(jobId);
      const updatedJob = response.data;
      
      // Update current job if it's the one we're checking
      if (currentJob && currentJob.id === jobId) {
        setCurrentJob(updatedJob);
        
        // If job completed, load results
        if (updatedJob.status === 'completed') {
          loadJobResults(jobId);
        } else if (updatedJob.status === 'failed') {
          setError('Job processing failed. Please try again.');
        }
      }
      
      // Update job in history
      setJobHistory(prevHistory => 
        prevHistory.map(job => job.id === jobId ? updatedJob : job)
      );
    } catch (err) {
      console.error(err);
    }
  };
  
  const loadJobResults = async (jobId: string) => {
    try {
      const response = await policyMatcherService.getJobResults(jobId);
      
      // Update current job with results
      setCurrentJob(prevJob => {
        if (prevJob && prevJob.id === jobId) {
          return {
            ...prevJob,
            results: response.data.results
          };
        }
        return prevJob;
      });
      
      // Update job in history
      setJobHistory(prevHistory => 
        prevHistory.map(job => {
          if (job.id === jobId) {
            return {
              ...job,
              results: response.data.results
            };
          }
          return job;
        })
      );
      
      setSuccess('Policy matching completed successfully');
    } catch (err) {
      setError('Failed to load job results');
      console.error(err);
    }
  };
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
    }
  };
  
  const handleConfidenceChange = (event: SelectChangeEvent) => {
    setConfidence(event.target.value);
  };
  
  const handleAlgorithmChange = (event: SelectChangeEvent) => {
    setAlgorithm(event.target.value);
  };
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleSubmit = async () => {
    if (!selectedFile) return;
    
    try {
      setLoading(true);
      setError('');
      
      // Map confidence level to threshold value
      const confidenceThreshold = 
        confidence === 'high' ? 0.8 :
        confidence === 'medium' ? 0.6 : 0.4;
      
      const response = await policyMatcherService.uploadRequirements(selectedFile, {
        confidenceThreshold,
        matchingAlgorithm: algorithm
      });
      
      const newJob = response.data;
      setCurrentJob(newJob);
      
      // Add new job to history
      setJobHistory(prevHistory => [newJob, ...prevHistory]);
      
      setSuccess('Document uploaded successfully. Processing started.');
      setSelectedFile(null);
    } catch (err) {
      setError('Failed to upload document. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleDownloadReport = async (jobId: string) => {
    try {
      setLoading(true);
      const response = await policyMatcherService.getJobReport(jobId);
      
      // Create a blob URL and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `policy-match-report-${jobId}.html`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      setSuccess('Report downloaded successfully');
    } catch (err) {
      setError('Failed to download report');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const openDeleteDialog = (jobId: string) => {
    setJobToDelete(jobId);
    setDeleteDialogOpen(true);
  };
  
  const handleDeleteJob = async () => {
    if (!jobToDelete) return;
    
    try {
      setLoading(true);
      await policyMatcherService.deleteJob(jobToDelete);
      
      // Remove job from history
      setJobHistory(prevHistory => 
        prevHistory.filter(job => job.id !== jobToDelete)
      );
      
      // If current job is deleted, clear it
      if (currentJob && currentJob.id === jobToDelete) {
        setCurrentJob(null);
      }
      
      setSuccess('Job deleted successfully');
    } catch (err) {
      setError('Failed to delete job');
      console.error(err);
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setJobToDelete(null);
    }
  };
  
  const handleViewJob = (job: Job) => {
    setCurrentJob(job);
    setTabValue(0); // Switch to results tab
  };
  
  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'success';
    if (score >= 0.6) return 'warning';
    return 'error';
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'pending': return 'info';
      case 'failed': return 'error';
      default: return 'default';
    }
  };
  
  const handleCloseSnackbar = () => {
    setError('');
    setSuccess('');
  };
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Policy Matcher
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Match Results" />
          <Tab label="Upload Document" />
          <Tab label="Job History" />
        </Tabs>
      </Paper>
      
      {/* Match Results Tab */}
      <TabPanel value={tabValue} index={0}>
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={currentJob ? 8 : 12}>
              <Typography variant="h6">
                {currentJob ? `Results for ${currentJob.file_name}` : 'No active job'}
              </Typography>
              {currentJob && (
                <Typography variant="body2" color="text.secondary">
                  Job ID: {currentJob.id} | Status: 
                  <Chip
                    label={currentJob.status}
                    color={getStatusColor(currentJob.status) as any}
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Typography>
              )}
            </Grid>
            {currentJob && (
              <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' } }}>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={() => handleDownloadReport(currentJob.id)}
                  disabled={currentJob.status !== 'completed'}
                  sx={{ mr: 1 }}
                >
                  Download Report
                </Button>
                <IconButton
                  color="error"
                  onClick={() => openDeleteDialog(currentJob.id)}
                >
                  <DeleteIcon />
                </IconButton>
              </Grid>
            )}
          </Grid>
        </Paper>
        
        {currentJob && currentJob.status === 'processing' && (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 4 }}>
            <CircularProgress size={60} />
            <Typography variant="h6" sx={{ mt: 2 }}>
              Processing your document...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This may take a few minutes depending on document size
            </Typography>
          </Box>
        )}
        
        {currentJob && currentJob.status === 'completed' && currentJob.results && (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Requirement</TableCell>
                  <TableCell>Policy</TableCell>
                  <TableCell>Section</TableCell>
                  <TableCell>Confidence</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentJob.results.length > 0 ? (
                  currentJob.results.map((row) => (
                    <TableRow key={row.id}>
                      <TableCell>{row.requirement}</TableCell>
                      <TableCell>{row.policy}</TableCell>
                      <TableCell>{row.section}</TableCell>
                      <TableCell>
                        <Chip
                          label={`${(row.confidence * 100).toFixed(0)}%`}
                          color={getConfidenceColor(row.confidence) as any}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      No matches found. Try adjusting your confidence threshold.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        
        {(!currentJob || currentJob.status === 'failed') && (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" gutterBottom>
                No active matching job
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Upload a document to start matching policies
              </Typography>
              <Button
                variant="contained"
                onClick={() => setTabValue(1)}
              >
                Upload Document
              </Button>
            </CardContent>
          </Card>
        )}
      </TabPanel>
      
      {/* Upload Document Tab */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={5}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Upload Requirements Document
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<UploadIcon />}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    Select Document
                    <input
                      type="file"
                      hidden
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={handleFileChange}
                    />
                  </Button>
                  
                  {selectedFile && (
                    <Typography variant="body2" color="text.secondary">
                      Selected: {selectedFile.name}
                    </Typography>
                  )}
                </Box>
                
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Confidence Threshold</InputLabel>
                  <Select
                    value={confidence}
                    label="Confidence Threshold"
                    onChange={handleConfidenceChange}
                  >
                    <MenuItem value="high">High (80%+)</MenuItem>
                    <MenuItem value="medium">Medium (60%+)</MenuItem>
                    <MenuItem value="low">Low (40%+)</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Matching Algorithm</InputLabel>
                  <Select
                    value={algorithm}
                    label="Matching Algorithm"
                    onChange={handleAlgorithmChange}
                  >
                    <MenuItem value="default">Default</MenuItem>
                    <MenuItem value="tfidf">TF-IDF</MenuItem>
                    <MenuItem value="semantic">Semantic Matching</MenuItem>
                  </Select>
                </FormControl>
                
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={handleSubmit}
                  disabled={!selectedFile || loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Match Policies'}
                </Button>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={7}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  How Policy Matching Works
                </Typography>
                
                <Typography variant="body2" paragraph>
                  The Policy Matcher analyzes your requirements document and automatically identifies matching policies in your organization's policy library.
                </Typography>
                
                <Typography variant="subtitle1" gutterBottom>
                  Supported Document Formats
                </Typography>
                <Typography variant="body2" paragraph>
                  • PDF (.pdf)
                  • Microsoft Word (.doc, .docx)
                  • Plain Text (.txt)
                </Typography>
                
                <Typography variant="subtitle1" gutterBottom>
                  Confidence Thresholds
                </Typography>
                <Typography variant="body2" paragraph>
                  • High: Only shows very strong matches (80%+ confidence)
                  • Medium: Shows moderate to strong matches (60%+ confidence)
                  • Low: Shows all potential matches (40%+ confidence)
                </Typography>
                
                <Typography variant="subtitle1" gutterBottom>
                  Matching Algorithms
                </Typography>
                <Typography variant="body2">
                  • Default: Balanced approach suitable for most documents
                  • TF-IDF: Better for technical documents with specific terminology
                  • Semantic: Better for understanding context and meaning
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
      
      {/* Job History Tab */}
      <TabPanel value={tabValue} index={2}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>File Name</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Matches</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jobHistory.length > 0 ? (
                jobHistory.map((job) => (
                  <TableRow key={job.id}>
                    <TableCell>{job.file_name}</TableCell>
                    <TableCell>{new Date(job.created_at).toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        label={job.status}
                        color={getStatusColor(job.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {job.results ? job.results.length : '-'}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        color="primary"
                        onClick={() => handleViewJob(job)}
                        disabled={job.status === 'failed'}
                      >
                        <ViewListIcon />
                      </IconButton>
                      <IconButton
                        color="primary"
                        onClick={() => handleDownloadReport(job.id)}
                        disabled={job.status !== 'completed'}
                      >
                        <DownloadIcon />
                      </IconButton>
                      <IconButton
                        color="error"
                        onClick={() => openDeleteDialog(job.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No job history found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <CircularProgress />
          </Box>
        )}
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button
            startIcon={<RefreshIcon />}
            onClick={loadJobHistory}
            disabled={loading}
          >
            Refresh History
          </Button>
        </Box>
      </TabPanel>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Job</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this job? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteJob} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Error and Success Snackbars */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
      
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PolicyMatcher;

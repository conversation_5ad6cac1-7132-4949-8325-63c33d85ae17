<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Work - ComplianceMax V74</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            min-height: 100vh;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .navbar {
            background: rgba(15, 23, 42, 0.95);
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            padding: 0.75rem 0;
        }
        
        .feature-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 0.75rem;
            padding: 1.25rem;
            transition: all 0.3s ease;
            height: auto;
            min-height: 200px;
        }
        
        .feature-card:hover {
            background: rgba(30, 41, 59, 1);
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateY(-1px);
        }
        
        .btn-primary {
            background: #ef4444;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .btn-primary:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(30, 41, 59, 0.9);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            border: 1px solid rgba(75, 85, 99, 0.5);
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }
        
        .btn-secondary:hover {
            background: rgba(75, 85, 99, 0.5);
        }

        .emergency-badge {
            background: #ef4444;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .main-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .warning-card {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin-top: 1rem;
        }

        .intake-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .intake-modal.active {
            display: flex;
        }

        .intake-content {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 1rem;
            padding: 1.5rem;
            max-width: 500px;
            width: 90%;
            max-height: 85vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            color: white;
            margin-bottom: 0.25rem;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 0.5rem;
            background: rgba(15, 23, 42, 0.8);
            color: white;
            font-size: 0.875rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
        }

        .compact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .category-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-card:hover {
            border-color: rgba(239, 68, 68, 0.6);
            background: rgba(30, 41, 59, 1);
            transform: translateY(-1px);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 350px;
            font-size: 0.875rem;
        }
        
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .notification.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        
        .notification.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        
        .notification.info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="max-w-7xl mx-auto px-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <span class="text-white font-bold text-lg">CM</span>
                </div>
                <h1 class="text-xl font-bold">ComplianceMax</h1>
                <span class="emergency-badge">Emergency Work</span>
            </div>
            <div class="flex space-x-6">
                <a href="/dashboard" class="text-gray-300 hover:text-white transition-colors">Home</a>
                <a href="/emergency" class="text-red-400 font-semibold">Emergency Work</a>
                <a href="/cbcs" class="text-gray-300 hover:text-white transition-colors">CBCS Work</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 py-6">
        <!-- Header Section -->
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold mb-2">🚨 Emergency Work Intake</h1>
            <p class="text-gray-300 text-lg">Categories A & B - Immediate Response Required</p>
        </div>

        <!-- Emergency Categories -->
        <div class="compact-grid">
            <div class="category-card" onclick="startIntake('A')">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-lg font-bold text-red-400">Category A</h3>
                    <span class="emergency-badge">Immediate</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">Debris removal from improved public property</p>
                <ul class="text-xs text-gray-400 space-y-1">
                    <li>• Roads, bridges, culverts</li>
                    <li>• Public buildings, facilities</li>
                    <li>• Parks, recreational areas</li>
                </ul>
                <button class="btn-primary w-full mt-3">Start Category A Intake</button>
            </div>

            <div class="category-card" onclick="startIntake('B')">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-lg font-bold text-red-400">Category B</h3>
                    <span class="emergency-badge">Critical</span>
                </div>
                <p class="text-sm text-gray-300 mb-3">Emergency protective measures</p>
                <ul class="text-xs text-gray-400 space-y-1">
                    <li>• Life safety measures</li>
                    <li>• Property protection</li>
                    <li>• Public health/safety</li>
                </ul>
                <button class="btn-primary w-full mt-3">Start Category B Intake</button>
            </div>
        </div>

        <!-- Quick Info -->
        <div class="main-card">
            <h3 class="text-lg font-semibold mb-3 text-red-400">🚨 Emergency Work Benefits</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                    <strong class="text-green-400">✅ No CBCS Required</strong>
                    <p class="text-gray-400">Emergency work is exempt from consensus-based codes</p>
                </div>
                <div>
                    <strong class="text-green-400">✅ Immediate Authorization</strong>
                    <p class="text-gray-400">Work can begin immediately for life safety</p>
                </div>
                <div>
                    <strong class="text-green-400">✅ Streamlined Process</strong>
                    <p class="text-gray-400">Reduced documentation requirements</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Intake Modal -->
    <div id="intakeModal" class="intake-modal">
        <div class="intake-content">
            <h3 id="modalTitle" class="text-xl font-bold mb-4 text-red-400">Emergency Work Intake</h3>
            
            <form id="intakeForm">
                <input type="hidden" id="workCategory" name="workCategory" value="">
                
                <div class="form-group">
                    <label for="projectName">Project Name *</label>
                    <input type="text" id="projectName" name="projectName" required>
                </div>
                
                <div class="form-group">
                    <label for="urgencyLevel">Urgency Level *</label>
                    <select id="urgencyLevel" name="urgencyLevel" required>
                        <option value="">Select urgency</option>
                        <option value="immediate">Immediate (Life safety threat)</option>
                        <option value="critical">Critical (Infrastructure failure imminent)</option>
                        <option value="high">High (Public health/safety risk)</option>
                        <option value="medium">Medium (Property protection)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="workDescription">Emergency Description *</label>
                    <textarea id="workDescription" name="workDescription" rows="3" placeholder="Describe the emergency situation and required work" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="incidentDate">Incident Date *</label>
                    <input type="date" id="incidentDate" name="incidentDate" required>
                </div>

                <div class="form-group">
                    <label for="attachments">Supporting Documents</label>
                    <input type="file" id="attachments" name="attachments" multiple accept=".pdf,.doc,.docx,.jpg,.png,.tiff">
                    <div class="text-xs text-gray-400 mt-1">Photos, damage assessments, cost estimates</div>
                </div>
                
                <div class="flex space-x-3 mt-4">
                    <button type="button" onclick="closeIntake()" class="btn-secondary flex-1">Cancel</button>
                    <button type="submit" class="btn-primary flex-1">Submit Emergency Intake</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showNotification(message, type = 'info') {
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }
        
        function startIntake(category) {
            document.getElementById('intakeModal').classList.add('active');
            document.getElementById('modalTitle').textContent = `Category ${category} - Emergency Work Intake`;
            document.getElementById('workCategory').value = category;
        }

        function closeIntake() {
            document.getElementById('intakeModal').classList.remove('active');
            document.getElementById('intakeForm').reset();
        }

        async function uploadSelectedFiles() {
            const fileInput = document.getElementById('attachments');
            if (!fileInput.files.length) return [];

            const uploadPromises = Array.from(fileInput.files).map(async (file) => {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await fetch('/api/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        return result.file_id;
                    }
                } catch (error) {
                    console.error('Upload error:', error);
                }
                return null;
            });

            const results = await Promise.all(uploadPromises);
            return results.filter(id => id !== null);
        }

        document.getElementById('intakeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const uploadedIds = await uploadSelectedFiles();

            const formData = {
                projectName: document.getElementById('projectName').value,
                workCategory: document.getElementById('workCategory').value,
                urgencyLevel: document.getElementById('urgencyLevel').value,
                workDescription: document.getElementById('workDescription').value,
                incidentDate: document.getElementById('incidentDate').value,
                attachments: uploadedIds,
                start_wizard: true
            };

            try {
                const response = await fetch('/api/intake/emergency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.status === 'success') {
                    showNotification(`Emergency intake submitted! ID: ${result.intake_id}`, 'success');
                    closeIntake();
                    
                    if (result.wizard_session) {
                        setTimeout(() => {
                            showNotification('Launching compliance wizard...', 'info');
                        }, 1000);
                    }
                } else {
                    showNotification('Error: ' + result.message, 'error');
                }
            } catch (error) {
                showNotification('Error submitting intake: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html> 
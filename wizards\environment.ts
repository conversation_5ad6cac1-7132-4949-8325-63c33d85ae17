/**
 * ComplianceMax Environment Configuration
 * Production-ready environment management
 */

export interface EnvironmentConfig {
  // Database
  database: {
    url: string;
    maxConnections: number;
    ssl: boolean;
  };
  
  // Redis Cache
  redis: {
    url: string;
    maxRetries: number;
    retryDelay: number;
  };
  
  // Application
  app: {
    port: number;
    host: string;
    nodeEnv: 'development' | 'staging' | 'production';
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
  
  // Security
  security: {
    jwtSecret: string;
    sessionSecret: string;
    corsOrigin: string[];
    rateLimitWindow: number;
    rateLimitMax: number;
  };
  
  // FEMA Integration
  fema: {
    apiUrl: string;
    apiKey?: string;
    retryAttempts: number;
    timeoutMs: number;
  };
  
  // Document Processing
  docling: {
    ocrEnabled: boolean;
    maxFileSize: number;
    allowedTypes: string[];
    processingTimeout: number;
  };
}

/**
 * Get environment configuration with validation
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const nodeEnv = (process.env.NODE_ENV || 'development') as 'development' | 'staging' | 'production';
  
  // Validate required environment variables
  const requiredVars = [
    'DATABASE_URL',
    'REDIS_URL',
    'NEXTAUTH_SECRET'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
  
  return {
    database: {
      url: process.env.DATABASE_URL!,
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
      ssl: process.env.DB_SSL === 'true' || nodeEnv === 'production'
    },
    
    redis: {
      url: process.env.REDIS_URL!,
      maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.REDIS_RETRY_DELAY || '1000')
    },
    
    app: {
      port: parseInt(process.env.PORT || '3333'),
      host: process.env.HOSTNAME || '0.0.0.0',
      nodeEnv,
      logLevel: (process.env.LOG_LEVEL || 'info') as any
    },
    
    security: {
      jwtSecret: process.env.NEXTAUTH_SECRET!,
      sessionSecret: process.env.SESSION_SECRET || process.env.NEXTAUTH_SECRET!,
      corsOrigin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3333'],
      rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
      rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100')
    },
    
    fema: {
      apiUrl: process.env.FEMA_API_URL || 'https://www.fema.gov/api',
      apiKey: process.env.FEMA_API_KEY,
      retryAttempts: parseInt(process.env.FEMA_RETRY_ATTEMPTS || '3'),
      timeoutMs: parseInt(process.env.FEMA_TIMEOUT_MS || '30000')
    },
    
    docling: {
      ocrEnabled: process.env.DOCLING_OCR_ENABLED !== 'false',
      maxFileSize: parseInt(process.env.DOCLING_MAX_FILE_SIZE || '50000000'), // 50MB
      allowedTypes: process.env.DOCLING_ALLOWED_TYPES?.split(',') || [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png',
        'text/plain'
      ],
      processingTimeout: parseInt(process.env.DOCLING_TIMEOUT || '300000') // 5 minutes
    }
  };
}

/**
 * Validate environment configuration
 */
export function validateEnvironment(): void {
  try {
    const config = getEnvironmentConfig();
    console.log('✅ Environment configuration validated successfully');
    console.log(`🚀 Starting ComplianceMax in ${config.app.nodeEnv} mode`);
    console.log(`📊 Database: ${config.database.url.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`);
    console.log(`⚡ Redis: ${config.redis.url.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`);
    console.log(`🌐 Port: ${config.app.port}`);
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    process.exit(1);
  }
}

/**
 * Environment-specific configurations
 */
export const environmentConfigs = {
  development: {
    debug: true,
    hotReload: true,
    verboseLogging: true,
    mockExternalAPIs: false
  },
  
  staging: {
    debug: false,
    hotReload: false,
    verboseLogging: true,
    mockExternalAPIs: false
  },
  
  production: {
    debug: false,
    hotReload: false,
    verboseLogging: false,
    mockExternalAPIs: false
  }
};

// Export singleton instance
export const env = getEnvironmentConfig(); 
<% if (strict) { %>import { StrictMode } from 'react';<% } %>
import * as ReactDOM from 'react-dom/client';
<% if (routing) { %>import { BrowserRouter } from 'react-router-dom';<% } %>

import App from './app/<%= fileName %>';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
<%_ if(strict && !routing) { _%>
root.render(
  <StrictMode>
    <App/>
  </StrictMode>
)
<%_ } _%>
<%_ if(!strict && routing) { _%>
root.render(
  <BrowserRouter>
    <App/>
  </BrowserRouter>
)
<%_ } _%>
<%_ if(strict && routing) { _%>
root.render(
  <StrictMode>
    <BrowserRouter>
      <App/>
    </BrowserRouter>
  </StrictMode>
)
<%_ } _%>

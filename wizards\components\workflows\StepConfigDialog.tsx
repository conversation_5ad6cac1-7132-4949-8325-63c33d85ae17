import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  MenuItem,
  Typography,
  Box,
} from '@mui/material';
import { WorkflowStep, ActionType } from '../../types/workflow';
import { validateStep } from '../../services/validation';

interface StepConfigDialogProps {
  open: boolean;
  step: WorkflowStep | null;
  onClose: () => void;
  onSave: (step: WorkflowStep) => void;
}

const StepConfigDialog: React.FC<StepConfigDialogProps> = ({
  open,
  step,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState<Partial<WorkflowStep>>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (step) {
      setFormData(step);
    }
  }, [step]);

  const handleChange = (field: keyof WorkflowStep) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  const handleActionConfigChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData({
      ...formData,
      action_config: {
        ...formData.action_config,
        [field]: event.target.value,
      },
    });
  };

  const handleSave = () => {
    try {
      const updatedStep = {
        ...step,
        ...formData,
      } as WorkflowStep;
      validateStep(updatedStep, 0);
      onSave(updatedStep);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Invalid step configuration');
    }
  };

  const renderActionConfig = () => {
    if (!formData.action_type) return null;

    switch (formData.action_type) {
      case ActionType.HTTP:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="URL"
                value={formData.action_config?.url || ''}
                onChange={handleActionConfigChange('url')}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Method"
                select
                value={formData.action_config?.method || 'GET'}
                onChange={handleActionConfigChange('method')}
                required
              >
                {['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].map((method) => (
                  <MenuItem key={method} value={method}>
                    {method}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Headers"
                multiline
                rows={4}
                value={formData.action_config?.headers || ''}
                onChange={handleActionConfigChange('headers')}
                placeholder="JSON format"
              />
            </Grid>
          </Grid>
        );

      case ActionType.EMAIL:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="To"
                value={formData.action_config?.to || ''}
                onChange={handleActionConfigChange('to')}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Subject"
                value={formData.action_config?.subject || ''}
                onChange={handleActionConfigChange('subject')}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Body"
                multiline
                rows={4}
                value={formData.action_config?.body || ''}
                onChange={handleActionConfigChange('body')}
                required
              />
            </Grid>
          </Grid>
        );

      case ActionType.DATABASE:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Database Type"
                select
                value={formData.action_config?.databaseType || 'mongodb'}
                onChange={handleActionConfigChange('databaseType')}
                required
              >
                {['mongodb', 'sql'].map((type) => (
                  <MenuItem key={type} value={type}>
                    {type.toUpperCase()}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Operation"
                select
                value={formData.action_config?.operation || 'find'}
                onChange={handleActionConfigChange('operation')}
                required
              >
                {['find', 'insert', 'update', 'delete'].map((op) => (
                  <MenuItem key={op} value={op}>
                    {op.charAt(0).toUpperCase() + op.slice(1)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Query"
                multiline
                rows={4}
                value={formData.action_config?.query || ''}
                onChange={handleActionConfigChange('query')}
                required
                placeholder="JSON format for MongoDB, SQL for SQL databases"
              />
            </Grid>
          </Grid>
        );

      case ActionType.FILE:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Operation"
                select
                value={formData.action_config?.operation || 'read'}
                onChange={handleActionConfigChange('operation')}
                required
              >
                {['read', 'write', 'delete', 'move', 'copy'].map((op) => (
                  <MenuItem key={op} value={op}>
                    {op.charAt(0).toUpperCase() + op.slice(1)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Source Path"
                value={formData.action_config?.sourcePath || ''}
                onChange={handleActionConfigChange('sourcePath')}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Destination Path"
                value={formData.action_config?.destinationPath || ''}
                onChange={handleActionConfigChange('destinationPath')}
                required={['move', 'copy'].includes(formData.action_config?.operation || '')}
              />
            </Grid>
          </Grid>
        );

      case ActionType.NOTIFICATION:
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Type"
                select
                value={formData.action_config?.type || 'slack'}
                onChange={handleActionConfigChange('type')}
                required
              >
                {['slack', 'teams', 'webhook', 'custom'].map((type) => (
                  <MenuItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Message"
                multiline
                rows={4}
                value={formData.action_config?.message || ''}
                onChange={handleActionConfigChange('message')}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook URL"
                value={formData.action_config?.webhookUrl || ''}
                onChange={handleActionConfigChange('webhookUrl')}
                required={['slack', 'teams', 'webhook'].includes(formData.action_config?.type || '')}
              />
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Configure Step</DialogTitle>
      <DialogContent>
        {error && (
          <Typography color="error" sx={{ mb: 2 }}>
            {error}
          </Typography>
        )}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Step Name"
              value={formData.name || ''}
              onChange={handleChange('name')}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Type"
              select
              value={formData.type || 'action'}
              onChange={handleChange('type')}
              required
            >
              <MenuItem value="action">Action</MenuItem>
              <MenuItem value="condition">Condition</MenuItem>
            </TextField>
          </Grid>
          {formData.type === 'action' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Action Type"
                select
                value={formData.action_type || ''}
                onChange={handleChange('action_type')}
                required
              >
                {Object.values(ActionType).map((type) => (
                  <MenuItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
          )}
          {formData.type === 'action' && renderActionConfig()}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained" color="primary">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StepConfigDialog; 
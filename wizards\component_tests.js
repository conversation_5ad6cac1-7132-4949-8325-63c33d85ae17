import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { act } from 'react-dom/test-utils';
import { MockedProvider } from '@apollo/client/testing';

// Import components to test
import PolicyMatcher from '../EnhancedPolicyMatcher';
import DataVisualization from '../DataVisualization';
import ExportFunctionality from '../ExportFunctionality';
import NotificationSystem from '../NotificationSystem';
import EnhancedDashboard from '../EnhancedDashboard';
import ExternalSystemsIntegration from '../ExternalSystemsIntegration';

// Mock the API service
jest.mock('../services/policyMatcherService', () => ({
  uploadRequirements: jest.fn().mockResolvedValue({ 
    data: { id: 'job123', status: 'pending', created_at: '2025-04-13T10:00:00Z', file_name: 'test.pdf' }
  }),
  getJobStatus: jest.fn().mockResolvedValue({
    data: { id: 'job123', status: 'completed', created_at: '2025-04-13T10:00:00Z', file_name: 'test.pdf' }
  }),
  getJobResults: jest.fn().mockResolvedValue({
    data: { 
      results: [
        { id: 1, requirement: 'Test requirement', policy: 'Test policy', section: '1.1', confidence: 0.85 }
      ]
    }
  }),
  getJobReport: jest.fn().mockResolvedValue({
    data: '<html><body>Test Report</body></html>'
  }),
  getMatchingHistory: jest.fn().mockResolvedValue({
    data: { 
      jobs: [
        { id: 'job123', status: 'completed', created_at: '2025-04-13T10:00:00Z', file_name: 'test.pdf' }
      ]
    }
  }),
  deleteJob: jest.fn().mockResolvedValue({ data: { message: 'Job deleted successfully' } }),
}));

// Mock window.URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'blob:test');

describe('PolicyMatcher Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders policy matcher component', () => {
    render(
      <MockedProvider>
        <PolicyMatcher />
      </MockedProvider>
    );
    
    expect(screen.getByText('Policy Matcher')).toBeInTheDocument();
    expect(screen.getByText('Upload Document')).toBeInTheDocument();
  });

  test('handles file selection', async () => {
    render(
      <MockedProvider>
        <PolicyMatcher />
      </MockedProvider>
    );
    
    // Navigate to upload tab
    const uploadTab = screen.getByText('Upload Document');
    fireEvent.click(uploadTab);
    
    // Mock file selection
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = screen.getByText('Select Document').closest('label').querySelector('input');
    
    await act(async () => {
      fireEvent.change(fileInput, { target: { files: [file] } });
    });
    
    // Check if file name appears
    expect(screen.getByText('Selected: test.pdf')).toBeInTheDocument();
  });

  // Additional tests would be implemented here
});

describe('DataVisualization Component', () => {
  test('renders data visualization component', () => {
    render(<DataVisualization />);
    
    expect(screen.getByText('Data Visualization')).toBeInTheDocument();
    expect(screen.getByText('Chart Type')).toBeInTheDocument();
  });

  test('changes chart type', () => {
    render(<DataVisualization />);
    
    const chartTypeSelect = screen.getByLabelText('Chart Type');
    fireEvent.mouseDown(chartTypeSelect);
    
    const pieChartOption = screen.getByText('Pie Chart');
    fireEvent.click(pieChartOption);
    
    // Additional assertions would verify the chart changed
  });

  // Additional tests would be implemented here
});

describe('ExportFunctionality Component', () => {
  test('renders export functionality component', () => {
    render(<ExportFunctionality />);
    
    expect(screen.getByText('Export Reports')).toBeInTheDocument();
    expect(screen.getByText('Available Reports')).toBeInTheDocument();
  });

  test('selects reports for export', () => {
    render(<ExportFunctionality />);
    
    // Select the first report
    const firstReportRow = screen.getByText('Quarterly Compliance Report Q1 2025').closest('tr');
    fireEvent.click(firstReportRow);
    
    // Check if the row is selected
    expect(firstReportRow).toHaveClass('Mui-selected');
    
    // Check if export button is enabled
    const exportButton = screen.getByText(/Export 1 Report/);
    expect(exportButton).not.toBeDisabled();
  });

  // Additional tests would be implemented here
});

describe('NotificationSystem Component', () => {
  test('renders notification system component', () => {
    render(<NotificationSystem />);
    
    expect(screen.getByText('Notification System')).toBeInTheDocument();
    expect(screen.getByText(/Notifications/)).toBeInTheDocument();
  });

  test('marks notification as read', () => {
    render(<NotificationSystem />);
    
    // Find an unread notification
    const unreadNotification = screen.getByText('Policy Match Complete');
    
    // Find and click the mark as read button for this notification
    const markAsReadButton = unreadNotification.closest('li').querySelector('button');
    fireEvent.click(markAsReadButton);
    
    // Additional assertions would verify the notification was marked as read
  });

  // Additional tests would be implemented here
});

describe('EnhancedDashboard Component', () => {
  test('renders enhanced dashboard component', () => {
    render(<EnhancedDashboard />);
    
    expect(screen.getByText('Enhanced Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Compliance Trends')).toBeInTheDocument();
  });

  test('changes time range', async () => {
    render(<EnhancedDashboard />);
    
    const timeRangeSelect = screen.getByLabelText('Time Range');
    fireEvent.mouseDown(timeRangeSelect);
    
    const lastMonthOption = screen.getByText('Last Month');
    fireEvent.click(lastMonthOption);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
    
    // Additional assertions would verify the data changed
  });

  // Additional tests would be implemented here
});

describe('ExternalSystemsIntegration Component', () => {
  test('renders external systems integration component', () => {
    render(<ExternalSystemsIntegration />);
    
    expect(screen.getByText('External Systems Integration')).toBeInTheDocument();
    expect(screen.getByText('FEMA Database')).toBeInTheDocument();
  });

  test('searches FEMA database', () => {
    render(<ExternalSystemsIntegration />);
    
    // Enter search query
    const searchInput = screen.getByPlaceholderText('Search disasters by ID, name, or state');
    fireEvent.change(searchInput, { target: { value: 'Hurricane' } });
    
    // Click search button
    const searchButton = screen.getByText('Search').closest('button');
    fireEvent.click(searchButton);
    
    // Additional assertions would verify search results
  });

  // Additional tests would be implemented here
});

// Integration tests
describe('Integration Tests', () => {
  test('policy matcher triggers notification', async () => {
    // This would test if completing a policy match job creates a notification
    // Would require rendering both components and simulating the complete workflow
  });

  test('export functionality works with policy matcher results', async () => {
    // This would test if policy matcher results can be exported
    // Would require rendering both components and simulating the complete workflow
  });

  // Additional integration tests would be implemented here
});

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stepper,
  Step,
  Step<PERSON><PERSON>l,
  Button,
  CircularProgress,
  useTheme,
} from '@mui/material';
import ProjectDetails from './steps/ProjectDetails';
// CONSOLIDATED: Use master document upload system
// import DocumentUpload from './steps/DocumentUpload';
import PolicySelection from './steps/PolicySelection';
import ReviewSummary from './steps/ReviewSummary';

// Master Document Upload Integration Component
const MasterDocumentUpload: React.FC<{ documents: any[]; onUpdate: (docs: any[]) => void }> = ({ documents, onUpdate }) => {
  const handleRedirectToMaster = () => {
    // Redirect to master document upload system with return context
    const currentUrl = encodeURIComponent(window.location.href);
    window.location.href = `document_upload_system.html?return=${currentUrl}&context=compliance-review`;
  };

  return (
    <Box sx={{ textAlign: 'center', py: 4 }}>
      <Typography variant="h6" gutterBottom>
        Document Upload & Analysis
      </Typography>
      <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
        Use our comprehensive document upload system with AI-powered analysis and pod routing
      </Typography>
      <Button
        variant="contained"
        size="large"
        onClick={handleRedirectToMaster}
        sx={{ mb: 2 }}
      >
        📄 Open Document Upload System
      </Button>
      <Typography variant="caption" display="block">
        Includes: Pod routing • Compliance analysis • FEMA workbook integration
      </Typography>
    </Box>
  );
};

const steps = [
  'Project Details',
  'Document Upload',
  'Policy Selection',
  'Review Summary',
];

const ComplianceReview: React.FC = () => {
  const theme = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    projectDetails: null,
    documents: [],
    selectedPolicies: [],
  });

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // TODO: Implement submission logic
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulated delay
      handleNext();
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <ProjectDetails
            data={formData.projectDetails}
            onUpdate={(data) =>
              setFormData((prev) => ({ ...prev, projectDetails: data }))
            }
          />
        );
      case 1:
        return (
          <MasterDocumentUpload
            documents={formData.documents}
            onUpdate={(docs) =>
              setFormData((prev) => ({ ...prev, documents: docs }))
            }
          />
        );
      case 2:
        return (
          <PolicySelection
            selectedPolicies={formData.selectedPolicies}
            onUpdate={(policies) =>
              setFormData((prev) => ({ ...prev, selectedPolicies: policies }))
            }
          />
        );
      case 3:
        return <ReviewSummary data={formData} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto' }}>
      <Typography
        variant="h4"
        sx={{
          mb: 4,
          color: '#fff',
          textAlign: 'center',
          fontWeight: 600,
        }}
      >
        Compliance Review
      </Typography>

      <Paper
        sx={{
          p: 4,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: theme.shape.borderRadius * 2,
        }}
      >
        <Stepper
          activeStep={activeStep}
          sx={{
            mb: 4,
            '& .MuiStepLabel-label': {
              color: 'rgba(255, 255, 255, 0.7)',
              '&.Mui-active': {
                color: theme.palette.primary.main,
              },
              '&.Mui-completed': {
                color: theme.palette.success.main,
              },
            },
          }}
        >
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <Box sx={{ mt: 4 }}>{renderStepContent(activeStep)}</Box>

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={handleBack}
            disabled={activeStep === 0 || isSubmitting}
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              borderColor: 'rgba(255, 255, 255, 0.7)',
              '&:hover': {
                borderColor: '#fff',
                color: '#fff',
              },
            }}
          >
            Back
          </Button>
          <Button
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
            disabled={isSubmitting}
            sx={{
              minWidth: 120,
            }}
          >
            {isSubmitting ? (
              <CircularProgress size={24} color="inherit" />
            ) : activeStep === steps.length - 1 ? (
              'Submit'
            ) : (
              'Next'
            )}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default ComplianceReview;

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Alert,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Schedule, ScheduleFrequency, ScheduleRecurrence } from '../../types/schedule';

interface ScheduleFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (schedule: Partial<Schedule>) => Promise<void>;
  initialData?: Schedule | null;
  workflowId?: string;
}

const ScheduleForm: React.FC<ScheduleFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialData,
  workflowId,
}) => {
  const [formData, setFormData] = useState<Partial<Schedule>>({
    name: '',
    description: '',
    start_time: new Date(),
    recurrence: {
      frequency: ScheduleFrequency.DAILY,
      interval: 1,
      timezone: 'UTC',
    },
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    } else {
      setFormData({
        name: '',
        description: '',
        start_time: new Date(),
        workflow_id: workflowId,
        recurrence: {
          frequency: ScheduleFrequency.DAILY,
          interval: 1,
          timezone: 'UTC',
        },
      });
    }
  }, [initialData, workflowId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onSubmit(formData);
    } catch (err) {
      setError('Failed to save schedule');
    }
  };

  const handleRecurrenceChange = (field: keyof ScheduleRecurrence, value: any) => {
    setFormData((prev) => ({
      ...prev,
      recurrence: {
        ...prev.recurrence,
        [field]: value,
      },
    }));
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {initialData ? 'Edit Schedule' : 'New Schedule'}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="Name"
                fullWidth
                required
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                fullWidth
                multiline
                rows={2}
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, description: e.target.value }))
                }
              />
            </Grid>
            <Grid item xs={12}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="Start Time"
                  value={formData.start_time}
                  onChange={(date) =>
                    setFormData((prev) => ({ ...prev, start_time: date }))
                  }
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Frequency</InputLabel>
                <Select
                  value={formData.recurrence?.frequency}
                  onChange={(e) =>
                    handleRecurrenceChange('frequency', e.target.value)
                  }
                >
                  <MenuItem value={ScheduleFrequency.ONCE}>Once</MenuItem>
                  <MenuItem value={ScheduleFrequency.DAILY}>Daily</MenuItem>
                  <MenuItem value={ScheduleFrequency.WEEKLY}>Weekly</MenuItem>
                  <MenuItem value={ScheduleFrequency.MONTHLY}>Monthly</MenuItem>
                  <MenuItem value={ScheduleFrequency.CRON}>Custom (Cron)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Interval"
                type="number"
                fullWidth
                value={formData.recurrence?.interval}
                onChange={(e) =>
                  handleRecurrenceChange('interval', parseInt(e.target.value))
                }
                disabled={formData.recurrence?.frequency === ScheduleFrequency.ONCE}
              />
            </Grid>
            {formData.recurrence?.frequency === ScheduleFrequency.WEEKLY && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Days of Week</InputLabel>
                  <Select
                    multiple
                    value={formData.recurrence?.days_of_week || []}
                    onChange={(e) =>
                      handleRecurrenceChange('days_of_week', e.target.value)
                    }
                  >
                    <MenuItem value={0}>Sunday</MenuItem>
                    <MenuItem value={1}>Monday</MenuItem>
                    <MenuItem value={2}>Tuesday</MenuItem>
                    <MenuItem value={3}>Wednesday</MenuItem>
                    <MenuItem value={4}>Thursday</MenuItem>
                    <MenuItem value={5}>Friday</MenuItem>
                    <MenuItem value={6}>Saturday</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            {formData.recurrence?.frequency === ScheduleFrequency.MONTHLY && (
              <Grid item xs={12}>
                <TextField
                  label="Day of Month"
                  type="number"
                  fullWidth
                  value={formData.recurrence?.day_of_month}
                  onChange={(e) =>
                    handleRecurrenceChange(
                      'day_of_month',
                      parseInt(e.target.value)
                    )
                  }
                />
              </Grid>
            )}
            {formData.recurrence?.frequency === ScheduleFrequency.CRON && (
              <Grid item xs={12}>
                <TextField
                  label="Cron Expression"
                  fullWidth
                  value={formData.recurrence?.cron_expression}
                  onChange={(e) =>
                    handleRecurrenceChange('cron_expression', e.target.value)
                  }
                  helperText="Example: 0 0 * * * (runs daily at midnight)"
                />
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained" color="primary">
            {initialData ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ScheduleForm; 
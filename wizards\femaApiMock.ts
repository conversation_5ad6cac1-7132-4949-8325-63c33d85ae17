// femaApiMock.ts (New file for mock FEMA API integration)
import { config } from "../config/config";

// Mock API fetch (in real app, replace with actual fetch to FEMA OpenFEMA or Federal Register API)
export const fetchFemaData = async (): Promise<{ largeProjectThreshold: number; pappgVersion: string }> => {
  // Simulate API call; in production, fetch from 'https://www.fema.gov/api/open/v1/PublicAssistance...' or similar
  // For now, return config values with a delay to mimic async
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        largeProjectThreshold: config.largeProjectThreshold,
        pappgVersion: config.pappgVersion,
      });
    }, 500);
  });
};
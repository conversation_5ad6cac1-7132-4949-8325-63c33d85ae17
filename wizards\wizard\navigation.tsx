import Link from "next/link";
import { Shield, FileText, CheckCircle, Users, Settings, Home } from "lucide-react";

export function Navigation() {
  return (
    <div className="w-56 bg-[#141b2d] text-white p-4 fixed h-full hidden md:block">
      <div className="py-4">
        <h2 className="font-bold text-xl mb-6">ComplianceMax</h2>
        <nav className="space-y-2">
          <Link href="/" className="flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]">
            <Home className="h-4 w-4" />
            <span>Home</span>
          </Link>
          <Link href="/dashboard" className="flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]">
            <Shield className="h-4 w-4" />
            <span>Dashboard</span>
          </Link>
          <Link href="/projects" className="flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]">
            <Users className="h-4 w-4" />
            <span>Projects</span>
          </Link>
          <Link href="/compliance-wizard" className="flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]">
            <CheckCircle className="h-4 w-4" />
            <span>Compliance Wizard</span>
          </Link>
          <Link href="/documents" className="flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]">
            <FileText className="h-4 w-4" />
            <span>Documents</span>
          </Link>
          <Link href="/settings" className="flex items-center space-x-2 p-2 rounded hover:bg-[#1e2a45]">
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </Link>
        </nav>
      </div>
    </div>
  );
} 
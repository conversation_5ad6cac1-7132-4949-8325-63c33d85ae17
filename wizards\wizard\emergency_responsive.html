{% extends "base_responsive.html" %}

{% block title %}Emergency Work (A&B) - ComplianceMax V74{% endblock %}

{% block page_title %}Emergency Work (Categories A & B){% endblock %}

{% block page_subtitle %}Debris Removal & Emergency Protective Measures{% endblock %}

{% block content %}
<div class="emergency-section">
    <div class="emergency-header">
        🚨 Emergency Work Intake
    </div>
    
    <form class="emergency-form" id="emergencyForm" onsubmit="handleEmergencySubmit(event)">
        <div class="grid grid-2">
            <div class="form-group">
                <label for="applicant-name">Applicant Name *</label>
                <input type="text" id="applicant-name" name="applicant_name" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="disaster-number">Disaster Number *</label>
                <input type="text" id="disaster-number" name="disaster_number" class="form-control" 
                       placeholder="e.g., DR-4xxx-XX" required>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="form-group">
                <label for="category">Emergency Category *</label>
                <select id="category" name="category" class="form-control" required onchange="updateCategoryInfo()">
                    <option value="">Select Category</option>
                    <option value="A">Category A - Debris Removal</option>
                    <option value="B">Category B - Emergency Protective Measures</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="estimated-cost">Estimated Cost *</label>
                <input type="number" id="estimated-cost" name="estimated_cost" class="form-control" 
                       min="0" step="0.01" required>
            </div>
        </div>

        <div class="form-group">
            <label for="work-description">Work Description *</label>
            <textarea id="work-description" name="work_description" class="form-control" 
                      rows="3" required placeholder="Describe the emergency work performed..."></textarea>
        </div>

        <div class="grid grid-2">
            <div class="form-group">
                <label for="incident-date">Incident Date *</label>
                <input type="date" id="incident-date" name="incident_date" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="completion-date">Expected Completion</label>
                <input type="date" id="completion-date" name="completion_date" class="form-control">
            </div>
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-block">Generate Emergency Work Documentation</button>
        </div>
    </form>
</div>

<!-- Category Information Panel -->
<div id="category-info" class="emergency-section" style="display: none;">
    <div class="emergency-header">
        📋 Category Requirements
    </div>
    <div id="category-details">
        <!-- Dynamic content will be loaded here -->
    </div>
</div>

<!-- Results Panel -->
<div id="results-panel" class="emergency-section" style="display: none;">
    <div class="emergency-header">
        📄 Generated Documentation
    </div>
    <div id="results-content">
        <!-- Results will be displayed here -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateCategoryInfo() {
    const category = document.getElementById('category').value;
    const infoPanel = document.getElementById('category-info');
    const detailsDiv = document.getElementById('category-details');
    
    if (category) {
        infoPanel.style.display = 'block';
        
        const categoryInfo = {
            'A': {
                title: 'Category A - Debris Removal',
                requirements: [
                    'Document debris location and type',
                    'Estimate cubic yards of debris',
                    'Identify disposal sites',
                    'Environmental compliance required',
                    'Cost documentation for reimbursement'
                ]
            },
            'B': {
                title: 'Category B - Emergency Protective Measures',
                requirements: [
                    'Demonstrate imminent threat to public health/safety',
                    'Document emergency nature of work',
                    'Show work eliminates or reduces threat',
                    'Provide cost estimates and documentation',
                    'Environmental review may be required'
                ]
            }
        };
        
        const info = categoryInfo[category];
        if (info) {
            detailsDiv.innerHTML = `
                <h4 class="mb-2">${info.title}</h4>
                <ul style="margin-left: 1rem;">
                    ${info.requirements.map(req => `<li class="mb-1">${req}</li>`).join('')}
                </ul>
            `;
        }
    } else {
        infoPanel.style.display = 'none';
    }
}

async function handleEmergencySubmit(event) {
    event.preventDefault();
    
    if (!validateForm('emergencyForm')) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }
    
    const submitBtn = event.target.querySelector('button[type="submit"]');
    setLoading(submitBtn, true);
    
    try {
        const formData = new FormData(event.target);
        const data = Object.fromEntries(formData.entries());
        
        const response = await fetch('/api/intake/emergency', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('Emergency work documentation generated successfully!', 'success');
            displayResults(result.documentation);
        } else {
            showNotification(result.message || 'Error generating documentation', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Network error occurred. Please try again.', 'error');
    } finally {
        setLoading(submitBtn, false);
    }
}

function displayResults(documentation) {
    const resultsPanel = document.getElementById('results-panel');
    const resultsContent = document.getElementById('results-content');
    
    resultsContent.innerHTML = `
        <div class="mb-3">
            <h4>Generated Documentation:</h4>
            <pre style="background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 6px; white-space: pre-wrap; font-size: 0.9rem;">
${documentation}
            </pre>
        </div>
        <div class="grid grid-2">
            <button class="btn" onclick="downloadDocumentation()">📥 Download PDF</button>
            <button class="btn" onclick="continueToWizard()">➡️ Continue to Wizard</button>
        </div>
    `;
    
    resultsPanel.style.display = 'block';
    resultsPanel.scrollIntoView({ behavior: 'smooth' });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} show`;
    notification.innerHTML = `
        ${message}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; cursor: pointer;">×</button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

function downloadDocumentation() {
    showNotification('PDF download feature coming soon!', 'info');
}

function continueToWizard() {
    window.location.href = '/wizard/emergency';
}
</script>
{% endblock %} 
-- ComplianceMax PostgreSQL Schema
-- Preserves complete conditional workflow logic and state tracking

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- PAPPG Versions enum for strict validation
CREATE TYPE pappg_version_enum AS ENUM (
  'PAPPG v1.0 (Jan 2016)',
  'PAPPG v2.0 (Apr 2017)', 
  'PAPPG v3.1 (May 2018)',
  'PAPPG v4.0 (June 2020)',
  'PAPPG v5.0 (Oct 2022)'
);

-- Applicant type enum
CREATE TYPE applicant_type_enum AS ENUM (
  'State Government',
  'Local Government', 
  'Tribal Government',
  'Private Nonprofit',
  'FEMA',
  'Recipient'
);

-- Core compliance steps with conditional logic
CREATE TABLE compliance_steps (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  step_id VARCHAR(50),
  process_phase TEXT NOT NULL,
  step_requirement TEXT NOT NULL,
  
  -- Core conditional logic (IF-THEN structure)
  trigger_condition_if TEXT NOT NULL,
  action_required_then TEXT NOT NULL,
  documentation_required TEXT,
  responsible_party TEXT NOT NULL,
  
  -- Regulatory framework
  pappg_version pappg_version_enum NOT NULL,
  applicable_regulations TEXT,
  cfr_reference TEXT,
  
  -- Document processing integration
  doctype_required TEXT,
  docling_tag TEXT,
  
  -- State tracking (preserves all checkbox logic)
  state_tracking JSONB NOT NULL DEFAULT '{
    "checklist_damageinventory": 0.0,
    "checklist_damagedescription": 0.0,
    "checklist_costing": 0.0,
    "checklist_invoices": 0.0,
    "checklist_mitigation": 0.0,
    "checklist_ehp": 0.0,
    "checklist_insurance": 0.0,
    "checklist_labordocs": 0.0,
    "checklist_contracts": 0.0,
    "checklist_debrisdocs": 0.0,
    "checklist_progressreports": 0.0,
    "checklist_closeout": 0.0,
    "executedcontract_checked": 0.0,
    "procurementprocedure_checked": 0.0,
    "solicitation_checked": 0.0,
    "bid_checked": 0.0,
    "evaluation_checked": 0.0,
    "invoice_checked": 0.0,
    "checkbox": 0.0,
    "doctyperequired_checkbox": false,
    "action_required_checkbox": false,
    "condition_checkbox": false
  }'::jsonb,
  
  -- Metadata
  grok_tag TEXT,
  notes TEXT,
  sourcefile TEXT,
  sheetname TEXT,
  sourcesheet TEXT,
  
  -- Incident date logic for PAPPG version determination
  incident_date_simulated DATE,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Projects table for tracking compliance workflows
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  disaster_number VARCHAR(50),
  incident_date DATE NOT NULL,
  
  -- Auto-determined PAPPG version based on incident date
  pappg_version pappg_version_enum NOT NULL,
  
  -- Project metadata
  applicant_type applicant_type_enum NOT NULL,
  status VARCHAR(50) DEFAULT 'active',
  
  -- Workflow state aggregation
  workflow_state JSONB NOT NULL DEFAULT '{
    "total_steps": 0,
    "completed_steps": 0,
    "progress_percentage": 0.0,
    "current_phase": "Phase 1: Declaration and Initial Eligibility",
    "next_required_actions": []
  }'::jsonb,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Project step instances (links projects to compliance steps with state)
CREATE TABLE project_compliance_steps (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  compliance_step_id UUID NOT NULL REFERENCES compliance_steps(id),
  
  -- Instance-specific state (overrides default)
  state_tracking JSONB NOT NULL,
  
  -- Conditional evaluation results
  condition_met BOOLEAN DEFAULT FALSE,
  action_completed BOOLEAN DEFAULT FALSE,
  documentation_submitted BOOLEAN DEFAULT FALSE,
  
  -- Assigned responsible party for this instance
  assigned_to VARCHAR(255),
  due_date DATE,
  completed_date DATE,
  
  -- Notes specific to this project instance
  project_notes TEXT,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(project_id, compliance_step_id)
);

-- Document processing table (Docling integration)
CREATE TABLE processed_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  compliance_step_id UUID REFERENCES compliance_steps(id),
  
  -- File information
  filename VARCHAR(500) NOT NULL,
  original_path TEXT,
  file_size INTEGER,
  file_type VARCHAR(100),
  
  -- Docling processing results
  extracted_text TEXT,
  markdown_content TEXT,
  docling_metadata JSONB,
  
  -- FEMA categorization
  fema_category VARCHAR(100),
  confidence_score DECIMAL(5,4),
  
  -- Document validation against requirements
  meets_requirements BOOLEAN DEFAULT FALSE,
  validation_notes TEXT,
  
  -- Processing metadata
  processing_time INTEGER, -- milliseconds
  processed_at TIMESTAMP DEFAULT NOW(),
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- PAPPG version determination function
CREATE OR REPLACE FUNCTION determine_pappg_version(incident_date DATE)
RETURNS pappg_version_enum AS $$
BEGIN
  CASE 
    WHEN incident_date >= '2022-10-01' THEN RETURN 'PAPPG v5.0 (Oct 2022)';
    WHEN incident_date >= '2020-06-01' THEN RETURN 'PAPPG v4.0 (June 2020)';
    WHEN incident_date >= '2018-05-01' THEN RETURN 'PAPPG v3.1 (May 2018)';
    WHEN incident_date >= '2017-04-01' THEN RETURN 'PAPPG v2.0 (Apr 2017)';
    ELSE RETURN 'PAPPG v1.0 (Jan 2016)';
  END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Trigger to auto-set PAPPG version on project creation
CREATE OR REPLACE FUNCTION set_project_pappg_version()
RETURNS TRIGGER AS $$
BEGIN
  NEW.pappg_version := determine_pappg_version(NEW.incident_date);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER project_pappg_version_trigger
  BEFORE INSERT OR UPDATE ON projects
  FOR EACH ROW
  EXECUTE FUNCTION set_project_pappg_version();

-- Function to evaluate conditional logic
CREATE OR REPLACE FUNCTION evaluate_condition(
  condition_text TEXT,
  project_context JSONB
) RETURNS BOOLEAN AS $$
BEGIN
  -- Simplified condition evaluation (can be expanded)
  RETURN TRUE; -- Placeholder for complex condition logic
END;
$$ LANGUAGE plpgsql;

-- Workflow progress calculation function
CREATE OR REPLACE FUNCTION calculate_project_progress(p_project_id UUID)
RETURNS JSONB AS $$
DECLARE
  total_steps INTEGER;
  completed_steps INTEGER;
  progress_pct DECIMAL(5,2);
  current_phase TEXT;
  result JSONB;
BEGIN
  -- Count total applicable steps for this project's PAPPG version
  SELECT COUNT(*) INTO total_steps
  FROM project_compliance_steps pcs
  JOIN compliance_steps cs ON pcs.compliance_step_id = cs.id
  JOIN projects p ON pcs.project_id = p.id
  WHERE pcs.project_id = p_project_id
    AND cs.pappg_version = (SELECT pappg_version FROM projects WHERE id = p_project_id);
  
  -- Count completed steps
  SELECT COUNT(*) INTO completed_steps
  FROM project_compliance_steps
  WHERE project_id = p_project_id
    AND action_completed = TRUE;
  
  -- Calculate percentage
  progress_pct := CASE 
    WHEN total_steps > 0 THEN (completed_steps::DECIMAL / total_steps::DECIMAL) * 100
    ELSE 0
  END;
  
  -- Determine current phase (simplified)
  SELECT process_phase INTO current_phase
  FROM compliance_steps cs
  JOIN project_compliance_steps pcs ON cs.id = pcs.compliance_step_id
  WHERE pcs.project_id = p_project_id
    AND pcs.action_completed = FALSE
  ORDER BY cs.id
  LIMIT 1;
  
  -- Build result
  result := jsonb_build_object(
    'total_steps', total_steps,
    'completed_steps', completed_steps,
    'progress_percentage', progress_pct,
    'current_phase', COALESCE(current_phase, 'Complete')
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Indexes for performance
CREATE INDEX idx_compliance_steps_pappg_version ON compliance_steps(pappg_version);
CREATE INDEX idx_compliance_steps_trigger_condition ON compliance_steps USING gin(to_tsvector('english', trigger_condition_if));
CREATE INDEX idx_compliance_steps_action_required ON compliance_steps USING gin(to_tsvector('english', action_required_then));
CREATE INDEX idx_compliance_steps_docling_tag ON compliance_steps(docling_tag) WHERE docling_tag IS NOT NULL;
CREATE INDEX idx_compliance_steps_state_tracking ON compliance_steps USING gin(state_tracking);

CREATE INDEX idx_projects_incident_date ON projects(incident_date);
CREATE INDEX idx_projects_pappg_version ON projects(pappg_version);
CREATE INDEX idx_projects_workflow_state ON projects USING gin(workflow_state);

CREATE INDEX idx_project_compliance_steps_project_id ON project_compliance_steps(project_id);
CREATE INDEX idx_project_compliance_steps_condition_met ON project_compliance_steps(condition_met, action_completed);

CREATE INDEX idx_processed_documents_project_id ON processed_documents(project_id);
CREATE INDEX idx_processed_documents_docling_metadata ON processed_documents USING gin(docling_metadata);
CREATE INDEX idx_processed_documents_fema_category ON processed_documents(fema_category);

-- Views for common queries
CREATE VIEW current_project_status AS
SELECT 
  p.id,
  p.name,
  p.disaster_number,
  p.incident_date,
  p.pappg_version,
  p.applicant_type,
  p.workflow_state,
  COUNT(pcs.id) as total_steps,
  COUNT(CASE WHEN pcs.action_completed THEN 1 END) as completed_steps,
  ROUND(
    (COUNT(CASE WHEN pcs.action_completed THEN 1 END)::DECIMAL / 
     NULLIF(COUNT(pcs.id), 0)::DECIMAL) * 100, 2
  ) as progress_percentage
FROM projects p
LEFT JOIN project_compliance_steps pcs ON p.id = pcs.project_id
GROUP BY p.id, p.name, p.disaster_number, p.incident_date, p.pappg_version, p.applicant_type, p.workflow_state;

-- Sample data insertion will be handled by migration script 

-- Directory structure for documents
DOCS/
├── PRODUCTION/           # Current working versions
├── INTEGRATION_READY/    # Policy documents ready for integration  
├── ARCHIVE/             # Historical/superseded versions
└── REFERENCE/           # Documentation and guides 